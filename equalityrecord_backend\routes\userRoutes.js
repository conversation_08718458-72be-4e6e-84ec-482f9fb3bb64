const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const UploadService = require("../../../services/UploadService");
const upload = UploadService.local_upload();

const {
  retrieveAll,
  getUserDetailsById,
  addOne,
  updateOne,
  deleteOne,
  uploadFile,
  uploadPhoto,
  getUserSubscription,
  getAllClientByMemberId,
  getAllClients,
  assignClientToMembersByClientUserId,
  getAllMembersByManagerId,
  getAllClientsProject,
  createManagerWithPermissions,
  addMemberToCompany,
  getCompanyInfo,
  createCompanyMemberWithPayment,
  confirmCompanyMemberPayment,
  createInvoiceSubscriptionPayment,
  confirmInvoiceSubscriptionPayment,
  fetchAllLegalDocuments,
  getUserClients
} = require("../controllers/userController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const middlewares2 = [ProjectMiddleware, UrlMiddleware, HostMiddleware];
const imageMiddlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware(), upload.single("file")];

// Key: user
router.post("/retrieve", middlewares, retrieveAll);
router.get("/view/:id", middlewares, getUserDetailsById);
router.get("/view/clients/:id", middlewares, getAllClientByMemberId);
router.get("/view/all/client", middlewares, getAllClients);
router.get("/view/my/clients", middlewares, getUserClients);
router.get("/view/all/client/project", middlewares, getAllClientsProject);
router.get("/manager/all_members/:id", middlewares, getAllMembersByManagerId);
router.get("/company/info", middlewares, getCompanyInfo);
router.put("/client/assign_to_member/:id", middlewares, assignClientToMembersByClientUserId);
router.get("/subscription", middlewares, getUserSubscription);
router.post("/add", middlewares, addOne);
router.post("/manager/create", middlewares, createManagerWithPermissions);
router.post("/manager/add_member", middlewares, addMemberToCompany);
router.post("/company/member/create", middlewares, createCompanyMemberWithPayment);
router.post("/company/member/payment/confirm/:id", middlewares, confirmCompanyMemberPayment);
router.post("/invoice-subscription/create", middlewares, createInvoiceSubscriptionPayment);
router.post("/invoice-subscription/confirm/:id", middlewares, confirmInvoiceSubscriptionPayment);
router.put("/:id", middlewares, updateOne);
router.delete("/:id", middlewares, deleteOne);

router.post("/upload/file", imageMiddlewares, uploadFile);
router.put("/photo/:id", middlewares, uploadPhoto);
router.get("/legal-documents", middlewares2, fetchAllLegalDocuments);

module.exports = router;
