const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
const { Parser } = require("json2csv");
const csv = require("csv-parser");
const JSZip = require("jszip");
const fs = require("fs");
const path = require("path");
const PDFDocument = require("pdf-lib").PDFDocument;
const axios = require("axios");
const aws = require("aws-sdk");
const config = require("../../../config");

async function createLyricsPDF(lyrics) {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage();
  const { height, width } = page.getSize();

  page.drawText(lyrics || "No lyrics available", {
    x: 50,
    y: height - 50,
    size: 12
  });

  return await pdfDoc.save();
}

// Helper function to download file content
async function downloadFile(url) {
  try {
    const response = await axios.get(url, { responseType: "arraybuffer" });
    return response.data;
  } catch (error) {
    console.error(`Error downloading file from ${url}:`, error);
    return null;
  }
}

module.exports = class ProjectService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT
          p.*,
          c.program AS program_name, c.name AS program_owner_name, c.position AS program_owner_position
        FROM equalityrecord_project p
        INNER JOIN equalityrecord_client c ON p.client_id = c.id
        WHERE p.user_id = ${req.user_id}
        ORDER BY p.team_name ASC;
      `);

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllProjectsForClient(req) {
    try {
      this.sdk = this.getSDK(req);

      this.sdk.setTable("user");
      let userExists = await this.sdk.get({ id: req.user_id });

      if (userExists.length === 0) {
        return {
          error: true,
          message: "Client not found",
          list: []
        };
      }

      const result = await this.sdk.rawQuery(`
        SELECT
          p.*,
          c.program AS program_name, c.name AS program_owner_name, c.position AS program_owner_position
        FROM equalityrecord_project p
        LEFT JOIN equalityrecord_client c ON p.client_id = c.id
        WHERE p.client_id = ${userExists[0].client_id}
        ORDER BY p.team_name ASC;
      `);

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   client_id: 1, // aka program
      //   team_name: "Team 1",
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      //   mix_type_id: 1,
      //   team_type: 1, // 1 = All Girls, 2 = Coed
      // };

      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE p.user_id = ${req.user_id}`;
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` p.user_id = ${filter.user_id} `);
        }
        if (filter.client_id) {
          filterArr.push(` p.client_id = ${filter.client_id} `);
        }
        if (filter.team_name) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_name.toLowerCase()}%' `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.mix_type_id) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_id}`);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }
        if (filter.team_details_date) {
          filterArr.push(` p.team_details_date = '${filter.team_details_date}' `);
        }
        if (filter.routine_submission_date) {
          filterArr.push(` p.routine_submission_date = '${filter.routine_submission_date}' `);
        }
        if (filter.estimated_delivery_date) {
          filterArr.push(` p.estimated_delivery_date = '${filter.estimated_delivery_date}' `);
        }
        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      // we may not get subproject employee cost
      // so we need to calculate it manually

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        // iterate result to calculate total from subproject_employee
        for (let i = 0; i < result.length; i++) {
          let subProjectRawSql = `
          SELECT s.*, COALESCE(se.total_employee_cost, 0) AS expenses
          FROM equalityrecord_subproject s
          LEFT JOIN (
            SELECT se.subproject_id, SUM(COALESCE(se.employee_cost, 0)) AS total_employee_cost
            FROM equalityrecord_subproject_employee se
            GROUP BY se.subproject_id
          ) se ON s.id = se.subproject_id
          WHERE s.project_id = ${result[i].id}
          ORDER BY s.id ASC
        `;

          let subProjectResult = await this.sdk.rawQuery(subProjectRawSql);
          result[i].subprojects = subProjectResult ?? [];

          // calculate total from subproject
          let total = 0;
          if (subProjectResult.length > 0) {
            for (let j = 0; j < subProjectResult.length; j++) {
              total += subProjectResult[j].expenses;
            }
          }

          result[i].expenses = total;
        }

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllForAdmin(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   id: 1, // aka project_id
      //   client_id: 1, // aka program
      //   team_name: "Team 1",
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      //   mix_type_id: 1,
      //   team_type: 1, // 1 = All Girls, 2 = Coed
      // };

      let filterQuery = "";
      if (req.role !== "admin") {
        return {
          error: true,
          message: "You are not allowed to access this feature"
        };
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_ids && filter.user_ids.length === 1) {
          filterArr.push(` p.user_id = ${filter.user_ids[0]} `);
        } else if (filter.user_ids && filter.user_ids.length > 1) {
          filterArr.push(` p.user_id IN (${filter.user_ids.join(",")}) `);
        }
        if (filter.client_ids && filter.client_ids.length === 1) {
          filterArr.push(` p.client_id = ${filter.client_ids[0]} `);
        } else if (filter.client_ids && filter.client_ids.length > 1) {
          filterArr.push(` p.client_id IN (${filter.client_ids.join(",")}) `);
        }
        if (filter.project_id) {
          filterArr.push(` p.id = ${filter.project_id} `);
        }
        if (filter.team_names && filter.team_names.length === 1) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_names[0].toLowerCase()}%' `);
        } else if (filter.team_names && filter.team_names.length > 1) {
          filterArr.push(` LOWER(p.team_name) IN (${filter.team_names.map((team_name) => `'${team_name.toLowerCase()}'`).join(",")}) `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.mix_type_id) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_id}`);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }
        if (filter.team_details_date) {
          filterArr.push(` p.team_details_date = '${filter.team_details_date}' `);
        }
        if (filter.routine_submission_date) {
          filterArr.push(` p.routine_submission_date = '${filter.routine_submission_date}' `);
        }
        if (filter.estimated_delivery_date) {
          filterArr.push(` p.estimated_delivery_date = '${filter.estimated_delivery_date}' `);
        }
        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }

        if (filter.payment_status && filter.payment_status_without_completed) {
          return {
            error: true,
            message: "Cannot use payment_status and payment_status_without_completed together",
            list: []
          };
        }

        if (filter.payment_status_without_completed) {
          filterArr.push(` p.payment_status IN (2, 3, 4, 5) `);
        }

        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status,
        CONCAT(u.first_name, ' ', u.last_name) AS user_name,
        u.subscription
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      // we may not get subproject employee cost
      // so we need to calculate it manually

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        // iterate result to calculate total from subproject_employee
        for (let i = 0; i < result.length; i++) {
          let subProjectRawSql = `
          SELECT s.*, COALESCE(se.total_employee_cost, 0) AS expenses
          FROM equalityrecord_subproject s
          LEFT JOIN (
            SELECT se.subproject_id, SUM(COALESCE(se.employee_cost, 0)) AS total_employee_cost
            FROM equalityrecord_subproject_employee se
            GROUP BY se.subproject_id
          ) se ON s.id = se.subproject_id
          WHERE s.project_id = ${result[i].id}
          ORDER BY s.id ASC
        `;

          let subProjectResult = await this.sdk.rawQuery(subProjectRawSql);
          result[i].subprojects = subProjectResult ?? [];

          // calculate total from subproject
          let total = 0;
          if (subProjectResult.length > 0) {
            for (let j = 0; j < subProjectResult.length; j++) {
              total += subProjectResult[j].expenses;
            }
          }

          result[i].expenses = total;

          // team_details
          let teamDetailsRawSql = `
            SELECT *
            FROM equalityrecord_team_details
            WHERE project_id = ${result[i].id}
          `;

          let teamDetailsResult = await this.sdk.rawQuery(teamDetailsRawSql);

          if (teamDetailsResult.length > 0) {
            result[i].team_details = teamDetailsResult;
          } else {
            result[i].team_details = null;
          }

          // eight_count
          let eightCountRawSql = `
            SELECT *
            FROM equalityrecord_eight_count
            WHERE project_id = ${result[i].id}
          `;
          let eightCountResult = await this.sdk.rawQuery(eightCountRawSql);

          if (eightCountResult.length > 0) {
            result[i].eight_count = eightCountResult;
          } else {
            result[i].eight_count = null;
          }

          // media
          let mediaRawSql = `
            SELECT *
            FROM equalityrecord_media
            WHERE project_id = ${result[i].id}
          `;
          let mediaResult = await this.sdk.rawQuery(mediaRawSql);

          if (mediaResult.length > 0) {
            result[i].media_found = true;
            // now detect is_music
            let is_music = mediaResult.find((item) => item.is_music === 1);
            if (is_music) {
              result[i].is_music_found = true;
            } else {
              result[i].is_music_found = false;
            }
          } else {
            result[i].media_found = false;
            result[i].is_music_found = false;
          }

          // company_info
          let companyInfoRawSql = `
            SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
              p.company_name, p.office_email, p.company_logo, p.license_company_logo,
              CASE 
                WHEN mp.member_ids IS NOT NULL 
                THEN JSON_EXTRACT(mp.member_ids, '$[0]')
                ELSE NULL 
              END AS main_member_id
            FROM equalityrecord_user u
            LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
            LEFT JOIN equalityrecord_manager_permission mp ON (JSON_CONTAINS(mp.member_ids, CAST(u.id AS CHAR)) OR mp.manager_id = u.id)
            WHERE u.id = ${result[i].user_id}
          `;

          let companyInfoResult = await this.sdk.rawQuery(companyInfoRawSql);

          if (companyInfoResult.length > 0) {
            result[i].company_info = companyInfoResult[0];
          } else {
            result[i].company_info = null;
          }
        }

        let completedProjectsSql = ``;
        if (filter.user_ids && filter.user_ids.length === 1) {
          completedProjectsSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE payment_status = 1
            AND user_id = ${filter.user_ids[0]}
          `;
        } else if (filter.user_ids && filter.user_ids.length > 1) {
          completedProjectsSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE payment_status = 1
            AND user_id IN (${filter.user_ids.join(",")})
          `;
        } else {
          completedProjectsSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE payment_status = 1
          `;
        }

        const completedProjects = await this.sdk.rawQuery(completedProjectsSql);

        let totalCountSql = ``;
        if (filter.user_ids && filter.user_ids.length === 1) {
          totalCountSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE user_id = ${filter.user_ids[0]}
          `;
        } else if (filter.user_ids && filter.user_ids.length > 1) {
          totalCountSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE user_id IN (${filter.user_ids.join(",")})
          `;
        } else {
          totalCountSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
          `;
        }

        const totalCount = await this.sdk.rawQuery(totalCountSql);

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
          total_completed_projects: completedProjects.length > 0 ? completedProjects[0].total : 0,
          total_count: totalCount.length > 0 ? totalCount[0].total : 0
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
          total_completed_projects: 0,
          total_count: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllForProjectCalendarForAdmin(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   project_id: 1,
      //   user_id: 1, // aka member_id
      //   client_id: 1,
      //   team_name: "Team 1",
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      // };

      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE p.user_id = ${req.user_id}`;
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` p.user_id = ${filter.user_id} `);
        }
        if (filter.client_id) {
          filterArr.push(` p.client_id = ${filter.client_id} `);
        }
        if (filter.project_id) {
          filterArr.push(` p.id = ${filter.project_id} `);
        }
        if (filter.team_name) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_name.toLowerCase()}%' `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.mix_type_id) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_id}`);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }
        if (filter.team_details_date) {
          filterArr.push(` p.team_details_date = '${filter.team_details_date}' `);
        }
        if (filter.routine_submission_date) {
          filterArr.push(` p.routine_submission_date = '${filter.routine_submission_date}' `);
        }
        if (filter.estimated_delivery_date) {
          filterArr.push(` p.estimated_delivery_date = '${filter.estimated_delivery_date}' `);
        }
        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        CONCAT(u.first_name, ' ', u.last_name) AS user_name
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllForProjectCalendarForManager(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   project_id: 1,
      //   user_id: 1, // aka member_id
      //   client_id: 1,
      //   team_name: "Team 1",
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      // };

      let filterQuery = "";
      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not allowed to access this feature"
        };
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "manager") {
          filterArr.push(` p.user_id = ${filter.user_id} `);
        }
        if (filter.client_id) {
          filterArr.push(` p.client_id = ${filter.client_id} `);
        }
        if (filter.project_id) {
          filterArr.push(` p.id = ${filter.project_id} `);
        }
        if (filter.team_name) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_name.toLowerCase()}%' `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.mix_type_id) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_id}`);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }
        if (filter.team_details_date) {
          filterArr.push(` p.team_details_date = '${filter.team_details_date}' `);
        }
        if (filter.routine_submission_date) {
          filterArr.push(` p.routine_submission_date = '${filter.routine_submission_date}' `);
        }
        if (filter.estimated_delivery_date) {
          filterArr.push(` p.estimated_delivery_date = '${filter.estimated_delivery_date}' `);
        }
        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        CONCAT(u.first_name, ' ', u.last_name) AS user_name
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllMultiFilter(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   client_ids: [6, 2, 8], // aka program
      //   team_names: ["Team name 1", "Team name 2"]
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      //   mix_type_ids: [3, 7, 1],
      //   team_type: 1, // 1 = All Girls, 2 = Coed
      // };

      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE p.user_id = ${req.user_id}`;
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` p.user_id = ${filter.user_id} `);
        }
        if (filter.client_ids && filter.client_ids.length === 1) {
          filterArr.push(` p.client_id = ${filter.client_ids[0]} `);
        } else if (filter.client_ids && filter.client_ids.length > 1) {
          filterArr.push(` p.client_id IN (${filter.client_ids.join(",")}) `);
        }
        if (filter.team_names && filter.team_names.length === 1) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_names[0].toLowerCase()}%' `);
        } else if (filter.team_names && filter.team_names.length > 1) {
          filterArr.push(` LOWER(p.team_name) IN (${filter.team_names.map((team_name) => `'${team_name.toLowerCase()}'`).join(",")}) `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.mix_type_ids && filter.mix_type_ids.length === 1) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_ids[0]} `);
        } else if (filter.mix_type_ids && filter.mix_type_ids.length > 1) {
          filterArr.push(` p.mix_type_id IN (${filter.mix_type_ids.join(",")}) `);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }

        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }

        // payment_status_without_completed = 1, means payment_status = 2, 3, 4, 5
        // payment_status_all = 1, means no need to filter payment_status

        if (filter.payment_status && filter.payment_status_without_completed) {
          return {
            error: true,
            message: "Cannot use payment_status and payment_status_without_completed together",
            list: []
          };
        }

        if (filter.payment_status_without_completed) {
          filterArr.push(` p.payment_status IN (2, 3, 4, 5) `);
        }

        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status,
        s.lock_date AS survey_lock_date,
        s.create_at AS survey_created_at,
        u.subscription
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      // we may not get subproject employee cost
      // so we need to calculate it manually

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        // iterate result to calculate total from subproject_employee
        for (let i = 0; i < result.length; i++) {
          let subProjectRawSql = `
            SELECT s.*, COALESCE(se.total_employee_cost, 0) AS expenses,
              wo.status AS work_order_status,
              wo.writer_submit_status, wo.artist_submit_status, wo.engineer_submit_status
            FROM equalityrecord_subproject s
            LEFT JOIN (
              SELECT se.subproject_id, SUM(COALESCE(se.employee_cost, 0)) AS total_employee_cost
              FROM equalityrecord_subproject_employee se
              GROUP BY se.subproject_id
            ) se ON s.id = se.subproject_id
            LEFT JOIN
              equalityrecord_work_order wo ON s.workorder_id = wo.id
            WHERE s.project_id = ${result[i].id}
            ORDER BY s.id ASC
          `;

          let subProjectResult = await this.sdk.rawQuery(subProjectRawSql);
          result[i].subprojects = subProjectResult ?? [];

          // calculate total from subproject
          let total = 0;
          if (subProjectResult.length > 0) {
            for (let j = 0; j < subProjectResult.length; j++) {
              total += subProjectResult[j].expenses;
            }
          }

          result[i].expenses = total;

          // team_details
          let teamDetailsRawSql = `
            SELECT *
            FROM equalityrecord_team_details
            WHERE project_id = ${result[i].id}
          `;

          let teamDetailsResult = await this.sdk.rawQuery(teamDetailsRawSql);

          if (teamDetailsResult.length > 0) {
            result[i].team_details = teamDetailsResult;
          } else {
            result[i].team_details = null;
          }

          // eight_count
          let eightCountRawSql = `
            SELECT *
            FROM equalityrecord_eight_count
            WHERE project_id = ${result[i].id}
          `;
          let eightCountResult = await this.sdk.rawQuery(eightCountRawSql);

          if (eightCountResult.length > 0) {
            result[i].eight_count = eightCountResult;
          } else {
            result[i].eight_count = null;
          }

          // media
          let mediaRawSql = `
            SELECT *
            FROM equalityrecord_media
            WHERE project_id = ${result[i].id}
          `;
          let mediaResult = await this.sdk.rawQuery(mediaRawSql);

          if (mediaResult.length > 0) {
            result[i].medias = mediaResult;
          } else {
            result[i].medias = null;
          }

          // company_info
          let companyInfoRawSql = `
            SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
              p.company_name, p.office_email, p.company_logo, p.license_company_logo,
              CASE 
                WHEN mp.member_ids IS NOT NULL 
                THEN JSON_EXTRACT(mp.member_ids, '$[0]')
                ELSE NULL 
              END AS main_member_id
            FROM equalityrecord_user u
            LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
            LEFT JOIN equalityrecord_manager_permission mp ON (JSON_CONTAINS(mp.member_ids, CAST(u.id AS CHAR)) OR mp.manager_id = u.id)
            WHERE u.id = ${result[i].user_id}
          `;

          let companyInfoResult = await this.sdk.rawQuery(companyInfoRawSql);

          if (companyInfoResult.length > 0) {
            result[i].company_info = companyInfoResult[0];
          } else {
            result[i].company_info = null;
          }
        }

        const completedProjects = await this.sdk.rawQuery(`
          SELECT COUNT(*) AS total
          FROM equalityrecord_project
          WHERE payment_status = 1
          AND user_id = ${req.user_id}
        `);

        const totalCount = await this.sdk.rawQuery(`
          SELECT COUNT(*) AS total
          FROM equalityrecord_project
          WHERE user_id = ${req.user_id}
        `);

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
          total_completed_projects: completedProjects.length > 0 ? completedProjects[0].total : 0,
          total_count: totalCount.length > 0 ? totalCount[0].total : 0
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
          total_completed_projects: 0,
          total_count: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllMultiFilterForManager(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   member_ids: [6, 2, 8], // aka program
      //   team_names: ["Team name 1", "Team name 2"]
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      //   mix_type_ids: [3, 7, 1],
      //   team_type: 1, // 1 = All Girls, 2 = Coed
      // };

      let filterQuery = "";
      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not allowed to access this feature"
        };
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        // TODO: first, second
        if (filter.member_ids && filter.member_ids.length === 1 && req.role === "manager") {
          filterArr.push(` p.user_id = ${filter.member_ids[0]} `);
        } else if (filter.member_ids && filter.member_ids.length > 1 && req.role === "manager") {
          filterArr.push(` p.user_id IN (${filter.member_ids.join(",")}) `);
        }
        if (filter.client_ids && filter.client_ids.length === 1) {
          filterArr.push(` p.client_id = ${filter.client_ids[0]} `);
        } else if (filter.client_ids && filter.client_ids.length > 1) {
          filterArr.push(` p.client_id IN (${filter.client_ids.join(",")}) `);
        }
        if (filter.team_names && filter.team_names.length === 1) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_names[0].toLowerCase()}%' `);
        } else if (filter.team_names && filter.team_names.length > 1) {
          filterArr.push(` LOWER(p.team_name) IN (${filter.team_names.map((team_name) => `'${team_name.toLowerCase()}'`).join(",")}) `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.mix_type_ids && filter.mix_type_ids.length === 1) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_ids[0]} `);
        } else if (filter.mix_type_ids && filter.mix_type_ids.length > 1) {
          filterArr.push(` p.mix_type_id IN (${filter.mix_type_ids.join(",")}) `);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }

        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }

        // payment_status_without_completed = 1, means payment_status = 2, 3, 4, 5
        // payment_status_all = 1, means no need to filter payment_status

        if (filter.payment_status && filter.payment_status_without_completed) {
          return {
            error: true,
            message: "Cannot use payment_status and payment_status_without_completed together",
            list: []
          };
        }

        if (filter.payment_status_without_completed) {
          filterArr.push(` p.payment_status IN (2, 3, 4, 5) `);
        }

        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status,
        s.lock_date AS survey_lock_date,
        s.create_at AS survey_created_at,
        u.subscription
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      // we may not get subproject employee cost
      // so we need to calculate it manually

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        // iterate result to calculate total from subproject_employee
        for (let i = 0; i < result.length; i++) {
          let subProjectRawSql = `
            SELECT s.*, COALESCE(se.total_employee_cost, 0) AS expenses,
              wo.status AS work_order_status,
              wo.writer_submit_status, wo.artist_submit_status, wo.engineer_submit_status
            FROM equalityrecord_subproject s
            LEFT JOIN (
              SELECT se.subproject_id, SUM(COALESCE(se.employee_cost, 0)) AS total_employee_cost
              FROM equalityrecord_subproject_employee se
              GROUP BY se.subproject_id
            ) se ON s.id = se.subproject_id
            LEFT JOIN
              equalityrecord_work_order wo ON s.workorder_id = wo.id
            WHERE s.project_id = ${result[i].id}
            ORDER BY s.id ASC
          `;

          let subProjectResult = await this.sdk.rawQuery(subProjectRawSql);
          result[i].subprojects = subProjectResult ?? [];

          // calculate total from subproject
          let total = 0;
          if (subProjectResult.length > 0) {
            for (let j = 0; j < subProjectResult.length; j++) {
              total += subProjectResult[j].expenses;
            }
          }

          result[i].expenses = total;

          // team_details
          let teamDetailsRawSql = `
            SELECT *
            FROM equalityrecord_team_details
            WHERE project_id = ${result[i].id}
          `;

          let teamDetailsResult = await this.sdk.rawQuery(teamDetailsRawSql);

          if (teamDetailsResult.length > 0) {
            result[i].team_details_found = true;
          } else {
            result[i].team_details_found = false;
          }

          // eight_count
          let eightCountRawSql = `
            SELECT *
            FROM equalityrecord_eight_count
            WHERE project_id = ${result[i].id}
          `;
          let eightCountResult = await this.sdk.rawQuery(eightCountRawSql);

          if (eightCountResult.length > 0) {
            result[i].eight_count = eightCountResult;
          } else {
            result[i].eight_count = null;
          }

          // media
          let mediaRawSql = `
            SELECT *
            FROM equalityrecord_media
            WHERE project_id = ${result[i].id}
          `;
          let mediaResult = await this.sdk.rawQuery(mediaRawSql);

          if (mediaResult.length > 0) {
            result[i].media_found = true;
            // now detect is_music
            let is_music = mediaResult.find((item) => item.is_music === 1);
            if (is_music) {
              result[i].is_music_found = true;
            } else {
              result[i].is_music_found = false;
            }
          } else {
            result[i].media_found = false;
            result[i].is_music_found = false;
          }

          // company_info
          let companyInfoRawSql = `
            SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
              p.company_name, p.office_email, p.company_logo, p.license_company_logo,
              CASE 
                WHEN mp.member_ids IS NOT NULL 
                THEN JSON_EXTRACT(mp.member_ids, '$[0]')
                ELSE NULL 
              END AS main_member_id
            FROM equalityrecord_user u
            LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
            LEFT JOIN equalityrecord_manager_permission mp ON (JSON_CONTAINS(mp.member_ids, CAST(u.id AS CHAR)) OR mp.manager_id = u.id)
            WHERE u.id = ${result[i].user_id}
          `;

          let companyInfoResult = await this.sdk.rawQuery(companyInfoRawSql);

          if (companyInfoResult.length > 0) {
            result[i].company_info = companyInfoResult[0];
          } else {
            result[i].company_info = null;
          }
        }

        let completedProjectsSql = ``;
        if (filter.member_ids && filter.member_ids.length === 1) {
          completedProjectsSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE payment_status = 1
            AND user_id = ${filter.member_ids[0]}
          `;
        } else if (filter.member_ids && filter.member_ids.length > 1) {
          completedProjectsSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE payment_status = 1
            AND user_id IN (${filter.member_ids.join(",")})
          `;
        } else {
          completedProjectsSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE payment_status = 1
          `;
        }

        const completedProjects = await this.sdk.rawQuery(completedProjectsSql);

        let totalCountSql = ``;
        if (filter.member_ids && filter.member_ids.length === 1) {
          totalCountSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE user_id = ${filter.member_ids[0]}
          `;
        } else if (filter.member_ids && filter.member_ids.length > 1) {
          totalCountSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
            WHERE user_id IN (${filter.member_ids.join(",")})
          `;
        } else {
          totalCountSql = `
            SELECT COUNT(*) AS total
            FROM equalityrecord_project
          `;
        }

        const totalCount = await this.sdk.rawQuery(totalCountSql);

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
          total_completed_projects: completedProjects.length > 0 ? completedProjects[0].total : 0,
          total_count: totalCount.length > 0 ? totalCount[0].total : 0
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
          total_completed_projects: 0,
          total_count: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is a main member or manager
      // Only main member or manager can create projects
      if (req.role !== "admin") {
        // Check if the user is a manager
        const isManager = req.role === "manager";

        // If not a manager, check if they are a main member (part of a company but the first member)
        let isMainMember = false;

        if (!isManager && req.role === "member") {
          // Check if user is part of a company
          const companyInfo = await this.sdk.rawQuery(`
            SELECT mp.* 
            FROM equalityrecord_manager_permission mp
            WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
          `);

          if (companyInfo.length > 0) {
            // User is part of a company, check if they are the first member
            const memberIds = JSON.parse(companyInfo[0].member_ids);
            // Main member is typically the first member in the company
            isMainMember = memberIds[0] == req.user_id;
          }
        }

        // If neither manager nor main member, reject project creation
        // if (!isManager && !isMainMember) {
        //   return {
        //     error: true,
        //     message: "Only main member or manager can create projects",
        //     project_id: null,
        //     survey_id: null
        //   };
        // }
      }

      this.sdk.setTable("project");

      const result = await this.sdk.insert({
        user_id: req.body.user_id,
        client_id: Number(req.body.client_id),
        mix_season_id: Number(req.body.mix_season_id),
        team_name: req.body.team_name,
        mix_date: req.body.mix_date,
        mix_type_id: Number(req.body.mix_type_id),
        team_type: req.body.team_type ?? 0,
        division: req.body.division ?? null,
        colors: req.body.colors,
        discount: Number(req.body.discount),
        content_status: "Pending",
        is_song_project: req.body.is_song_project ?? 0,
        team_details_date: req.body.team_details_date,
        routine_submission_date: req.body.routine_submission_date,
        estimated_delivery_date: req.body.estimated_delivery_date,
        payment_status: req.body.payment_status ? Number(req.body.payment_status) : 0,
        invoice_item_id: req.body.invoice_item_id ?? null,
        invoice_id: req.body.invoice_id ?? null,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      if (!result) {
        return {
          error: true,
          message: "Failed to add project",
          project_id: null,
          survey_id: null
        };
      }

      let voiceOverEightCount = 0;
      let songEightCount = 0;
      let trackingEightCount = 0;

      const survey_id = await this.addSurvey(req, result);
      const team_details_id = await this.addTeamDetails(req, result);

      // create sub project
      this.sdk.setTable("mix_type");
      const mixType = await this.sdk.get({
        id: Number(req.body.mix_type_id)
      });

      if (mixType[0].sub_projects && JSON.parse(mixType[0].sub_projects).length > 0) {
        let subProjectsArr = JSON.parse(mixType[0].sub_projects);
        // [
        //  {
        //    type: 'voiceover',
        //    eight_count: 3,
        //    quantity: 2,
        //  },
        //  {
        //    type: 'song',
        //    eight_count: 2,
        //    quantity: 1,
        //  },
        //  {
        //    type: 'tracking',
        //    eight_count: 3,
        //    quantity: 1,
        //  },
        // ];

        let voiceovers = subProjectsArr.filter((subProject) => subProject.type === "voiceover");

        let songs = subProjectsArr.filter((subProject) => subProject.type === "song");

        let trackings = subProjectsArr.filter((subProject) => subProject.type === "tracking");

        let voiceoverCount = 0;
        let songCount = 0;
        let trackingCount = 0;

        if (voiceovers.length > 0) {
          for (let i = 0; i < voiceovers.length; i++) {
            if (voiceovers[i].quantity > 0) {
              for (let j = 0; j < voiceovers[i].quantity; j++) {
                voiceoverCount++;
                voiceOverEightCount = voiceovers[i].eight_count;

                this.sdk.rawQuery(`INSERT INTO equalityrecord_subproject (type, type_name, project_id, eight_count, status, is_song, create_at, update_at)
                              VALUES ('Voiceover ${voiceoverCount}', 'Voiceover ${voiceoverCount}', ${Number(result)}, ${
                  voiceovers[i].eight_count
                }, 0, 0, '${sqlDateFormat(new Date())}', '${sqlDateTimeFormat(new Date())}')`);
              }
            }
          }
        }

        if (songs.length > 0) {
          for (let i = 0; i < songs.length; i++) {
            if (songs[i].quantity > 0) {
              for (let j = 0; j < songs[i].quantity; j++) {
                songCount++;
                songEightCount = songs[i].eight_count;

                this.sdk.rawQuery(`INSERT INTO equalityrecord_subproject (type, type_name, project_id, eight_count, status, is_song, create_at, update_at)
                              VALUES ('Song ${songCount}', 'Song ${songCount}', ${Number(result)}, ${songs[i].eight_count}, 0, 1, '${sqlDateFormat(
                  new Date()
                )}', '${sqlDateTimeFormat(new Date())}')`);
              }
            }
          }
        }

        if (trackings.length > 0) {
          for (let i = 0; i < trackings.length; i++) {
            if (trackings[i].quantity > 0) {
              for (let j = 0; j < trackings[i].quantity; j++) {
                trackingCount++;
                trackingEightCount = trackings[i].eight_count;

                this.sdk.rawQuery(`INSERT INTO equalityrecord_subproject (type, type_name, project_id, eight_count, status, is_song, create_at, update_at)
                              VALUES ('Tracking ${trackingCount}', 'Tracking ${trackingCount}', ${Number(result)}, ${
                  trackings[i].eight_count
                }, 0, 0, '${sqlDateFormat(new Date())}', '${sqlDateTimeFormat(new Date())}')`);
              }
            }
          }
        }
      }

      return {
        error: false,
        message: "Project added successfully",
        project_id: result,
        survey_id: survey_id
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is a main member or manager
      // Only main member or manager can update projects
      if (req.role !== "admin") {
        // Check if the user is a manager
        const isManager = req.role === "manager";

        // If not a manager, check if they are a main member (part of a company but the first member)
        let isMainMember = false;

        if (!isManager && req.role === "member") {
          // Check if user is part of a company
          const companyInfo = await this.sdk.rawQuery(`
            SELECT mp.* 
            FROM equalityrecord_manager_permission mp
            WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
          `);

          if (companyInfo.length > 0) {
            // User is part of a company, check if they are the first member
            const memberIds = JSON.parse(companyInfo[0].member_ids);
            // Main member is typically the first member in the company
            isMainMember = memberIds[0] == req.user_id;
          }
        }

        // If neither manager nor main member, reject project update
        // if (!isManager && !isMainMember) {
        //   return {
        //     error: true,
        //     message: "Only main member or manager can update projects"
        //   };
        // }
      }

      this.sdk.setTable("project");

      await this.sdk.update(
        filterEmptyFields({
          client_id: req.body.client_id,
          mix_season_id: req.body.mix_season_id,
          mix_date: req.body.mix_date,
          mix_type_id: req.body.mix_type_id,
          team_name: req.body.team_name,
          team_type: req.body.team_type,
          division: req.body.division,
          discount: Number(req.body.discount),
          colors: req.body.colors,
          content_status: req.body.content_status,
          is_song_project: req.body.is_song_project ?? 0,
          team_details_date: req.body.team_details_date,
          routine_submission_date: req.body.routine_submission_date,
          estimated_delivery_date: req.body.estimated_delivery_date,
          payment_status: req.body.payment_status ? Number(req.body.payment_status) : 0,
          update_at: sqlDateTimeFormat(new Date())
        }),
        Number(req.params.id)
      );

      return {
        error: false,
        message: "Project updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSubProjectDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      const subproject = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject
        WHERE id = ${Number(req.body.subproject_id)}
      `);

      if (!subproject) throw new Error("Sub-project not found");

      let other_subproject = [];
      if (subproject[0].parent_subproject_id) {
        other_subproject = await this.sdk.get({
          parent_subproject_id: subproject[0].parent_subproject_id
        });
      }

      let subproject_ids = [Number(req.body.subproject_id), ...other_subproject.map((subproject) => subproject.id)];

      for (const subproject_id of subproject_ids) {
        let subproject_columns = [
          "type",
          "type_name",
          "bpm",
          "genre",
          "song_type",
          "song_key",
          "is_file",
          "lyrics",
          "file_cost",
          "assigned",
          "notes",
          "gender"
        ];

        let subproject_data = {};
        for (const key in req.body) {
          if (subproject_columns.includes(key)) {
            subproject_data[key] = req.body[key];
          }
        }

        this.sdk.setTable("subproject");
        // if request body is part of subproject table, update it
        await this.sdk.update(
          filterEmptyFields({
            ...subproject_data,
            update_at: sqlDateTimeFormat(new Date())
          }),
          subproject_id
        );

        this.sdk.setTable("project_file");
        if (req.body.files && req.body.files.length > 0) {
          for (let i = 0; i < req.body.files.length; i++) {
            const payload = {
              project_id: req.body.project_id,
              subproject_id: subproject_id,
              employee_type: "engineer",
              type: "master",
              url: req.body.files[i],
              is_from_admin: 1,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            };

            const result = await this.sdk.insert(payload);
          }
        }
      }

      // find parent_subproject_id with same subproject id and update it as well

      return {
        error: false,
        message: "Project updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async addSubProject(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      const result = await this.sdk.insert({
        type: req.body.type_name,
        type_name: req.body.type_name,
        project_id: Number(req.body.project_id),
        eight_count: Number(req.body.eight_count),
        is_file: req.body.is_file ?? 0,
        lyrics: req.body.lyrics || "",
        file_cost: req.body.file_cost || 0,
        files: req.body.files || "",
        status: 0,
        is_song: req.body.is_song ?? 0,
        create_at: sqlDateFormat(new Date()),
        gender: req.body.employees.find((employee) => employee.type === "artist")?.gender || "",
        update_at: sqlDateTimeFormat(new Date())
      });

      if (!result) throw new Error("Failed to add sub-project");

      this.sdk.setTable("subproject_employee");

      let employees = req.body.employees;
      if (employees && employees.length > 0) {
        const subprojectEmployees = employees.map((employee) => {
          return {
            subproject_id: result,
            employee_id: Number(employee.id),
            employee_type: employee.type,
            gender: employee.gender,
            employee_cost: Number(employee.cost),
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          };
        });

        const subprojectEmployee = await this.sdk.rawQuery(`
          INSERT INTO equalityrecord_subproject_employee (subproject_id, employee_id, employee_type, gender, employee_cost, create_at, update_at)
          VALUES ${subprojectEmployees
            .map(
              (employee) =>
                `(${employee.subproject_id}, ${employee.employee_id}, '${employee.employee_type}', '${employee.gender}', '${employee.employee_cost}', '${employee.create_at}', '${employee.update_at}')`
            )
            .join(",")};
        `);

        if (!subprojectEmployee) {
          this.sdk.setTable("subproject");
          this.sdk.delete({}, result);
          throw new Error("Failed to add employee on sub-project");
        }
      }

      return true;
    } catch (error) {
      throw new Error(error);
    }
  }

  async addSubProjectWithoutProjectId(req) {
    try {
      this.sdk = this.getSDK(req);

      this.sdk.setTable("mix_season");

      const mixSeason = await this.sdk.get({
        id: req.body.mix_season
      });

      if (mixSeason.length === 0) throw new Error("Mix season not found");

      this.sdk.setTable("subproject");

      let id_string_count = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count FROM equalityrecord_subproject
        WHERE id_string LIKE '%${mixSeason[0].name.replace(/-/g, "").replace(/20/g, "")}%';
      `);

      console.log(id_string_count);

      if (id_string_count[0].count > 0) {
        id_string_count = id_string_count[0].count;
      } else {
        id_string_count = 0;
      }

      const id_string = `${mixSeason[0].name.replace(/-/g, "").replace(/20/g, "")}-${id_string_count.toString().padStart(3, "0")}`;

      const result = await this.sdk.insert({
        type: req.body.type_name,
        type_name: req.body.type_name,
        eight_count: Number(req.body.eight_count),
        is_file: req.body.is_file ?? 0,
        lyrics: req.body.lyrics || "",
        file_cost: req.body.file_cost || 0,
        files: req.body.files || "",
        mix_season_id: Number(req.body.mix_season),
        status: 0,
        user_id: req.user_id,
        song_title_key: req.body.song_title_key || "", // song title key
        bpm_lyrics: req.body.bpm_lyrics || "",
        is_song: req.body.is_song ?? 0,
        song_type: req.body.song_type || "",
        genre: req.body.genre || "",
        attachments: req.body.attachments || "",
        bpm: req.body.bpm,
        id_string: id_string,
        song_key: req.body.song_key,
        gender: req.body.employees.find((employee) => employee.type === "artist")?.gender || "",
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      if (!result) throw new Error("Failed to add sub-project");

      this.sdk.setTable("subproject_employee");

      let employees = req.body.employees;
      if (employees && employees.length > 0) {
        const subprojectEmployees = employees.map((employee) => {
          return {
            subproject_id: result,
            employee_id: Number(employee.id),
            employee_type: employee.type,
            gender: employee.gender,
            employee_cost: Number(employee.cost),
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          };
        });

        const subprojectEmployee = await this.sdk.rawQuery(`
          INSERT INTO equalityrecord_subproject_employee (subproject_id, employee_id, employee_type, gender, employee_cost, create_at, update_at)
          VALUES ${subprojectEmployees
            .map(
              (employee) =>
                `(${employee.subproject_id}, ${employee.employee_id}, '${employee.employee_type}', '${employee.gender || ""}', '${employee.employee_cost}', '${
                  employee.create_at
                }', '${employee.update_at}')`
            )
            .join(",")};
        `);

        if (!subprojectEmployee) {
          this.sdk.setTable("subproject");
          this.sdk.delete({}, result);
          throw new Error("Failed to add employee on sub-project");
        }
      }

      return {
        error: false,
        message: "Sub-project added successfully",
        result: result
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  // export all song to a csv
  async exportSongsToCsv(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      const result = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject
        WHERE is_song = 1;
      `);

      const obFields = Object.keys(result[0]).map((key) => key);

      const escapeCsv = (value) => {
        if (typeof value !== "string") return value;
        // Remove commas and other special characters entirely
        return value.replace(/[!@#$%^&*()_+\-=`'",]/g, "");
      };

      // Escape values while maintaining the original structure
      const escapedResult = result.map((row) => {
        const escapedRow = {};
        Object.keys(row).forEach((key) => {
          escapedRow[key] = escapeCsv(row[key]); // Escape each value in the row
        });
        return escapedRow; // Return an object, not an array
      });

      return escapedResult;
    } catch (error) {
      throw new Error(error);
    }
  }

  async downloadAllSongs(req) {
    try {
      this.sdk = this.getSDK(req);
      const filter = req.body.filter || {};

      // Build query based on filters
      const whereConditions = [
        "sp.is_song = 1",
        `(sp.user_id = ${req.user_id} OR p.user_id = ${req.user_id} OR c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id})`
      ];

      // filter.id_string array
      if (filter.id_string && Array.isArray(filter.id_string)) {
        whereConditions.push(`sp.id_string LIKE '%${filter.id_string.join(",")}%'`);
      }
      if (filter.mix_date_range) {
        whereConditions.push(`p.mix_date BETWEEN '${filter.mix_date_range.start}' AND '${filter.mix_date_range.end}'`);
      }

      // Prepare employee filter conditions
      const employeeFilters = [];
      if (filter.writer_id) {
        employeeFilters.push(`(spe.employee_id = ${filter.writer_id} AND spe.employee_type = 'writer')`);
      }
      if (filter.artist_id) {
        employeeFilters.push(`(spe.employee_id = ${filter.artist_id} AND spe.employee_type = 'artist')`);
      }
      if (filter.engineer_id) {
        employeeFilters.push(`(spe.employee_id = ${filter.engineer_id} AND spe.employee_type = 'engineer')`);
      }

      // Subquery to get filtered subproject IDs based on employee criteria
      const subprojectSubquery =
        employeeFilters.length > 0
          ? `
        WITH filtered_subprojects AS (
          SELECT DISTINCT sp.id
          FROM equalityrecord_subproject sp
          INNER JOIN equalityrecord_subproject_employee spe ON sp.id = spe.subproject_id
          LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
          WHERE ${whereConditions.join(" AND ")}
            AND (${employeeFilters.join(" OR ")})
        )
      `
          : "";

      // Main query
      const mainQuery = `
      ${subprojectSubquery}
      SELECT 
        sp.*,
        c.program AS project_name,
        p.team_name AS team_name,
        wo.writer_submit_status,
        wo.status AS workorder_status, 
        wo.auto_approve AS workorder_auto_approve
      FROM equalityrecord_subproject sp
        LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
        LEFT JOIN equalityrecord_client c ON p.client_id = c.id
        LEFT JOIN equalityrecord_work_order wo ON sp.workorder_id = wo.id
        ${employeeFilters.length > 0 ? "INNER JOIN filtered_subprojects fs ON sp.id = fs.id" : `WHERE ${whereConditions.join(" AND ")}`}
      GROUP BY sp.id
      ORDER BY sp.id DESC
    `;

      let result = await this.sdk.rawQuery(mainQuery);

      if (result.length > 0) {
        // Get all subproject IDs
        const subprojectIds = result.map((item) => item.id);

        // Batch fetch employees for all subprojects
        const employeesQuery = `
        SELECT 
          e.*, 
          spe.employee_cost AS emp_cost, 
          spe.employee_type AS emp_type,
          spe.subproject_id
        FROM equalityrecord_subproject_employee spe
        INNER JOIN equalityrecord_employee e ON spe.employee_id = e.id
        WHERE spe.subproject_id IN (${subprojectIds.join(",")})
      `;
        const employees = await this.sdk.rawQuery(employeesQuery);

        // Batch fetch all files
        const filesQuery = `
        SELECT *
        FROM equalityrecord_project_file
        WHERE subproject_id IN (${subprojectIds.join(",")})
      `;
        const allFiles = await this.sdk.rawQuery(filesQuery);

        // Process results
        result = result.map((resultItem) => {
          // Map employees to their subprojects
          resultItem.employees = employees.filter((emp) => emp.subproject_id === resultItem.id);

          // Map files to their categories
          const subprojectFiles = allFiles.filter((file) => file.subproject_id === resultItem.id);

          resultItem.admin_writer_instrumentals = subprojectFiles.filter(
            (file) => file.employee_type === "writer" && file.type === "instrumental" && file.is_from_admin === 1
          );

          resultItem.admin_producer_instrumentals = subprojectFiles.filter(
            (file) => file.employee_type === "producer" && file.type === "instrumental" && file.is_from_admin === 1
          );

          resultItem.producer_files = subprojectFiles.filter(
            (file) => file.employee_type === "producer" && file.type === "instrumental" && file.is_from_admin === 0
          );

          resultItem.demos = subprojectFiles.filter((file) => file.employee_type === "writer" && file.type === "demo");

          resultItem.masters = subprojectFiles.filter((file) => file.employee_type === "engineer" && file.type === "master");

          return resultItem;
        });
      }

      return { result };
    } catch (error) {
      throw new Error(error);
    }
  }

  // get a specific column of all songs
  async getSongsColumn(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");
      // check for sql injection
      if (!req.query.type) throw new Error("Column is required");

      let column = req.query.type;
      let alias = column;

      if (column.includes("'") || column.includes('"')) throw new Error("Invalid column name");

      //mix_date, writer, writer_cost, engineer, engineer_cost, artist, artist_cost, for columns like this we need to join with subproject_employee

      let array_of_columns_employee = ["is_writer", "name", "writer_cost", "is_artist", "artist_cost", "is_engineer", "engineer_cost"];
      let array_of_client_columns = ["project_name", "program_name"];

      let table_with_column = "equalityrecord_subproject";

      if (array_of_columns_employee.includes(column)) {
        table_with_column = `equalityrecord_employee`;
      }

      console.log(array_of_client_columns.includes(column));

      if (array_of_client_columns.includes(column)) {
        table_with_column = `equalityrecord_client`;
      }

      if (column === "program_name") {
        column = `program`;
        alias = `program_name`;
      }

      const result = await this.sdk.rawQuery(`
        SELECT ${table_with_column}.${column} AS ${alias} FROM equalityrecord_subproject
        LEFT JOIN equalityrecord_subproject_employee ON equalityrecord_subproject_employee.subproject_id = equalityrecord_subproject.id 
        LEFT JOIN equalityrecord_employee ON equalityrecord_employee.id = equalityrecord_subproject_employee.employee_id
        LEFT JOIN equalityrecord_project ON equalityrecord_project.id = equalityrecord_subproject.project_id
        LEFT JOIN equalityrecord_client ON equalityrecord_client.id = equalityrecord_project.client_id
        WHERE is_song = 1 AND 
        (equalityrecord_subproject.user_id = ${req.user_id} OR equalityrecord_project.user_id = ${req.user_id} OR equalityrecord_client.user_id = ${req.user_id} OR equalityrecord_client.member_two_user_id = ${req.user_id})
        GROUP BY equalityrecord_subproject.id
        ORDER BY equalityrecord_subproject.mix_date DESC, equalityrecord_subproject.id DESC;
      `);

      return result;
    } catch (error) {
      throw new Error(error);
    }
  }

  // move song to a new mix season change id_string
  async moveSongToNewMixSeason(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      let { subproject_id, mix_season } = req.body;

      this.sdk.setTable("mix_season");

      const mixSeason = await this.sdk.get({
        id: mix_season
      });

      if (!mixSeason) throw new Error("Mix season not found");

      let id_string_count = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count FROM equalityrecord_subproject
        WHERE id_string LIKE '%${mixSeason[0].name.replace(/-/g, "").replace(/20/g, "")}%';
      `);

      if (id_string_count[0].count > 0) {
        id_string_count = id_string_count[0].count;
      } else {
        id_string_count = 0;
      }

      const id_string = `${mixSeason[0].name.replace(/-/g, "").replace(/20/g, "")}-${id_string_count.toString().padStart(3, "0")}`;

      this.sdk.setTable("subproject");
      const result = await this.sdk.update(
        {
          id_string: id_string,
          update_at: sqlDateTimeFormat(new Date())
        },
        subproject_id
      );

      return {
        error: false,
        message: "Sub-project moved successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async addSubProjectToProject(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      let { project_ids, subproject_id } = req.body;

      // clone for each project_id of that subproject id and the last update the subproject id with the last project_id
      for (const project_id of project_ids) {
        this.sdk.setTable("project");
        const project = await this.sdk.get({
          id: project_id
        });

        // check if the project is already in the subproject
        const exists = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_subproject
          WHERE project_id = ${project_id}
          AND id = ${subproject_id};
        `);

        if (exists.length > 0) continue;

        if (!project) throw new Error("Project not found");

        this.sdk.setTable("subproject");
        const subproject = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject
        WHERE id = ${subproject_id};
      `);

        if (!subproject) throw new Error("Sub-project not found");

        delete subproject[0].id;
        delete subproject[0].create_at;
        delete subproject[0].update_at;

        let subprojectResult = await this.sdk.insert({
          ...subproject[0],
          mix_date: project[0].mix_date,
          project_id: Number(project_id),
          parent_subproject_id: Number(subproject_id),
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        this.sdk.setTable("project_file");
        const projectFiles = await this.sdk.get({
          subproject_id: subproject_id
        });

        for (const projectFile of projectFiles) {
          delete projectFile.id;
          await this.sdk.insert({
            ...projectFile,
            project_id: Number(project_id)
          });
        }

        // employee
        this.sdk.setTable("subproject_employee");
        const subprojectEmployees = await this.sdk.get({
          subproject_id: subproject_id
        });

        for (const subprojectEmployee of subprojectEmployees) {
          delete subprojectEmployee.id;
          delete subprojectEmployee.create_at;
          delete subprojectEmployee.update_at;
          await this.sdk.insert({
            ...subprojectEmployee,
            subproject_id: Number(subprojectResult)
          });
        }

        // this.sdk.setTable("work_order");
        // let workOrder = await this.sdk.get({
        //   subproject_id: subproject_id
        // });

        // delete workOrder.id;

        // await this.sdk.insert({
        //   ...workOrder,
        //   subproject_id: Number(subprojectResult),
        //   project_id: Number(project_id)
        // });
      }

      // fetch all subprojects with parent_subproject_id
      this.sdk.setTable("subproject");
      const subprojects = await this.sdk.get({
        parent_subproject_id: subproject_id
      });

      // update all subprojects with this new project_ids
      for (const subproject of subprojects) {
        let old_project_ids = subproject.project_ids ? JSON.parse(subproject.project_ids) : [];
        let new_project_ids = [...old_project_ids, ...project_ids];
        await this.sdk.update(
          {
            project_ids: JSON.stringify(new_project_ids)
          },
          subproject.id
        );
      }
      return {
        error: false,
        message: "Sub-project added successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSubProject(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      await this.sdk.update(
        {
          status: 1,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.body.subproject_id
      );

      let employees = req.body.employees;
      if (employees && employees.length > 0) {
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_subproject_employee
          WHERE subproject_id = ${Number(req.body.subproject_id)};
        `);

        const subprojectEmployees = employees.map((employee) => {
          return {
            subproject_id: Number(req.body.subproject_id),
            employee_id: Number(employee.id),
            employee_type: employee.type,
            employee_cost: Number(employee.cost),
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          };
        });

        await this.sdk.rawQuery(`
          INSERT INTO equalityrecord_subproject_employee (subproject_id, employee_id, employee_type, employee_cost, create_at, update_at)
          VALUES ${subprojectEmployees
            .map(
              (employee) =>
                `(${employee.subproject_id}, ${employee.employee_id}, '${employee.employee_type}', ${employee.employee_cost}, '${employee.create_at}', '${employee.update_at}')`
            )
            .join(",")};
        `);

        return {
          error: false,
          message: "Sub-project updated successfully"
        };
      } else {
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_subproject_employee
          WHERE subproject_id = ${Number(req.body.subproject_id)};
        `);

        return {
          error: false,
          message: "Sub-project updated successfully"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteSubProjects(req) {
    try {
      this.sdk = this.getSDK(req);

      let subProjectIds = req.body.subproject_ids;
      // [1, 2, 4] or [1]

      let joinedIds = subProjectIds.join(",");
      if (subProjectIds.length === 1) {
        await this.sdk.rawQuery(`DELETE FROM equalityrecord_subproject_employee WHERE subproject_id = ${joinedIds};`);
        await this.sdk.rawQuery(`DELETE FROM equalityrecord_subproject_idea WHERE subproject_id = ${joinedIds};`);
        await this.sdk.rawQuery(`DELETE FROM equalityrecord_subproject WHERE id = ${joinedIds};`);
        return {
          error: false,
          message: "Sub-project deleted successfully"
        };
      } else if (subProjectIds.length > 1) {
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_subproject_employee
          WHERE subproject_id IN (${subProjectIds.join(",")});
        `);

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_subproject_idea
          WHERE subproject_id IN (${subProjectIds.join(",")});
        `);

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_subproject
          WHERE id IN (${subProjectIds.join(",")});
        `);

        return {
          error: false,
          message: "Sub-projects deleted successfully"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSubProjectEmployee(req) {
    try {
      this.sdk = this.getSDK(req);

      const { new_employee_id, employee_type, employee_cost, subproject_id } = req.body;

      this.sdk.setTable("subproject");
      const subproject = await this.sdk.get({
        id: Number(req.body.subproject_id)
      });

      let other_subproject = [];
      if (subproject[0].parent_subproject_id) {
        other_subproject = await this.sdk.get({
          parent_subproject_id: subproject[0].parent_subproject_id
        });
      }

      let subproject_ids = [Number(req.body.subproject_id), ...other_subproject.map((subproject) => subproject.id)];

      if (new_employee_id) {
        const existingEmployees = await this.sdk.rawQuery(`
    SELECT id, subproject_id FROM equalityrecord_subproject_employee
    WHERE subproject_id IN (${subproject_ids.join(",")})
    AND employee_type = '${employee_type}';
  `);

        const existingSubprojectIds = new Set(existingEmployees.map((emp) => emp.subproject_id));

        // Insert missing subproject employees
        for (const subproject_id of subproject_ids) {
          if (!existingSubprojectIds.has(subproject_id)) {
            await this.sdk.rawQuery(`
        INSERT INTO equalityrecord_subproject_employee (subproject_id, employee_id, employee_type, employee_cost, create_at, update_at)
        VALUES (${Number(subproject_id)}, ${Number(new_employee_id)}, '${employee_type}', ${Number(employee_cost)}, '${sqlDateFormat(
              new Date()
            )}', '${sqlDateTimeFormat(new Date())}');
      `);
          }
        }

        // Update all subproject employees in one query
        await this.sdk.rawQuery(`
    UPDATE equalityrecord_subproject_employee
    SET employee_id = ${Number(new_employee_id)}, employee_cost = ${Number(employee_cost)}
    WHERE subproject_id IN (${subproject_ids.join(",")})
    AND employee_type = '${employee_type}';
  `);
      }

      return {
        error: false,
        message: "Sub-project updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async resetSubProjectEmployee(req) {
    try {
      this.sdk = this.getSDK(req);

      const { employee_type, subproject_id } = req.body;

      this.sdk.setTable("subproject");
      const subproject = await this.sdk.get({
        id: Number(req.body.subproject_id)
      });

      let other_subproject = [];
      if (subproject[0].parent_subproject_id) {
        other_subproject = await this.sdk.get({
          parent_subproject_id: subproject[0].parent_subproject_id
        });
      }

      let subproject_ids = [Number(req.body.subproject_id), ...other_subproject.map((subproject) => subproject.id)];

      if (subproject_id && employee_type) {
        const exists = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_subproject_employee
          WHERE subproject_id IN (${subproject_ids.join(",")})
          AND employee_type = '${employee_type}';
        `);

        if (exists.length > 0) {
          await this.sdk.rawQuery(`
            UPDATE equalityrecord_subproject_employee
            SET employee_id = 0, employee_cost = 0
            WHERE id IN (${exists.map((employee) => employee.id).join(",")});
          `);
        }
      }

      return {
        error: false,
        message: "Sub-project employee reset successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSubProjectEmployeeCost(req) {
    try {
      this.sdk = this.getSDK(req);

      const { employee_id, employee_type, employee_cost, subproject_id } = req.body;

      const exists = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject_employee
        WHERE subproject_id = ${Number(subproject_id)}
        AND employee_id = ${Number(employee_id)}
        AND employee_type = '${employee_type}';
      `);

      if (exists.length > 0) {
        await this.sdk.rawQuery(`
          UPDATE equalityrecord_subproject_employee
          SET employee_cost = ${Number(employee_cost)}
          WHERE id = ${Number(exists[0].id)};
        `);
      }

      this.sdk.setTable("subproject");
      await this.sdk.update(
        {
          update_at: sqlDateTimeFormat(new Date())
        },
        req.body.subproject_id
      );

      return {
        error: false,
        message: "Sub-project updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSubProjectEightCount(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      await this.sdk.update(
        {
          eight_count: req.body.eight_count,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.body.subproject_id
      );

      return {
        error: false,
        message: "Sub-project eight count updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSubProjectStatus(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      await this.sdk.update(
        {
          status: req.body.status,
          update_at: sqlDateTimeFormat(new Date())
        },
        req.body.subproject_id
      );
      return {
        error: false,
        message: "Sub-project status updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getSubProjectsByProjectId(req) {
    try {
      this.sdk = this.getSDK(req);

      // one sub-project has many employees
      // so join with equalityrecord_subproject_employee table and equalityrecord_employee table
      let result = await this.sdk.rawQuery(`
        SELECT
          sp.*,
          wo.writer_submit_status,
          wo.status AS workorder_status, wo.auto_approve AS workorder_auto_approve
        FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
          LEFT JOIN equalityrecord_work_order wo ON sp.workorder_id = wo.id
        WHERE sp.project_id = ${req.params.project_id};
      `);

      if (result.length > 0) {
        for (let resultItem of result) {
          const employees = await this.sdk.rawQuery(`
          SELECT e.*, spe.employee_cost AS emp_cost, spe.employee_type AS emp_type
            FROM equalityrecord_subproject_employee spe
            INNER JOIN equalityrecord_employee e ON spe.employee_id = e.id
          WHERE spe.subproject_id = ${resultItem.id};
        `);

          resultItem.employees = employees;

          // include files of each sub-project
          // retrieve instrumental files -> uploaded by writer
          // this.sdk.setTable("project_file");
          // let instrumentals = await this.sdk.get({
          //   employee_type: "writer",
          //   type: "instrumental"
          // });
          // resultItem.instrumentals = instrumentals.length > 0 ? instrumentals : [];

          // retrieve admin instrumental (loops) files -> uploaded by admin
          this.sdk.setTable("project_file");
          let adminWriterInstrumentals = await this.sdk.get({
            // workorder_id: result[0].id,
            subproject_id: resultItem.id,
            employee_type: "writer",
            type: "instrumental",
            is_from_admin: 1
          });
          resultItem.admin_writer_instrumentals = adminWriterInstrumentals.length > 0 ? adminWriterInstrumentals : [];

          // retrieve admin instrumental (loops) files -> uploaded by admin
          this.sdk.setTable("project_file");
          let adminProducerInstrumentals = await this.sdk.get({
            // workorder_id: result[0].id,
            subproject_id: resultItem.id,
            employee_type: "producer",
            type: "instrumental",
            is_from_admin: 1
          });
          resultItem.admin_producer_instrumentals = adminProducerInstrumentals.length > 0 ? adminProducerInstrumentals : [];

          // retrieve admin instrumental (loops) files -> uploaded by admin
          this.sdk.setTable("project_file");
          let producerFiles = await this.sdk.get({
            // workorder_id: result[0].id,
            subproject_id: resultItem.id,
            employee_type: "producer",
            type: "instrumental",
            is_from_admin: 0
          });
          resultItem.producer_files = producerFiles.length > 0 ? producerFiles : [];

          // retrieve demo files -> uploaded by writer
          let demos = await this.sdk.get({
            subproject_id: resultItem.id,
            employee_type: "writer",
            type: "demo"
          });
          resultItem.demos = demos.length > 0 ? demos : [];

          // retrieve session files -> uploaded by artist
          // let sessions = await this.sdk.get({
          //   employee_type: "artist",
          //   type: "session"
          // });
          // resultItem.sessions = sessions.length > 0 ? sessions : [];

          // retrieve master files -> uploaded by engineer
          let masters = await this.sdk.get({
            subproject_id: resultItem.id,
            employee_type: "engineer",
            type: "master"
          });
          resultItem.masters = masters.length > 0 ? masters : [];
        }
      }
      return result;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllUnAssignedSongs(req) {
    this.sdk = this.getSDK(req);

    let filter = req.body.filter ?? {};

    // where workorder_id is null
    let result = await this.sdk.rawQuery(`
      SELECT
        sp.*,
        p.user_id, p.client_id, p.mix_season_id, p.mix_date, p.team_name,
        p.mix_type_id, p.team_type, p.division, p.colors, p.discount, p.content_status,
        c.program AS program_name, c.name AS program_owner_name, c.position AS program_owner_position,
        c.email AS program_owner_email, c.phone AS program_owner_phone,
        ms.name AS mix_season,
        e.id AS artist_id, e.name AS artist_name, e.email AS artist_email
      FROM equalityrecord_subproject sp
        LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
        LEFT JOIN equalityrecord_client c ON p.client_id = c.id
        LEFT JOIN equalityrecord_mix_season ms ON p.mix_season_id = ms.id
        LEFT JOIN equalityrecord_subproject_employee se ON sp.id = se.subproject_id
        LEFT JOIN equalityrecord_employee e ON se.employee_id = e.id
      WHERE
        sp.is_song = 1
        AND se.employee_type = 'artist'
        AND p.user_id = ${req.user_id}
        AND c.user_id = ${req.user_id};
    `);

    if (result.length > 0) {
      return {
        error: false,
        list: result
      };
    } else {
      return {
        error: false,
        list: []
      };
    }
  }

  async addTeamDetails(req, projectId) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("team_details");

      const exists = await this.sdk.get({
        project_id: Number(projectId)
      });

      if (exists.length > 0) {
        return null;
      }

      const result = await this.sdk.insert({
        project_id: Number(projectId),
        colors: req.body.colors,
        song_list: req.body.song_list,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return result;
    } catch (error) {
      throw new Error(error);
    }
  }

  async addSurvey(req, projectId) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const exists = await this.sdk.get({
        project_id: projectId
      });

      if (exists.length > 0) {
        return null;
      }

      const result = await this.sdk.insert({
        project_id: Number(projectId),
        uuidv4: req.body.uuidv4,
        theme_of_the_routine: null,
        overall_description: null,
        email_status: 0,
        email_retry: 0,
        status: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return result;
    } catch (error) {
      throw new Error(error);
    }
  }

  async view(req) {
    try {
      this.sdk = this.getSDK(req);

      // Initialize the main filter query
      let filterQuery = `WHERE p.id = ${req.params.id} `;

      const filter = req.body.filter ?? {};

      let filterArr = [];
      if (req.role !== "admin" && req.role !== "client" && req.role !== "manager") {
        filterArr.push(`p.user_id = ${req.user_id}`);
      }

      // Construct filtering logic for nextProject and previousProject

      if (filter.client_ids && filter.client_ids.length > 0) {
        filterArr.push(`p.client_id IN (${filter.client_ids.join(",")})`);
      }
      if (filter.team_names && filter.team_names.length > 0) {
        filterArr.push(`LOWER(p.team_name) IN (${filter.team_names.map((name) => `'${name.toLowerCase()}'`).join(",")})`);
      }
      if (filter.mix_date_start) {
        filterArr.push(`p.mix_date >= '${filter.mix_date_start}'`);
      }
      if (filter.mix_date_end) {
        filterArr.push(`p.mix_date <= '${filter.mix_date_end}'`);
      }
      if (filter.mix_type_ids && filter.mix_type_ids.length > 0) {
        filterArr.push(`p.mix_type_id IN (${filter.mix_type_ids.join(",")})`);
      }
      if (filter.team_type) {
        filterArr.push(`p.team_type = ${filter.team_type}`);
      }
      if (filter.payment_status) {
        filterArr.push(`p.payment_status = ${filter.payment_status}`);
      }
      if (filter.payment_status_without_completed) {
        filterArr.push(`p.payment_status IN (2, 3, 4, 5)`);
      }
      if (filter.user_id) {
        filterArr.push(`p.user_id = ${filter.user_id}`);
      }

      filterQuery = filterArr.length > 0 ? filterQuery + ` AND ${filterArr.join(" AND ")}` : filterQuery;

      const additionalFilterQuery = filterArr.length > 0 ? `WHERE ${filterArr.join(" AND ")}` : "";

      // Fetch the project data
      const projectQuery = `
      SELECT p.*, c.program AS program_name, c.name AS program_owner_name,
        c.position AS program_owner_position, c.email AS program_owner_email, 
        c.phone AS program_owner_phone, m.name AS mix_type_name, m.price AS total,
        ms.name AS mix_season_name, COALESCE(se.total_employee_cost, 0) AS expenses,
        s.id AS survey_id, s.uuidv4, s.theme_of_the_routine, s.overall_description, 
        s.status AS survey_status, td.colors, td.song_list
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      LEFT JOIN equalityrecord_mix_season ms ON p.mix_season_id = ms.id
      LEFT JOIN equalityrecord_team_details td ON p.id = td.project_id
      LEFT JOIN (
        SELECT s.project_id, SUM(se.employee_cost) AS total_employee_cost
        FROM equalityrecord_subproject s
        LEFT JOIN equalityrecord_subproject_employee se ON s.id = se.subproject_id
        GROUP BY s.project_id
      ) se ON p.id = se.project_id
      ${filterQuery}
      LIMIT 1
    `;
      let result = await this.sdk.rawQuery(projectQuery);

      if (result.length === 0) {
        return null;
      }

      if (req.role === "manager") {
        //  check if the manager has permission to access this project
        const managerMembers = await this.sdk.rawQuery(`
          SELECT mp.member_ids FROM equalityrecord_manager_permission mp WHERE mp.manager_id = ${req.user_id};
        `);

        if (managerMembers.length === 0) {
          return null;
        }

        const memberIds = JSON.parse(managerMembers[0].member_ids).map((id) => Number(id));
        if (!memberIds.includes(result[0].user_id)) {
          return null;
        }
      }

      // remove where p.id = ${req.params.id}

      filterQuery = filterQuery.replace(`p.id = ${req.params.id} AND`, "");

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      ${additionalFilterQuery}
      ORDER BY p.mix_date ASC, p.id ASC`;

      let projectList = await this.sdk.rawQuery(rawSql);

      // find THE POSITION of current project
      const currentProjectPosition = projectList.findIndex((project) => project.id === result[0].id);
      result[0].next_project_id = currentProjectPosition > -1 ? projectList[currentProjectPosition + 1]?.id : null;

      // find previous project
      result[0].previous_project_id = currentProjectPosition > 0 ? projectList[currentProjectPosition - 1]?.id : null;

      const companyInfoQuery = `
      SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
        p.company_name, p.office_email, p.company_logo, p.license_company_logo,
        CASE 
          WHEN mp.member_ids IS NOT NULL 
          THEN JSON_EXTRACT(mp.member_ids, '$[0]')
          ELSE NULL 
        END AS main_member_id
      FROM equalityrecord_user u
      LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
      LEFT JOIN equalityrecord_manager_permission mp ON (JSON_CONTAINS(mp.member_ids, CAST(u.id AS CHAR)) OR mp.manager_id = u.id)
      WHERE u.id = ${result[0].user_id}
    `;
      let companyInfoResult = await this.sdk.rawQuery(companyInfoQuery);
      result[0].company_info = companyInfoResult.length > 0 ? companyInfoResult[0] : null;

      return result[0];
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is a main member or manager
      // Only main member or manager can delete projects
      if (req.role !== "admin") {
        // Check if the user is a manager
        const isManager = req.role === "manager";

        // If not a manager, check if they are a main member (part of a company but the first member)
        let isMainMember = false;

        if (!isManager && req.role === "member") {
          // Check if user is part of a company
          const companyInfo = await this.sdk.rawQuery(`
            SELECT mp.* 
            FROM equalityrecord_manager_permission mp
            WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
          `);

          if (companyInfo.length > 0) {
            // User is part of a company, check if they are the first member
            const memberIds = JSON.parse(companyInfo[0].member_ids);
            // Main member is typically the first member in the company
            isMainMember = memberIds[0] == req.user_id;
          }
        }

        // If neither manager nor main member, reject project deletion
        // if (!isManager && !isMainMember) {
        //   return {
        //     error: true,
        //     message: "Only main member or manager can delete projects"
        //   };
        // }
      }

      const result = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_project WHERE id = ${req.params.id}
      `);

      if (result.length === 0) {
        return {
          error: true,
          message: "Project not found!"
        };
      } else {
        const subProjects = await this.sdk.rawQuery(`
          SELECT *
          FROM equalityrecord_subproject
          WHERE project_id = ${req.params.id};
        `);
        if (subProjects.length > 1) {
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_employee
            WHERE subproject_id IN (${subProjects.map((subproject) => subproject.id).join(",")});
          `);
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_idea
            WHERE subproject_id IN (${subProjects.map((subproject) => subproject.id).join(",")});
          `);
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject
            WHERE project_id = ${req.params.id};
          `);
        } else if (subProjects.length === 1) {
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_employee
            WHERE subproject_id = ${subProjects[0].id};
          `);
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_idea
            WHERE subproject_id = ${subProjects[0].id};
          `);
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject
            WHERE project_id = ${req.params.id};
          `);
        }
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_project_file
          WHERE project_id = ${req.params.id};
        `);
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_survey
          WHERE project_id = ${req.params.id};
        `);
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_edit
          WHERE project_id = ${req.params.id};
        `);
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_survey_notification
          WHERE project_id = ${req.params.id};
        `);
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_subproject
          WHERE project_id = ${req.params.id};
        `);
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_idea
          WHERE project_id = ${req.params.id};
        `);
        this.sdk.setTable("project");
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Project deleted successfully"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async cleanup(req) {
    try {
      this.sdk = this.getSDK(req);

      const { user_id, mix_season_id } = req.body;

      // Get all projects for this mix season
      const projectIds = await this.sdk.rawQuery(`
        SELECT id FROM equalityrecord_project 
        WHERE mix_season_id = ${mix_season_id} AND user_id = ${user_id};
      `);

      if (projectIds.length === 0) {
        return {
          error: true,
          message: "No projects found for this mix season"
        };
      }

      const projectIdList = projectIds.map((p) => p.id);

      if (projectIdList.length === 1) {
        // Get all file URLs before deletion
        const fileUrls = await this.sdk.rawQuery(`
          SELECT url FROM equalityrecord_project_file 
          WHERE project_id = ${projectIdList[0]};
        `);

        // Delete files from S3 if there are any
        if (fileUrls.length > 0) {
          const urls = fileUrls.map((f) => f.url);
          await this.deleteMultipleS3Files(urls);
        }

        // Now proceed with database cleanup
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_project_file
          WHERE project_id = ${projectIdList[0]};
        `);

        // Rest of the cleanup code...
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_survey_notification
          WHERE project_id = ${projectIdList[0]};
        `);

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_survey
          WHERE project_id = ${projectIdList[0]};
        `);

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_idea
          WHERE project_id = ${projectIdList[0]};
        `);

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_project
          WHERE id = ${projectIdList[0]};
        `);

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_mix_season
          WHERE id = ${mix_season_id};
        `);

        const subProjects = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_subproject WHERE project_id = ${projectIdList[0]};
        `);

        for (let subProject of subProjects) {
          let projectFiles = await this.sdk.rawQuery(`
            SELECT id, url FROM equalityrecord_project_file WHERE subproject_id IN (${subProjects.map((row) => row.id).join(",")}) ;
          `);

          if (projectFiles.length > 0) {
            const urls = projectFiles.map((f) => f.url);
            await this.deleteMultipleS3Files(urls);
          }

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_project_file
            WHERE subproject_id = ${subProject.id};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject
            WHERE id = ${subProject.id};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_work_order
            WHERE id = ${subProject.workorder_id};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_employee
            WHERE subproject_id = ${subProject.id};
          `);
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_idea
            WHERE subproject_id = ${subProject.id};
          `);
        }
      } else if (projectIdList.length > 1) {
        // Get all file URLs for multiple projects before deletion
        const fileUrls = await this.sdk.rawQuery(`
          SELECT url FROM equalityrecord_project_file 
          WHERE project_id IN (${projectIdList.join(",")});
        `);

        // Delete files from S3 if there are any
        if (fileUrls.length > 0) {
          const urls = fileUrls.map((f) => f.url);
          await this.deleteMultipleS3Files(urls);
        }

        for (let projectId of projectIdList) {
          // Now proceed with database cleanup for each project
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_project_file
            WHERE project_id = ${projectId};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_survey
            WHERE project_id = ${projectId};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_survey_notification
            WHERE project_id = ${projectId};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_idea
            WHERE project_id = ${projectId};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_project
            WHERE id = ${projectId};
          `);
        }

        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_mix_season
          WHERE id = ${mix_season_id};
        `);

        const subProjects = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_subproject WHERE project_id IN (${projectIdList.join(",")});
        `);

        for (let subProject of subProjects) {
          let projectFiles = await this.sdk.rawQuery(`
            SELECT id, url FROM equalityrecord_project_file WHERE subproject_id IN (${subProjects.map((row) => row.id).join(",")}) ;
          `);

          if (projectFiles.length > 0) {
            const urls = projectFiles.map((f) => f.url);
            await this.deleteMultipleS3Files(urls);
          }

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_project_file
            WHERE subproject_id = ${subProject.id};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject
            WHERE id = ${subProject.id};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_work_order
            WHERE id = ${subProject.workorder_id};
          `);

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_employee
            WHERE subproject_id = ${subProject.id};
          `);
          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_subproject_idea
            WHERE subproject_id = ${subProject.id};
          `);
        }
      }

      return {
        error: false,
        message: "Cleanup completed successfully"
      };
    } catch (error) {
      console.error("Error in cleanup:", error);
      throw new Error(error);
    }
  }

  async deleteMultipleS3Files(urls) {
    try {
      const s3 = new aws.S3({
        accessKeyId: config.aws_key,
        secretAccessKey: config.aws_secret
      });

      // urls = ['https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo.png', 'https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo2.png]

      if (urls.length > 0) {
        s3.deleteObjects({
          Bucket: config.aws_bucket,
          Delete: {
            Objects: urls.map((url) => {
              const fileName = url.split("/").pop();
              return {
                Key: fileName
              };
            })
          }
        });
      }

      return {
        error: false,
        message: "Files deleted successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOneFile(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project_file");

      const result = await this.sdk.get({
        id: req.params.id
      });

      await this.sdk.delete({}, req.params.id);
      return {
        error: false,
        message: "File deleted successfully",
        file: result[0]
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewSurvey(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT s.*
        FROM equalityrecord_survey s
        LEFT JOIN equalityrecord_project p ON s.project_id = p.id
        WHERE s.project_id = ${req.body.project_id};
      `);

      if (result.length === 0) {
        this.sdk.setTable("survey");
        const payload = {
          project_id: req.body.project_id,
          uuidv4: req.body.project_id,
          theme_of_the_routine: null,
          overall_description: null,
          email_status: 0,
          email_retry: 0,
          status: 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        };
        await this.sdk.insert(payload);
        return payload;
      } else {
        return result[0];
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewSurveyByProjectId(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const result = await this.sdk.get({
        project_id: req.params.id
      });

      if (result.length > 0) {
        return {
          error: false,
          model: result[0],
          message: "Survey found"
        };
      } else {
        return {
          error: true,
          model: null,
          message: "Survey not found"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllSubProjectsByEmployeeId(req) {
    try {
      this.sdk = this.getSDK(req);

      // equalityrecord_subproject_employee
      // id, employee_id, subproject_id, employee_type

      // equalityrecord_subproject
      // id, type_name, project_id, status

      // equalityrecord_employee
      // id, name, email, is_writer, writer_cost, is_artist, artist_cost, is_engineer, engineer_cost, is_producer, producer_cost, w_nine

      const employeeId = req.query.employee_id;

      // scenario: consider one employee can have multiple subprojects, each project can have multiple subprojects
      const result = await this.sdk.rawQuery(`
        SELECT spe.employee_id, spe.employee_type, sp.*,
        e.id AS employee_id, e.name AS employee_name, e.email AS employee_email,
        e.is_writer, e.writer_cost, e.is_artist, e.artist_cost, e.is_engineer, e.engineer_cost, e.is_producer, e.producer_cost, e.w_nine
        FROM equalityrecord_subproject_employee spe
        LEFT JOIN equalityrecord_subproject sp ON spe.subproject_id = sp.id
        LEFT JOIN equalityrecord_employee e ON spe.employee_id = e.id
        WHERE spe.employee_id = ${employeeId};
      `);

      return result;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllProjectFile(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT pf.*
        FROM equalityrecord_project_file pf
        LEFT JOIN equalityrecord_project p ON pf.project_id = p.id
        WHERE pf.project_id = ${req.params.id};
      `);

      if (result.length === 0) {
        return {
          error: false,
          list: []
        };
      } else {
        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async createProjectFile(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project_file");

      const payload = {
        project_id: req.body.project_id,
        type: req.body.type,
        url: req.body.url,
        employee_id: req.body.employee_id ?? null,
        description: req.body.description ?? null,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      return {
        error: false,
        id: result,
        message: "Project file created successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async createAdminFile(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project_file");

      const payload = {
        project_id: req.body.project_id,
        subproject_id: req.body.subproject_id,
        employee_type: req.body.employee_type,
        type: req.body.type,
        url: req.body.url,
        is_from_admin: 1,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      return {
        error: false,
        id: result,
        message: "Project file created successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllIdea(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT i.*
        FROM equalityrecord_idea i
        LEFT JOIN equalityrecord_project p ON i.project_id = p.id
        WHERE i.project_id = ${req.params.project_id};
      `);

      // iterate result and get subproject_idea
      for (let resultItem of result) {
        const subprojectIdeas = await this.sdk.rawQuery(`
          SELECT
            si.*,
            sp.type AS subproject_type, sp.type_name AS subproject_type_name
          FROM equalityrecord_subproject_idea si
          LEFT JOIN equalityrecord_subproject sp ON si.subproject_id = sp.id
          WHERE si.idea_id = ${resultItem.id};
        `);

        resultItem.subproject_ideas = subprojectIdeas;
      }

      if (result.length === 0) {
        return {
          error: false,
          list: []
        };
      } else {
        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllProjectIdeasBySubProjectId(req) {
    try {
      this.sdk = this.getSDK(req);

      const subProjects = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject WHERE id = ${req.params.id};
      `);

      const result = await this.sdk.rawQuery(`
        SELECT i.*
        FROM equalityrecord_idea i
        LEFT JOIN equalityrecord_project p ON i.project_id = p.id
        WHERE i.project_id = ${subProjects[0].id};
      `);

      // iterate result and get subproject_idea
      for (let resultItem of result) {
        const subprojectIdeas = await this.sdk.rawQuery(`
          SELECT
            si.*,
            sp.type AS subproject_type sp.type_name AS subproject_type_name
          FROM equalityrecord_subproject_idea si
          LEFT JOIN equalityrecord_subproject sp ON si.subproject_id = sp.id
          WHERE si.idea_id = ${resultItem.id};
        `);

        resultItem.subproject_ideas = subprojectIdeas;
      }

      if (result.length === 0) {
        return {
          error: false,
          list: []
        };
      } else {
        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addIdea(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("idea");

      const payload = {
        project_id: req.body.project_id,
        idea_key: req.body.idea_key,
        idea_value: req.body.idea_value,
        survey_id: req.body.survey_id ?? null,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      return {
        error: false,
        id: result,
        message: "Idea created successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateIdea(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("idea");

      const exists = await this.sdk.get({
        idea_key: req.body.idea_key,
        project_id: req.body.project_id
      });

      if (exists.length === 0) {
        return {
          error: true,
          message: "Idea not found"
        };
      }

      const payload = {
        idea_value: req.body.idea_value,
        update_at: sqlDateTimeFormat(new Date())
      };

      await this.sdk.update(payload, exists[0].id);

      return {
        error: false,
        message: "Idea updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllSubProjectIdea(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT si.*, i.idea_key, i.idea_value, i.project_id
        FROM equalityrecord_subproject_idea si
        LEFT JOIN equalityrecord_subproject sp ON si.subproject_id = sp.id
        LEFT JOIN equalityrecord_idea i ON si.idea_id = i.id
        WHERE si.subproject_id = ${req.params.subproject_id};
      `);

      if (result.length === 0) {
        return {
          error: false,
          list: []
        };
      } else {
        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addSubProjectIdea(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject_idea");

      const payload = {
        subproject_id: req.body.subproject_id,
        idea_id: req.body.idea_id,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      return {
        error: false,
        id: result,
        message: "Sub-project idea created successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async createOrUpdateSubProjectIdeas(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject_idea");

      let idea_ids = req.body.idea_ids;
      // idea_ids = [1, 2, 4];

      let subproject_id = req.body.subproject_id;

      const existingSubProjectIdeas = await this.sdk.get({
        subproject_id: Number(subproject_id)
      });

      let existingIdeaIds = [];
      if (existingSubProjectIdeas.length > 0) {
        if (idea_ids.length === 0) {
          // remove all subproject_idea
          await this.sdk.rawQuery(`DELETE FROM equalityrecord_subproject_idea WHERE subproject_id = ${subproject_id};`);

          return {
            error: false,
            message: "Sub-project ideas updated successfully"
          };
        }

        existingIdeaIds = existingSubProjectIdeas.map((row) => Number(row.idea_id));

        // if existingIdeaIds = [1, 2, 3] & idea_ids [1, 2, 4] then delete 3 and add 4
        // if existingIdeaIds = [1, 2, 4] & idea_ids = [1, 2, 4] then delete all
        // if existingIdeaIds = [6, 7, 8] & idea_ids = [1, 2, 4] then delete all and add 1, 2, 4

        let insertIdeaIds = [];
        let deleteIdeaIds = [];
        // find the difference between existingIdeaIds and idea_ids
        for (let existingIdeaId of existingIdeaIds) {
          if (idea_ids.includes(existingIdeaId)) {
            continue;
          } else {
            deleteIdeaIds.push(existingIdeaId);
          }
        }

        for (let idea_id of idea_ids) {
          if (existingIdeaIds.includes(idea_id)) {
            continue;
          } else {
            insertIdeaIds.push(idea_id);
          }
        }

        if (deleteIdeaIds.length > 0) {
          if (deleteIdeaIds.length === 1) {
            await this.sdk.rawQuery(`
              DELETE FROM equalityrecord_subproject_idea WHERE subproject_id = ${subproject_id} AND idea_id = ${deleteIdeaIds[0]};
            `);
          } else if (deleteIdeaIds.length > 1) {
            await this.sdk.rawQuery(`
              DELETE FROM equalityrecord_subproject_idea WHERE subproject_id = ${subproject_id} AND idea_id IN (${deleteIdeaIds.join(",")});
            `);
          }
        }

        if (insertIdeaIds.length > 0) {
          let subProjectIdeaPayload = insertIdeaIds.map((idea_id) => {
            return {
              subproject_id: Number(subproject_id),
              idea_id: Number(idea_id),
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            };
          });

          await this.sdk.rawQuery(`
            INSERT INTO equalityrecord_subproject_idea (subproject_id, idea_id, create_at, update_at)
            VALUES ${subProjectIdeaPayload.map((idea) => `(${idea.subproject_id}, ${idea.idea_id}, '${idea.create_at}', '${idea.update_at}')`).join(",")};
          `);
        }
      } else {
        // if existingIdeaIds = [] & idea_ids = [1, 2, 4] then add 1, 2, 4
        if (idea_ids.length === 1) {
          let subProjectIdeaPayload = {
            subproject_id: Number(subproject_id),
            idea_id: Number(idea_ids[0]),
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          };

          await this.sdk.insert(subProjectIdeaPayload);
        } else if (idea_ids.length > 1) {
          let subProjectIdeaPayload = idea_ids.map((idea_id) => {
            return {
              subproject_id: Number(subproject_id),
              idea_id: Number(idea_id),
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            };
          });

          await this.sdk.rawQuery(`
            INSERT INTO equalityrecord_subproject_idea (subproject_id, idea_id, create_at, update_at)
            VALUES ${subProjectIdeaPayload.map((idea) => `(${idea.subproject_id}, ${idea.idea_id}, '${idea.create_at}', '${idea.update_at}')`).join(",")};
          `);
        }
      }

      return {
        error: false,
        message: "Sub-project ideas updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async addAndAssignSubProjectIdea(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("idea");

      const payload = {
        project_id: req.body.project_id,
        idea_key: req.body.idea_key,
        idea_value: req.body.idea_value,
        survey_id: req.body.survey_id ?? null,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      if (result) {
        this.sdk.setTable("subproject_idea");

        const subProjectIdeaPayload = {
          subproject_id: req.body.subproject_id,
          idea_id: result,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        };

        const subProjectIdeaResult = await this.sdk.insert(subProjectIdeaPayload);

        if (subProjectIdeaResult) {
          return {
            error: false,
            idea_id: result,
            message: "Sub-project idea created & assigned successfully"
          };
        }
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addAndAssignSubProjectIdeaForMultiSubProjects(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("idea");

      const subProjectIds = req.body.subproject_ids;

      const ideaExists = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (ideaExists.length > 0) {
        const ideaIds = ideaExists.map((idea) => idea.id);
        this.sdk.setTable("subproject_idea");

        if (subProjectIds.length == 1) {
          if (ideaIds.length === 1) {
            // find if the idea already assigned to the sub-project
            const exists = await this.sdk.get({
              subproject_id: Number(req.body.subproject_ids[0]),
              idea_id: Number(ideaIds[0])
            });
            if (exists.length > 0) {
              return {
                error: false,
                message: "Idea already assigned to the sub-project"
              };
            } else {
              let subProjectIdeaPayload = {
                subproject_id: Number(req.body.subproject_ids[0]),
                idea_id: Number(ideaIds[0]),
                create_at: sqlDateFormat(new Date()),
                update_at: sqlDateTimeFormat(new Date())
              };
              const subProjectIdeaResult = await this.sdk.insert(subProjectIdeaPayload);
              if (subProjectIdeaResult) {
                return {
                  error: false,
                  message: "Idea assigned to the sub-project successfully"
                };
              }
            }
          } else if (ideaIds.length > 0) {
            // iterate if the ideas
            for (let ideaId of ideaIds) {
              // find if the idea already assigned to the sub-project
              const exists = await this.sdk.get({
                subproject_id: Number(req.body.subproject_ids[0]),
                idea_id: Number(ideaId)
              });
              if (exists.length > 0) {
                continue;
              } else {
                let subProjectIdeaPayload = {
                  subproject_id: Number(req.body.subproject_ids[0]),
                  idea_id: Number(ideaId),
                  create_at: sqlDateFormat(new Date()),
                  update_at: sqlDateTimeFormat(new Date())
                };
                const subProjectIdeaResult = await this.sdk.insert(subProjectIdeaPayload);
                if (subProjectIdeaResult) {
                  continue;
                }
              }
            }

            return {
              error: false,
              message: "Idea assigned to the sub-project successfully"
            };
          }
        } else if (subProjectIds.length > 1) {
          if (ideaIds.length === 1) {
            // find if the idea already assigned to sub-projects
            for (let subProjectId of subProjectIds) {
              const exists = await this.sdk.get({
                subproject_id: Number(subProjectId),
                idea_id: Number(ideaIds[0])
              });
              if (exists.length > 0) {
                continue;
              } else {
                let subProjectIdeaPayload = {
                  subproject_id: Number(subProjectId),
                  idea_id: Number(ideaIds[0]),
                  create_at: sqlDateFormat(new Date()),
                  update_at: sqlDateTimeFormat(new Date())
                };
                const subProjectIdeaResult = await this.sdk.insert(subProjectIdeaPayload);
                if (subProjectIdeaResult) {
                  continue;
                }
              }
            }

            return {
              error: false,
              message: "Idea assigned to sub-projects successfully"
            };
          } else if (ideaIds.length > 0) {
            for (let ideaId of ideaIds) {
              // find if the idea already assigned to sub-projects
              for (let subProjectId of subProjectIds) {
                const exists = await this.sdk.get({
                  subproject_id: Number(subProjectId),
                  idea_id: Number(ideaId)
                });
                if (exists.length > 0) {
                  continue;
                } else {
                  let subProjectIdeaPayload = {
                    subproject_id: Number(subProjectId),
                    idea_id: Number(ideaId),
                    create_at: sqlDateFormat(new Date()),
                    update_at: sqlDateTimeFormat(new Date())
                  };
                  const subProjectIdeaResult = await this.sdk.insert(subProjectIdeaPayload);
                  if (subProjectIdeaResult) {
                    continue;
                  }
                }
              }
            }

            return {
              error: false,
              message: "Idea assigned to sub-projects successfully"
            };
          }
        }
      } else {
        return {
          error: true,
          message: "No idea found"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteSubProjectIdea(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject_idea");

      const exists = await this.sdk.get({
        idea_id: req.body.idea_id,
        subproject_id: req.body.subproject_id
      });

      if (exists.length === 0) {
        return {
          error: true,
          message: "Sub-project idea not found"
        };
      }

      await this.sdk.delete({}, exists[0].id);

      return {
        error: false,
        message: "Sub-project idea deleted successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteProjectIdea(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject_idea");

      const assignedToSubProjects = await this.sdk.get({
        idea_id: req.body.idea_id
      });

      if (assignedToSubProjects.length > 0) {
        return {
          error: true,
          message: "Project idea already assigned to sub-projects"
        };
      }

      this.sdk.setTable("idea");
      const exists = await this.sdk.get({
        id: req.body.idea_id,
        project_id: req.body.project_id
      });

      if (exists.length === 0) {
        return {
          error: true,
          message: "Project idea not found"
        };
      }

      await this.sdk.delete({}, exists[0].id);

      return {
        error: false,
        message: "Project idea deleted successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllUnAssignedSubProjects(req) {
    try {
      this.sdk = this.getSDK(req);

      // equalityrecord_subproject
      // id, project_id, type_name, create_at, update_at

      // equalityrecord_subproject_employee table
      // id, subproject_id, employee_id, employee_type, employee_cost

      let rawSql = `
        SELECT DISTINCT
          sp.*,
          p.team_name, c.program AS program_name
        FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
          INNER JOIN equalityrecord_subproject_employee spe ON spe.subproject_id = sp.id
        WHERE sp.workorder_id IS NULL
          AND spe.employee_type <> 'engineer'
          AND p.user_id = ${req.user_id}
          AND c.user_id = ${req.user_id};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        const subprojectIdeas = await this.sdk.rawQuery(`
          SELECT
            si.*,
            sp.type, sp.type_name, sp.project_id, sp.workorder_id,
            i.idea_key, i.idea_value, i.survey_id
          FROM equalityrecord_subproject_idea si
            LEFT JOIN equalityrecord_subproject sp ON si.subproject_id = sp.id
            LEFT JOIN equalityrecord_idea i ON si.idea_id = i.id
          WHERE si.subproject_id IN (${result.map((item) => item.id).join(", ")});
        `);

        result.forEach((resultItem) => {
          resultItem.subproject_ideas = subprojectIdeas.filter((ideaItem) => ideaItem.subproject_id === resultItem.id);
        });
      }

      if (result.length === 0) {
        return {
          error: false,
          list: []
        };
      } else {
        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllUnAssignedSubProjectsByWriterIdAndArtistId(req) {
    try {
      this.sdk = this.getSDK(req);

      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += ` AND (p.user_id = ${req.user_id}
        OR (c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id}) OR sp.user_id = ${req.user_id});`;
      }

      let rawSqlWriter = `
        SELECT
          sp.*,
          p.team_name, c.program AS program_name,
          spe.employee_type, spe.employee_id AS sp_emp_id
        FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
          INNER JOIN equalityrecord_subproject_employee spe ON spe.subproject_id = sp.id
        WHERE
          (spe.employee_id = ${req.body.writer_id} AND spe.employee_type = 'writer')
          AND sp.workorder_id IS NULL
          ${filterQuery}
       `;

      let rawSqlArtist = `
        SELECT
          sp.*,
          p.team_name, c.program AS program_name,
          spe.employee_type, spe.employee_id AS sp_emp_id
        FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
          INNER JOIN equalityrecord_subproject_employee spe ON spe.subproject_id = sp.id
        WHERE
          (spe.employee_id = ${req.body.artist_id} AND spe.employee_type = 'artist')
          AND sp.workorder_id IS NULL
          ${filterQuery}
        `;

      let resultWriter = await this.sdk.rawQuery(rawSqlWriter);
      let resultArtist = await this.sdk.rawQuery(rawSqlArtist);

      if (resultWriter.length === 0 || resultArtist.length === 0) {
        return {
          error: false,
          list: []
        };
      }

      let testIds = [];
      // iterate both resultWriter and resultArtist and match with id
      for (let resultWriterItem of resultWriter) {
        for (let resultArtistItem of resultArtist) {
          if (Number(resultWriterItem.id) === Number(resultArtistItem.id)) {
            testIds.push(resultWriterItem.id);
          }
        }
      }

      if (testIds.length === 0) {
        return {
          error: false,
          list: []
        };
      } else if (testIds.length == 1) {
        let result = await this.sdk.rawQuery(`
          SELECT
            sp.*,
            p.team_name, c.program AS program_name,
            spe.employee_type, spe.employee_id AS sp_emp_id
          FROM equalityrecord_subproject sp
            LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
            LEFT JOIN equalityrecord_client c ON p.client_id = c.id
            INNER JOIN equalityrecord_subproject_employee spe ON spe.subproject_id = sp.id
          WHERE sp.id = ${testIds[0]}
          ORDER BY p.mix_date ASC;
        `);

        const subprojectIdeas = await this.sdk.rawQuery(`
          SELECT
            si.*,
            sp.type, sp.type_name, sp.project_id, sp.workorder_id,
            i.idea_key, i.idea_value, i.survey_id
          FROM equalityrecord_subproject_idea si
            INNER JOIN equalityrecord_subproject sp ON si.subproject_id = sp.id
            INNER JOIN equalityrecord_idea i ON si.idea_id = i.id
          WHERE si.subproject_id = ${testIds[0]}
        `);

        // result[0].subproject_ideas = subprojectIdeas;
        result.forEach((subproject) => {
          subproject.subproject_ideas = subprojectIdeas.filter((ideaItem) => ideaItem.subproject_id === subproject.id);
        });

        return {
          error: false,
          list: result
        };
      } else if (testIds.length > 1) {
        let result = await this.sdk.rawQuery(`
          SELECT
            sp.*,
            p.team_name, c.program AS program_name,
            spe.employee_type, spe.employee_id AS sp_emp_id
          FROM equalityrecord_subproject sp
            LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
            LEFT JOIN equalityrecord_client c ON p.client_id = c.id
            INNER JOIN equalityrecord_subproject_employee spe ON spe.subproject_id = sp.id
          WHERE sp.id IN (${testIds.join(", ")})
          ORDER BY p.mix_date ASC;
        `);

        const subprojectIdeas = await this.sdk.rawQuery(`
          SELECT
            si.*,
            sp.type, sp.type_name, sp.project_id, sp.workorder_id,
            i.idea_key, i.idea_value, i.survey_id
          FROM equalityrecord_subproject_idea si
            INNER JOIN equalityrecord_subproject sp ON si.subproject_id = sp.id
            INNER JOIN equalityrecord_idea i ON si.idea_id = i.id
          WHERE si.subproject_id IN (${result.map((item) => item.id).join(", ")})
        `);

        result.forEach((subproject) => {
          subproject.subproject_ideas = subprojectIdeas.filter((ideaItem) => ideaItem.subproject_id === subproject.id);
        });

        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllLyrics(req) {
    try {
      this.sdk = this.getSDK(req);

      let projectId = req.params.project_id;

      const result = await this.sdk.rawQuery(`
        SELECT
          s.*, p.team_name, c.program AS program_name
        FROM equalityrecord_subproject s
          LEFT JOIN equalityrecord_project p ON s.project_id = p.id
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
        WHERE s.project_id = ${projectId};
      `);

      if (result.length === 0) {
        return {
          error: false,
          list: []
        };
      } else {
        return {
          error: false,
          list: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllMasterFiles(req) {
    try {
      this.sdk = this.getSDK(req);

      let projectId = req.params.project_id;

      const subProjects = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject WHERE project_id = ${projectId};
      `);

      let subProjectIds = [];

      if (subProjects.length > 0) {
        subProjectIds = subProjects.map((item) => item.id);
        let result = [];
        if (subProjectIds.length === 1) {
          result = await this.sdk.rawQuery(`
            SELECT
              * 
            FROM equalityrecord_project_file 
            WHERE subproject_id = ${subProjectIds[0]}
            AND project_id = ${projectId}
            AND employee_type = 'engineer'
            AND type = 'master';
          `);
        } else if (subProjectIds.length > 1) {
          result = await this.sdk.rawQuery(`
            SELECT
              * 
            FROM equalityrecord_project_file 
            WHERE subproject_id IN (${subProjectIds.join(", ")})
            AND project_id = ${projectId}
            AND employee_type = 'engineer'
            AND type = 'master';
          `);
        }

        return {
          error: false,
          list: result.length > 0 ? result : []
        };
      } else {
        return {
          error: false,
          list: []
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async masterView(req) {
    try {
      this.sdk = this.getSDK(req);

      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE p.user_id = ${req.user_id}`;
      }

      const limit = req.query.limit ? req.query.limit : 1000000;
      const offset = req.query.offset ? req.query.offset : 0;

      let result = await this.sdk.rawQuery(`
        SELECT p.*,
          c.program AS program_name, c.name AS program_owner_name,
          c.email AS program_owner_email,
          m.name AS mix_type_name,
          s.id AS survey_id,
          s.theme_of_the_routine AS theme_of_the_routine,
          s.status AS survey_status
        FROM equalityrecord_project p
          INNER JOIN equalityrecord_client c ON p.client_id = c.id
          INNER JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
          INNER JOIN equalityrecord_survey s ON p.id = s.project_id
        ${filterQuery}
        LIMIT ${limit} OFFSET ${offset}
      `);

      let allSubProjects = await this.sdk.rawQuery(`
        SELECT
          s.*,
          p.team_type,
          p.team_name,
          c.program AS program_name,
          wo.status AS workorder_status, wo.due_date AS workorder_due_date,
          wo.writer_id AS workorder_writer_id,
          wo.artist_id AS workorder_artist_id,
          wo.engineer_id AS workorder_engineer_id,
          wo.writer_submit_status, wo.artist_submit_status, wo.engineer_submit_status,
          wo.auto_approve AS workorder_auto_approve
        FROM equalityrecord_subproject s
        INNER JOIN equalityrecord_project p ON s.project_id = p.id
        INNER JOIN equalityrecord_client c ON p.client_id = c.id
        LEFT JOIN equalityrecord_work_order wo ON s.workorder_id = wo.id
        WHERE c.user_id = ${req.user_id} OR p.user_id = ${req.user_id};
      `);

      let allSubProjectEmployees = await this.sdk.rawQuery(`
        SELECT
          spe.*,
          e.name, e.email, e.is_writer, e.writer_cost, e.is_artist, e.artist_cost,
          e.is_engineer, e.engineer_cost, e.is_producer, e.producer_cost
        FROM equalityrecord_subproject_employee spe
        INNER JOIN equalityrecord_employee e ON spe.employee_id = e.id
      `);

      // find only idea count from equalityrecord_subproject_idea table
      // then add idea_count to allSubProjects
      let allSubProjectIdeas = await this.sdk.rawQuery(`
        SELECT
          si.subproject_id, COUNT(si.idea_id) AS idea_count, i.survey_id
        FROM equalityrecord_subproject_idea si
        LEFT JOIN equalityrecord_idea i ON si.idea_id = i.id
        GROUP BY si.subproject_id, i.survey_id;
      `);

      const filesQuery = `
        SELECT *
        FROM equalityrecord_project_file
        WHERE subproject_id IN (${allSubProjects.map((item) => item.id).join(",")})
        AND type = 'master'
        AND employee_type = 'engineer'
        `;
      const subprojectFiles = await this.sdk.rawQuery(filesQuery);
      // now iterate allSubProjects and allSubProjectEmployees and add employees to subprojects and subprojects to projects
      for (let i = 0; i < result.length; i++) {
        result[i].subprojects = allSubProjects.filter((row) => row.project_id === result[i].id);

        // add masters

        for (let j = 0; j < result[i].subprojects.length; j++) {
          result[i].subprojects[j].employees = allSubProjectEmployees.filter((row) => row.subproject_id === result[i].subprojects[j].id);
          result[i].subprojects[j].masters = subprojectFiles.filter((row) => row.subproject_id === result[i].subprojects[j].id);
          let subProjectHasIdeas = allSubProjectIdeas.find((row) => row.subproject_id === result[i].subprojects[j].id);

          if (result[i].survey_id && result[i].survey_status) {
            if (subProjectHasIdeas) {
              result[i].subprojects[j].idea_str = subProjectHasIdeas.idea_count && subProjectHasIdeas.idea_count > 0 ? "green" : "gray";
            } else {
              result[i].subprojects[j].idea_str = "gray";
            }
          } else {
            // console.log('subproject_id', result[i].subprojects[j].id);
            // console.log('subproject survey_status', result[i].survey_status);
            result[i].subprojects[j].idea_str = null;
          }
        }
      }

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async reassignSongs(req) {
    try {
      this.sdk = this.getSDK(req);

      let subproject_ids = req.body.subproject_ids; // [42, 89]
      let project_id = req.body.project_id;

      // update subproject
      // equalityrecord_subproject table:
      // id, type_name, is_song, project_id, ...

      // equalityrecord_project_file table:
      // id, subproject_id, project_id, ...

      // update equalityrecord_subproject & equalityrecord_project_file table
      // there can be multiple subprojects for one project
      // there can be multiple projects

      // update subproject
      if (subproject_ids.length === 1) {
        await this.sdk.rawQuery(`
          UPDATE equalityrecord_subproject
          SET project_id = ${project_id}
          WHERE id = ${subproject_ids[0]};
        `);

        await this.sdk.rawQuery(`
          UPDATE equalityrecord_project_file
          SET project_id = ${project_id}
          WHERE subproject_id = ${subproject_ids[0]};
        `);
      } else if (subproject_ids.length > 1) {
        await this.sdk.rawQuery(`
          UPDATE equalityrecord_subproject
          SET project_id = ${project_id}
          WHERE id IN (${subproject_ids.join(",")});
        `);

        await this.sdk.rawQuery(`
          UPDATE equalityrecord_project_file
          SET project_id = ${project_id}
          WHERE subproject_id IN (${subproject_ids.join(",")});
        `);
      } else {
        return {
          error: true,
          message: "No subprojects found"
        };
      }

      return {
        error: false,
        message: "Song(s) reassigned successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllProjectsForClient(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   client_id: 1, // aka program
      //   team_name: "Team 1",
      //   mix_date_start: "2021-01-01",
      //   mix_date_end: "2021-12-31",
      //   mix_type_id: 1,
      //   team_type: 1, // 1 = All Girls, 2 = Coed
      // };

      let filterQuery = "";
      // if (req.role === "client") {
      //   if (filter.is_impersonate) {
      //     filterQuery += `WHERE p.client_id = ${filter.static_user_id}`;
      //   } else {
      //     filterQuery += `WHERE p.client_id = ${req.body.client_id}`;
      //   }
      // }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (req.role === "client") {
          if (filter.is_impersonate) {
            filterArr.push(` p.client_id = ${filter.static_user_id} `);
          } else {
            filterArr.push(` p.client_id = ${filter.client_id} `);
          }
        }
        if (filter.team_name) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_name.toLowerCase()}%' `);
        }
        if (filter.team_names && filter.team_names.length === 1) {
          filterArr.push(` LOWER(p.team_name) LIKE '%${filter.team_names[0].toLowerCase()}%' `);
        } else if (filter.team_names && filter.team_names.length > 1) {
          filterArr.push(` LOWER(p.team_name) IN (${filter.team_names.map((team_name) => `'${team_name.toLowerCase()}'`).join(",")}) `);
        }
        if (filter.mix_type_ids && filter.mix_type_ids.length === 1) {
          filterArr.push(` p.mix_type_id = ${filter.mix_type_ids[0]} `);
        } else if (filter.mix_type_ids && filter.mix_type_ids.length > 1) {
          filterArr.push(` p.mix_type_id IN (${filter.mix_type_ids.join(",")}) `);
        }
        if (filter.mix_date_start) {
          filterArr.push(` p.mix_date >= '${filter.mix_date_start}' `);
        }
        if (filter.mix_date_end) {
          filterArr.push(` p.mix_date <= '${filter.mix_date_end}' `);
        }
        if (filter.team_type) {
          filterArr.push(` p.team_type = ${filter.team_type} `);
        }
        if (filter.team_details_date) {
          filterArr.push(` p.team_details_date = '${filter.team_details_date}' `);
        }
        if (filter.routine_submission_date) {
          filterArr.push(` p.routine_submission_date = '${filter.routine_submission_date}' `);
        }
        if (filter.estimated_delivery_date) {
          filterArr.push(` p.estimated_delivery_date = '${filter.estimated_delivery_date}' `);
        }
        if (filter.payment_status) {
          filterArr.push(` p.payment_status = ${filter.payment_status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_project p
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status,
        CONCAT(u.first_name, ' ', u.last_name) AS producer_name,
        u.subscription
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      ${filterQuery}
      ORDER BY p.mix_date ASC, p.id ASC
      LIMIT ${limit} OFFSET ${offset};`;

      // we may not get subproject employee cost
      // so we need to calculate it manually

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        // iterate result to calculate total from subproject_employee
        for (let i = 0; i < result.length; i++) {
          let subProjectRawSql = `
          SELECT s.*, COALESCE(se.total_employee_cost, 0) AS expenses
          FROM equalityrecord_subproject s
          LEFT JOIN (
            SELECT se.subproject_id, SUM(COALESCE(se.employee_cost, 0)) AS total_employee_cost
            FROM equalityrecord_subproject_employee se
            GROUP BY se.subproject_id
          ) se ON s.id = se.subproject_id
          WHERE s.project_id = ${result[i].id}
          ORDER BY s.id ASC
        `;

          let subProjectResult = await this.sdk.rawQuery(subProjectRawSql);
          result[i].subprojects = subProjectResult ?? [];

          // calculate total from subproject
          let total = 0;
          if (subProjectResult.length > 0) {
            for (let j = 0; j < subProjectResult.length; j++) {
              total += subProjectResult[j].expenses;
            }
          }

          result[i].expenses = total;

          // team_details
          let teamDetailsRawSql = `
            SELECT id
            FROM equalityrecord_team_details
            WHERE project_id = ${result[i].id}
          `;

          let teamDetailsResult = await this.sdk.rawQuery(teamDetailsRawSql);

          if (teamDetailsResult.length > 0) {
            result[i].team_details_found = true;
          } else {
            result[i].team_details_found = false;
          }

          // eight_count
          let eightCountRawSql = `
            SELECT id
            FROM equalityrecord_eight_count
            WHERE project_id = ${result[i].id}
          `;
          let eightCountResult = await this.sdk.rawQuery(eightCountRawSql);

          if (eightCountResult.length > 0) {
            result[i].eight_count_found = true;
          } else {
            result[i].eight_count_found = false;
          }

          // media
          let mediaRawSql = `
            SELECT id, is_music
            FROM equalityrecord_media
            WHERE project_id = ${result[i].id}
          `;
          let mediaResult = await this.sdk.rawQuery(mediaRawSql);

          if (mediaResult.length > 0) {
            result[i].media_found = true;
            // now detect is_music
            let is_music = mediaResult.find((item) => item.is_music === 1);
            if (is_music) {
              result[i].is_music_found = true;
            } else {
              result[i].is_music_found = false;
            }
          } else {
            result[i].media_found = false;
            result[i].is_music_found = false;
          }

          // company_info
          let companyInfoRawSql = `
            SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
              p.company_name, p.office_email, p.company_logo, p.license_company_logo
            FROM equalityrecord_user u
            LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
            WHERE u.id = ${result[i].user_id}
            `;

          let companyInfoResult = await this.sdk.rawQuery(companyInfoRawSql);

          if (companyInfoResult.length > 0) {
            result[i].company_info = companyInfoResult[0];
          } else {
            result[i].company_info = null;
          }
        }

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async createOrUpdateTeamDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("team_details");

      let id = req.body.id;
      if (id) {
        const exists = await this.sdk.get({ id });
        if (exists.length === 0) {
          return {
            error: false,
            id: null,
            message: "Team details not found"
          };
        }

        await this.sdk.update(
          filterEmptyFields({
            notes: req.body.notes,
            song_list: req.body.song_list,
            colors: req.body.colors,
            mascot: req.body.mascot,
            social_media: req.body.social_media,
            update_at: sqlDateTimeFormat(new Date())
          }),
          Number(id)
        );

        return {
          error: false,
          id: Number(id),
          message: "Team details updated successfully"
        };
      } else {
        const result = await this.sdk.insert({
          project_id: req.body.project_id,
          notes: req.body.notes ?? null,
          song_list: req.body.song_list ?? null,
          colors: req.body.colors ?? null,
          mascot: req.body.mascot ?? null,
          social_media: req.body.social_media ?? null,
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        if (result) {
          return {
            error: false,
            id: result,
            message: "Team details created successfully"
          };
        }
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewTeamDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("team_details");
      const result = await this.sdk.get({ project_id: req.params.id });
      return result[0];
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOneTeamDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("team_details");

      const result = await this.sdk.get({ id: req.params.id });
      console.log("result", result);

      if (result.length > 0) {
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Team details deleted successfully."
        };
      } else {
        return {
          error: true,
          message: "Team details not found."
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewProjectDetailsForClient(req) {
    try {
      this.sdk = this.getSDK(req);

      let filterQuery = "";
      // if (req.role !== 'admin') {
      //   filterQuery += `WHERE p.id = ${req.params.id} AND p.user_id = ${req.user_id}`;
      // } else {
      //   filterQuery += `WHERE p.id = ${req.params.id} `;
      // }

      if (req.role === "client") {
        filterQuery += `WHERE p.id = ${req.params.id} `;
      } else {
        return {
          error: true,
          message: "Access denied"
        };
      }

      const filter = req.body.filter ?? {};

      let filterArr = [];
      if (filter.client_ids && filter.client_ids.length > 0) {
        filterArr.push(`p.client_id IN (${filter.client_ids.join(",")})`);
      }
      if (filter.team_names && filter.team_names.length > 0) {
        filterArr.push(`LOWER(p.team_name) IN (${filter.team_names.map((name) => `'${name.toLowerCase()}'`).join(",")})`);
      }
      if (filter.mix_date_start) {
        filterArr.push(`p.mix_date >= '${filter.mix_date_start}'`);
      }
      if (filter.mix_date_end) {
        filterArr.push(`p.mix_date <= '${filter.mix_date_end}'`);
      }
      if (filter.mix_type_ids && filter.mix_type_ids.length > 0) {
        filterArr.push(`p.mix_type_id IN (${filter.mix_type_ids.join(",")})`);
      }
      if (filter.team_type) {
        filterArr.push(`p.team_type = ${filter.team_type}`);
      }
      if (filter.payment_status) {
        filterArr.push(`p.payment_status = ${filter.payment_status}`);
      }
      if (filter.payment_status_without_completed) {
        filterArr.push(`p.payment_status IN (2, 3, 4, 5)`);
      }

      filterQuery = filterArr.length > 0 ? filterQuery + ` AND ${filterArr.join(" AND ")}` : filterQuery;

      const additionalFilterQuery = filterArr.length > 0 ? `WHERE ${filterArr.join(" AND ")}` : "";

      let result = await this.sdk.rawQuery(`
        SELECT p.*,
          c.program AS program_name, c.name AS program_owner_name, c.position AS program_owner_position,
          c.email AS program_owner_email, c.phone AS program_owner_phone,
          m.name AS mix_type_name, m.price AS total,
          ms.name AS mix_season_name,
          COALESCE(se.total_employee_cost, 0) AS expenses,
          s.id AS survey_id, s.uuidv4, s.theme_of_the_routine,
          s.overall_description, s.status AS survey_status,
          ep.office_email,
          CONCAT(u.first_name, ' ', u.last_name) AS producer_name
        FROM equalityrecord_project p
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
          LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
          LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
          LEFT JOIN equalityrecord_mix_season ms ON p.mix_season_id = ms.id
          LEFT JOIN equalityrecord_profile ep ON c.user_id = ep.user_id
          LEFT JOIN equalityrecord_user u ON p.user_id = u.id
          LEFT JOIN (
            SELECT s.project_id, SUM(se.employee_cost) AS total_employee_cost
            FROM equalityrecord_subproject s
            LEFT JOIN equalityrecord_subproject_employee se ON s.id = se.subproject_id
            GROUP BY s.project_id
          ) se ON p.id = se.project_id
          ${filterQuery}
      `);

      // const nextProject = await this.sdk.rawQuery(`
      //   SELECT id AS next_project_id
      //     FROM equalityrecord_project
      //   WHERE id != ${req.params.id}
      //     AND mix_date > '${result[0].mix_date}'
      //     AND client_id = ${result[0].client_id}
      //   ORDER BY mix_date
      //   ASC LIMIT 1;
      // `);

      // const previousProject = await this.sdk.rawQuery(`
      //   SELECT id AS previous_project_id
      //     FROM equalityrecord_project
      //   WHERE id != ${req.params.id}
      //     AND mix_date < '${result[0].mix_date}'
      //     AND client_id = ${result[0].client_id}
      //   ORDER BY mix_date
      //   DESC LIMIT 1;
      // `);

      // if (nextProject.length > 0) {
      //   result[0].next_project_id = nextProject[0].next_project_id;
      // } else {
      //   result[0].next_project_id = null;
      // }

      // if (previousProject.length > 0) {
      //   result[0].previous_project_id = previousProject[0].previous_project_id;
      // } else {
      //   result[0].previous_project_id = null;
      // }

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      ${additionalFilterQuery}
      ORDER BY p.mix_date ASC, p.id ASC`;

      let projectList = await this.sdk.rawQuery(rawSql);

      // find THE POSITION of current project
      const currentProjectPosition = projectList.findIndex((project) => project.id === result[0].id);
      result[0].next_project_id = currentProjectPosition > -1 ? projectList[currentProjectPosition + 1]?.id : null;

      // find previous project
      result[0].previous_project_id = currentProjectPosition > 0 ? projectList[currentProjectPosition - 1]?.id : null;

      // company_info
      let companyInfoRawSql = `
        SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
          p.company_name, p.office_email, p.company_logo, p.license_company_logo,
          CASE 
            WHEN mp.member_ids IS NOT NULL 
            THEN JSON_EXTRACT(mp.member_ids, '$[0]')
            ELSE NULL 
          END AS main_member_id
        FROM equalityrecord_user u
        LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
        LEFT JOIN equalityrecord_manager_permission mp ON (JSON_CONTAINS(mp.member_ids, CAST(u.id AS CHAR)) OR mp.manager_id = u.id)
        WHERE u.id = ${result[0].user_id}
      `;

      let companyInfoResult = await this.sdk.rawQuery(companyInfoRawSql);

      if (companyInfoResult.length > 0) {
        result[0].company_info = companyInfoResult[0];
      } else {
        result[0].company_info = null;
      }

      return {
        error: false,
        model: result[0]
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewProjectDetailsForAdmin(req) {
    try {
      this.sdk = this.getSDK(req);

      let filterQuery = "";
      // if (req.role !== 'admin') {
      //   filterQuery += `WHERE p.id = ${req.params.id} AND p.user_id = ${req.user_id}`;
      // } else {
      //   filterQuery += `WHERE p.id = ${req.params.id} `;
      // }

      if (req.role === "admin") {
        filterQuery += `WHERE p.id = ${req.params.id} `;
      } else {
        return {
          error: true,
          message: "Access denied"
        };
      }

      const filter = req.body.filter ?? {};

      let filterArr = [];
      if (filter.client_ids && filter.client_ids.length > 0) {
        filterArr.push(`p.client_id IN (${filter.client_ids.join(",")})`);
      }
      if (filter.team_names && filter.team_names.length > 0) {
        filterArr.push(`LOWER(p.team_name) IN (${filter.team_names.map((name) => `'${name.toLowerCase()}'`).join(",")})`);
      }
      if (filter.mix_date_start) {
        filterArr.push(`p.mix_date >= '${filter.mix_date_start}'`);
      }
      if (filter.mix_date_end) {
        filterArr.push(`p.mix_date <= '${filter.mix_date_end}'`);
      }
      if (filter.mix_type_ids && filter.mix_type_ids.length > 0) {
        filterArr.push(`p.mix_type_id IN (${filter.mix_type_ids.join(",")})`);
      }
      if (filter.team_type) {
        filterArr.push(`p.team_type = ${filter.team_type}`);
      }
      if (filter.payment_status) {
        filterArr.push(`p.payment_status = ${filter.payment_status}`);
      }
      if (filter.payment_status_without_completed) {
        filterArr.push(`p.payment_status IN (2, 3, 4, 5)`);
      }

      filterQuery = filterArr.length > 0 ? filterQuery + ` AND ${filterArr.join(" AND ")}` : filterQuery;

      const additionalFilterQuery = filterArr.length > 0 ? `WHERE ${filterArr.join(" AND ")}` : "";

      let result = await this.sdk.rawQuery(`
        SELECT p.*,
          c.program AS program_name, c.name AS program_owner_name, c.position AS program_owner_position,
          c.email AS program_owner_email, c.phone AS program_owner_phone,
          m.name AS mix_type_name, m.price AS total,
          ms.name AS mix_season_name,
          COALESCE(se.total_employee_cost, 0) AS expenses,
          s.id AS survey_id, s.uuidv4, s.theme_of_the_routine,
          s.overall_description, s.status AS survey_status,
          ep.office_email,
          CONCAT(u.first_name, ' ', u.last_name) AS producer_name
        FROM equalityrecord_project p
          LEFT JOIN equalityrecord_client c ON p.client_id = c.id
          LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
          LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
          LEFT JOIN equalityrecord_mix_season ms ON p.mix_season_id = ms.id
          LEFT JOIN equalityrecord_profile ep ON c.user_id = ep.user_id
          LEFT JOIN equalityrecord_user u ON p.user_id = u.id
          LEFT JOIN (
            SELECT s.project_id, SUM(se.employee_cost) AS total_employee_cost
            FROM equalityrecord_subproject s
            LEFT JOIN equalityrecord_subproject_employee se ON s.id = se.subproject_id
            GROUP BY s.project_id
          ) se ON p.id = se.project_id
          ${filterQuery}
      `);

      /*

      const nextProject = await this.sdk.rawQuery(`
        SELECT id AS next_project_id
          FROM equalityrecord_project
        WHERE id != ${req.params.id}
          AND mix_date > '${result[0].mix_date}'
          AND client_id = ${result[0].client_id}
          ${additionalFilterQuery}
        ORDER BY mix_date
        ASC LIMIT 1;
      `);

      const previousProject = await this.sdk.rawQuery(`
        SELECT id AS previous_project_id
          FROM equalityrecord_project
        WHERE id != ${req.params.id}
          AND mix_date < '${result[0].mix_date}'
          AND client_id = ${result[0].client_id}
          ${additionalFilterQuery}
        ORDER BY mix_date
        DESC LIMIT 1;
      `);

      if (nextProject.length > 0) {
        result[0].next_project_id = nextProject[0].next_project_id;
      } else {
        result[0].next_project_id = null;
      }

      if (previousProject.length > 0) {
        result[0].previous_project_id = previousProject[0].previous_project_id;
      } else {
        result[0].previous_project_id = null;
      }
        */

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      ${additionalFilterQuery}
      ORDER BY p.mix_date ASC, p.id ASC`;

      let projectList = await this.sdk.rawQuery(rawSql);

      // find THE POSITION of current project
      const currentProjectPosition = projectList.findIndex((project) => project.id === result[0].id);
      result[0].next_project_id = currentProjectPosition > -1 ? projectList[currentProjectPosition + 1]?.id : null;

      // find previous project
      result[0].previous_project_id = currentProjectPosition > 0 ? projectList[currentProjectPosition - 1]?.id : null;

      // company_info
      let companyInfoRawSql = `
        SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
          p.company_name, p.office_email, p.company_logo, p.license_company_logo,
          CASE 
            WHEN mp.member_ids IS NOT NULL 
            THEN JSON_EXTRACT(mp.member_ids, '$[0]')
            ELSE NULL 
          END AS main_member_id
        FROM equalityrecord_user u
        LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
        WHERE u.id = ${result[0].user_id}
      `;

      let companyInfoResult = await this.sdk.rawQuery(companyInfoRawSql);

      if (companyInfoResult.length > 0) {
        result[0].company_info = companyInfoResult[0];
      } else {
        result[0].company_info = null;
      }

      return {
        error: false,
        model: result[0]
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewProjectDetailsForManager(req) {
    try {
      this.sdk = this.getSDK(req);

      // Role check: only allow access if role is "manager"
      if (req.role !== "manager") {
        return {
          error: true,
          message: "Access denied"
        };
      }

      const projectId = req.params.id;

      const filter = req.body.filter ?? {};

      let filterArr = [];
      if (filter.client_ids && filter.client_ids.length > 0) {
        filterArr.push(`p.client_id IN (${filter.client_ids.join(",")})`);
      }
      if (filter.team_names && filter.team_names.length > 0) {
        filterArr.push(`LOWER(p.team_name) IN (${filter.team_names.map((name) => `'${name.toLowerCase()}'`).join(",")})`);
      }
      if (filter.mix_date_start) {
        filterArr.push(`p.mix_date >= '${filter.mix_date_start}'`);
      }
      if (filter.mix_date_end) {
        filterArr.push(`p.mix_date <= '${filter.mix_date_end}'`);
      }
      if (filter.mix_type_ids && filter.mix_type_ids.length > 0) {
        filterArr.push(`p.mix_type_id IN (${filter.mix_type_ids.join(",")})`);
      }
      if (filter.team_type) {
        filterArr.push(`p.team_type = ${filter.team_type} `);
      }
      if (filter.payment_status) {
        filterArr.push(`p.payment_status = ${filter.payment_status} `);
      }
      if (filter.payment_status_without_completed) {
        filterArr.push(`p.payment_status IN (2, 3, 4, 5)`);
      }

      const additionalFilterQuery = filterArr.length > 0 ? ` WHERE ${filterArr.join(" AND ")}` : "";

      // Define the project query with necessary joins and filters
      const projectQuery = `
      SELECT p.*, 
        c.program AS program_name, c.name AS program_owner_name, 
        c.position AS program_owner_position, c.email AS program_owner_email, 
        c.phone AS program_owner_phone,
        m.name AS mix_type_name, m.price AS total,
        ms.name AS mix_season_name,
        COALESCE(se.total_employee_cost, 0) AS expenses,
        s.id AS survey_id, s.uuidv4, s.theme_of_the_routine,
        s.overall_description, s.status AS survey_status,
        ep.office_email,
        CONCAT(u.first_name, ' ', u.last_name) AS producer_name
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      LEFT JOIN equalityrecord_mix_season ms ON p.mix_season_id = ms.id
      LEFT JOIN equalityrecord_profile ep ON c.user_id = ep.user_id
      LEFT JOIN equalityrecord_user u ON p.user_id = u.id
      LEFT JOIN (
        SELECT s.project_id, SUM(se.employee_cost) AS total_employee_cost
        FROM equalityrecord_subproject s
        LEFT JOIN equalityrecord_subproject_employee se ON s.id = se.subproject_id
        GROUP BY s.project_id
      ) se ON p.id = se.project_id
      WHERE p.id = ${projectId}
    `;

      // Fetch project details
      const projectResult = await this.sdk.rawQuery(projectQuery);
      if (!projectResult.length) {
        return { error: true, message: "Project not found" };
      }
      const projectData = projectResult[0];

      const managerMembers = await this.sdk.rawQuery(`
          SELECT mp.member_ids FROM equalityrecord_manager_permission mp WHERE mp.manager_id = ${req.user_id};
      `);

      if (managerMembers.length === 0) {
        return { error: true, message: "You are not a manager of any project" };
      }

      const memberIds = JSON.parse(managerMembers[0].member_ids).map((id) => Number(id));

      if (!memberIds.includes(projectResult[0].user_id)) {
        return { error: true, message: "You are not a manager of this project" };
      }

      // Fetch next project ID based on user_id instead of client_id
      //   const nextProjectQuery = `
      //   SELECT id AS next_project_id
      //   FROM equalityrecord_project
      //   WHERE id != ${projectId}
      //     AND mix_date > '${projectData.mix_date}'
      //     AND user_id = ${projectData.user_id}
      //     ${additionalFilterQuery}
      //   ORDER BY mix_date ASC LIMIT 1;
      // `;
      //   const nextProject = await this.sdk.rawQuery(nextProjectQuery);
      //   projectData.next_project_id = nextProject.length > 0 ? nextProject[0].next_project_id : null;

      //   // Fetch previous project ID based on user_id instead of client_id
      //   const previousProjectQuery = `
      //   SELECT id AS previous_project_id
      //   FROM equalityrecord_project
      //   WHERE id != ${projectId}
      //     AND mix_date < '${projectData.mix_date}'
      //     AND user_id = ${projectData.user_id}
      //     ${additionalFilterQuery}
      //   ORDER BY mix_date DESC LIMIT 1;
      // `;
      //   const previousProject = await this.sdk.rawQuery(previousProjectQuery);
      //   projectData.previous_project_id = previousProject.length > 0 ? previousProject[0].previous_project_id : null;

      const rawSql = `SELECT p.*,
        c.program AS program_name,
        c.name AS program_owner_name,
        c.position AS program_owner_position,
        c.email AS program_owner_email,
        c.phone AS program_owner_phone,
        m.name AS mix_type_name,
        m.price AS total,
        m.color AS mix_type_color,
        s.status AS survey_status
      FROM equalityrecord_project p
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
      LEFT JOIN equalityrecord_survey s ON p.id = s.project_id
      ${additionalFilterQuery}
      ORDER BY p.mix_date ASC, p.id ASC`;

      let projectList = await this.sdk.rawQuery(rawSql);

      // find THE POSITION of current project
      const currentProjectPosition = projectList.findIndex((project) => project.id === projectResult[0].id);
      projectResult[0].next_project_id = currentProjectPosition > -1 ? projectList[currentProjectPosition + 1]?.id : null;

      // find previous project
      projectResult[0].previous_project_id = currentProjectPosition > 0 ? projectList[currentProjectPosition - 1]?.id : null;

      // Fetch company information
      const companyInfoQuery = `
      SELECT CONCAT(u.first_name, ' ', u.last_name) AS member_name,
        p.company_name, p.office_email, p.company_logo, p.license_company_logo,
        CASE 
          WHEN mp.member_ids IS NOT NULL 
          THEN JSON_EXTRACT(mp.member_ids, '$[0]')
          ELSE NULL 
        END AS main_member_id
      FROM equalityrecord_user u
      LEFT JOIN equalityrecord_profile p ON u.id = p.user_id
      LEFT JOIN equalityrecord_manager_permission mp ON (JSON_CONTAINS(mp.member_ids, CAST(u.id AS CHAR)) OR mp.manager_id = u.id)
      WHERE u.id = ${projectData.user_id}
    `;
      const companyInfo = await this.sdk.rawQuery(companyInfoQuery);
      projectData.company_info = companyInfo.length > 0 ? companyInfo[0] : null;

      return {
        error: false,
        model: projectData
      };
    } catch (error) {
      console.error("Error in viewProjectDetailsForManager:", error.message);
      return {
        error: true,
        message: "An error occurred while retrieving project details."
      };
    }
  }

  async getSubProjectWithTypeName(req) {
    try {
      this.sdk = this.getSDK(req);

      const filter = req.body.filter ?? [];
      const employee_filter = req.body.employee_filter ?? [];
      const client_filter = req.body.client_filter ?? [];
      const filterArr = ["sp.is_song = 1"];
      const columnsDirection = req.body.columnsDirection ?? [];

      if (filter.length > 0) {
        filterArr.push(...filter);
      }

      let { page = 1, limit = 10 } = req.body;
      page = Number(page);
      limit = Number(limit);
      const offset = (page - 1) * limit;

      const whereClause = filterArr.length > 0 ? ` AND ${filterArr.join(" AND ")}` : "";
      const employee_whereClause = employee_filter.length > 0 ? `AND e.${employee_filter.join(" AND ")}` : "";
      const client_whereClause = client_filter.length > 0 ? `AND c.${client_filter.join(" AND ")}` : "";

      // Build ORDER BY clause based on columnsDirection
      const buildOrderByClause = (columnsDirection) => {
        if (!columnsDirection || columnsDirection.length === 0) {
          return "ORDER BY sp.id DESC";
        }

        // Map frontend column names to actual database column references
        const columnMapping = {
          id: "sp.id",
          program: "c.program",
          project_name: "c.program",
          team_name: "p.team_name",
          writer_submit_status: "wo.writer_submit_status",
          workorder_status: "wo.status",
          workorder_auto_approve: "wo.auto_approve",
          survey_id: "s.id",
          uuidv4: "s.uuidv4",
          theme_of_the_routine: "s.theme_of_the_routine",
          overall_description: "s.overall_description",
          survey_status: "s.status",
          created_at: "sp.created_at",
          updated_at: "sp.updated_at"
        };

        const orderByItems = columnsDirection.map((item) => {
          const column = item.column;
          const direction = item.direction && item.direction.toLowerCase() === "asc" ? "ASC" : "DESC";

          // Use mapped column name or fallback to sp.column if not found in mapping
          const dbColumn = columnMapping[column] || `sp.${column}`;

          return `${dbColumn} ${direction}`;
        });

        return `ORDER BY ${orderByItems.join(", ")}`;
      };

      const orderByClause = buildOrderByClause(columnsDirection);

      // Subquery to get filtered subproject IDs based on employee criteria
      const subprojectSubquery =
        employee_filter.length > 0
          ? `
      WITH filtered_subprojects AS (
        SELECT DISTINCT sp.id
        FROM equalityrecord_subproject sp
        LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
        LEFT JOIN equalityrecord_client c ON p.client_id = c.id
        INNER JOIN equalityrecord_subproject_employee spe ON sp.id = spe.subproject_id
        INNER JOIN equalityrecord_employee e ON spe.employee_id = e.id
        WHERE ( sp.user_id = ${req.user_id} OR p.user_id = ${req.user_id} OR c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id})
        ${whereClause} 
        ${employee_whereClause ? `${employee_whereClause}` : ""}
        ${client_whereClause ? `${client_whereClause}` : ""}
      )
    `
          : "";

      // Modify main query to use the filtered subprojects
      const countQuery = `
    ${subprojectSubquery}
    SELECT COUNT(DISTINCT sp.id) as total 
    FROM equalityrecord_subproject sp
    LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
    LEFT JOIN equalityrecord_client c ON p.client_id = c.id
    ${
      employee_filter.length > 0
        ? `INNER JOIN filtered_subprojects fs ON sp.id = fs.id 
        WHERE ( sp.user_id = ${req.user_id} OR p.user_id = ${req.user_id} OR c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id})
         `
        : ` 
        WHERE ( sp.user_id = ${req.user_id} OR p.user_id = ${req.user_id} OR c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id})
      ${whereClause}
    `
    }
  `;

      const mainQuery = `
    ${subprojectSubquery}
    SELECT 
      sp.*,
      c.program AS project_name,
      p.team_name AS team_name,
      wo.writer_submit_status,
      wo.status AS workorder_status, 
      wo.auto_approve AS workorder_auto_approve,
      s.id AS survey_id, s.uuidv4, s.theme_of_the_routine,
      s.overall_description, s.status AS survey_status
    FROM equalityrecord_subproject sp
      LEFT JOIN equalityrecord_project p ON sp.project_id = p.id
      LEFT JOIN equalityrecord_client c ON p.client_id = c.id
      LEFT JOIN equalityrecord_work_order wo ON sp.workorder_id = wo.id
      LEFT JOIN equalityrecord_survey s ON sp.project_id = s.project_id
      ${
        employee_filter.length > 0
          ? `INNER JOIN filtered_subprojects fs ON sp.id = fs.id 
      WHERE ( sp.user_id = ${req.user_id} OR p.user_id = ${req.user_id} OR c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id})
        `
          : `WHERE ( sp.user_id = ${req.user_id} OR p.user_id = ${req.user_id} OR c.user_id = ${req.user_id} OR c.member_two_user_id = ${req.user_id})
        ${whereClause}`
      }
    GROUP BY sp.id
    ${orderByClause}
    LIMIT ${limit} OFFSET ${offset}
  `;

      const totalResult = await this.sdk.rawQuery(countQuery);
      const total = totalResult[0].total;

      let result = await this.sdk.rawQuery(mainQuery);

      if (result.length > 0) {
        // Get all subproject IDs
        const subprojectIds = result.map((item) => item.id);

        // Batch fetch employees for all subprojects
        const employeesQuery = `
        SELECT 
          e.*, 
          spe.employee_cost AS emp_cost, 
          spe.employee_type AS emp_type,
          spe.subproject_id
        FROM equalityrecord_subproject_employee spe
        INNER JOIN equalityrecord_employee e ON spe.employee_id = e.id
        WHERE spe.subproject_id IN (${subprojectIds.join(",")})
        ${employee_whereClause}
      `;
        const employees = await this.sdk.rawQuery(employeesQuery);

        // Batch fetch all files
        const filesQuery = `
        SELECT *
        FROM equalityrecord_project_file
        WHERE subproject_id IN (${subprojectIds.join(",")})
      `;
        const allFiles = await this.sdk.rawQuery(filesQuery);

        let allSubProjectIdeas = await this.sdk.rawQuery(`
      SELECT
        si.subproject_id, COUNT(si.idea_id) AS idea_count, i.survey_id
      FROM equalityrecord_subproject_idea si
      LEFT JOIN equalityrecord_idea i ON si.idea_id = i.id
      GROUP BY si.subproject_id, i.survey_id;
    `);

        // Process results
        result = result.map((resultItem) => {
          // Map employees to their subprojects
          resultItem.employees = employees.filter((emp) => emp.subproject_id === resultItem.id);

          // Map files to their categories
          const subprojectFiles = allFiles.filter((file) => file.subproject_id === resultItem.id);

          resultItem.admin_writer_instrumentals = subprojectFiles.filter(
            (file) => file.employee_type === "writer" && file.type === "instrumental" && file.is_from_admin === 1
          );

          resultItem.admin_producer_instrumentals = subprojectFiles.filter(
            (file) => file.employee_type === "producer" && file.type === "instrumental" && file.is_from_admin === 1
          );

          resultItem.producer_files = subprojectFiles.filter(
            (file) => file.employee_type === "producer" && file.type === "instrumental" && file.is_from_admin === 0
          );

          resultItem.demos = subprojectFiles.filter((file) => file.employee_type === "writer" && file.type === "demo");

          resultItem.masters = subprojectFiles.filter((file) => file.employee_type === "engineer" && file.type === "master");
          return resultItem;
        });

        // idea_str
        for (let i = 0; i < result.length; i++) {
          let subProjectHasIdeas = allSubProjectIdeas.find((row) => row.subproject_id === result[i].id);

          if (result[i].survey_id && result[i].survey_status) {
            if (subProjectHasIdeas) {
              result[i].idea_str = subProjectHasIdeas.idea_count && subProjectHasIdeas.idea_count > 0 ? "green" : "gray";
            } else {
              result[i].idea_str = "gray";
            }
          } else {
            result[i].idea_str = null;
          }
        }
      }

      return { result, total, page, limit };
    } catch (error) {
      throw new Error(error);
    }
  }
  async getProjectCountsByMixSeason(req) {
    try {
      this.sdk = this.getSDK(req);

      // Get user's subscription info with plan type (monthly or yearly)
      const subQuery = `
        SELECT 
          s.create_at as subscription_start,
          p.is_yearly
        FROM equalityrecord_stripe_subscription s
        JOIN equalityrecord_profile prof ON prof.subscription_id = s.id
        JOIN equalityrecord_stripe_price p ON s.price_id = p.id
        WHERE prof.user_id = ${req.user_id}
        LIMIT 1
      `;

      const subResult = await this.sdk.rawQuery(subQuery);

      if (!subResult.length) {
        return { error: true, message: "No active subscription found" };
      }

      const { subscription_start, is_yearly } = subResult[0];

      // Calculate current period based on subscription start date and whether it's yearly or monthly
      const startDate = new Date(subscription_start);
      let endDate;

      // Calculate the current period end date
      if (is_yearly == 1) {
        // For yearly subscriptions, end date is 1 year after start date
        endDate = new Date(startDate);
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        // For monthly subscriptions, end date is 1 month after start date
        endDate = new Date(startDate);
        endDate.setMonth(endDate.getMonth() + 1);
      }

      // Format dates for SQL queries
      const formatDate = (date) => {
        return date.toISOString().slice(0, 19).replace("T", " ");
      };

      const formattedStartDate = formatDate(startDate);
      const formattedEndDate = formatDate(endDate);

      // Count projects by mix season
      const projectQuery = `
        SELECT 
          ms.id as mix_season_id,
          ms.name as mix_season_name,
          COUNT(p.id) as project_count
        FROM 
          equalityrecord_project p
        JOIN 
          equalityrecord_mix_season ms ON p.mix_season_id = ms.id
        WHERE 
          p.user_id = ${req.user_id}
          AND p.create_at BETWEEN '${formattedStartDate}' AND '${formattedEndDate}'
        GROUP BY 
          ms.id, ms.name
        ORDER BY 
          ms.name ASC
      `;

      const seasonCounts = await this.sdk.rawQuery(projectQuery);

      // Get total count
      const totalCount = seasonCounts.reduce((sum, season) => sum + parseInt(season.project_count), 0);

      return {
        error: false,
        subscription_period: {
          start: formattedStartDate,
          end: formattedEndDate,
          is_yearly: is_yearly == 1
        },
        total_projects: totalCount,
        projects_by_season: seasonCounts
      };
    } catch (error) {
      console.error("Error getting project counts by mix season:", error);
      throw new Error(error.message || "Failed to get project counts");
    }
  }
};
