const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class ManagerPermissionService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("manager_permission");
      const result = await this.sdk.get();
      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   manager_id: 1,
      // };

      let filterQuery = "";
      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.manager_id) {
          filterArr.push(` mp.manager_id = ${filter.manager_id} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_manager_permission mp
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT
        mp.*, CONCAT(u.first_name, ' ', u.last_name) AS manager_name
      FROM equalityrecord_manager_permission mp
      LEFT JOIN equalityrecord_user u ON u.id = mp.manager_id
      ${filterQuery}
      ORDER BY mp.id DESC
      LIMIT ${limit} OFFSET ${offset};`;

      const result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOne(req) {
    try {
      this.sdk = this.getSDK(req);

      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not authorized to access this resource"
        };
      }

      this.sdk.setTable("manager_permission");
      const result = await this.sdk.get({
        manager_id: req.user_id
      });
      return {
        error: false,
        model: result.length > 0 ? result[0] : null
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOneManagerPermissionByManagerId(req) {
    try {
      this.sdk = this.getSDK(req);

      if (req.role !== "admin") {
        return {
          error: true,
          message: "You are not authorized to access this resource"
        };
      }

      this.sdk.setTable("manager_permission");
      const result = await this.sdk.get({
        manager_id: req.params.id
      });
      return {
        error: false,
        model: result.length > 0 ? result[0] : null
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async createOrUpdateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("manager_permission");
      const exists = await this.sdk.get({
        manager_id: Number(req.params.id)
      });

      let memberIds = req.body.member_ids;

      if (exists.length > 0) {
        await this.sdk.update(
          filterEmptyFields({
            member_ids: JSON.stringify(memberIds),
            update_at: sqlDateTimeFormat(new Date())
          }),
          Number(exists[0].id)
        );

        return {
          error: false,
          message: "Manager permission updated successfully"
        };
      } else {
        await this.sdk.insert({
          manager_id: Number(req.params.id),
          member_ids: JSON.stringify(memberIds),
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        return {
          error: false,
          message: "Manager permission created successfully"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async removeManagerPermission(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("manager_permission");
      await this.sdk.delete({
        manager_id: Number(req.params.id)
      });

      return {
        error: false,
        message: "Manager permission removed successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
