const {
  sqlDateFormat,
  sqlDateTimeFormat,
  filterEmptyFields,
} = require("../../../services/UtilService");

module.exports = class MixSeasonService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_mix_season
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id) {
          filterArr.push(` ms.user_id = ${filter.user_id} `);
        }
        if (filter.name) {
          filterArr.push(
            ` LOWER(ms.name) LIKE '%${filter.name.toLowerCase()}%' `
          );
        }
        if (filter.status) {
          filterArr.push(` ms.status = ${filter.status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      const result = await this.sdk.rawQuery(`
        SELECT
          ms.*, CONCAT(u.first_name, ' ', u.last_name) AS member_name
        FROM equalityrecord_mix_season ms
        LEFT JOIN equalityrecord_user u ON u.id = ms.user_id
        ${filterQuery}
        ORDER BY id DESC
        LIMIT ${limit} OFFSET ${offset};
      `);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllForManager(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not authorized to perform this action",
        };
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_mix_season
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (!filter.member_ids) {
        return {
          error: true,
          message: "At least one member id is required to perform this action",
        };
      }

      if (Object.keys(filter).length > 0) {
        if (
          filter.member_ids &&
          req.role === "manager" &&
          filter.member_ids.length === 1
        ) {
          filterArr.push(` ms.user_id = ${filter.member_ids[0]} `);
        } else if (
          filter.member_ids &&
          req.role === "manager" &&
          filter.member_ids.length > 1
        ) {
          filterArr.push(` ms.user_id IN (${filter.member_ids.join(",")}) `);
        }

        if (filter.name) {
          filterArr.push(
            ` LOWER(ms.name) LIKE '%${filter.name.toLowerCase()}%' `
          );
        }
        if (filter.status) {
          filterArr.push(` ms.status = ${filter.status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      const result = await this.sdk.rawQuery(`
        SELECT
          ms.*, CONCAT(u.first_name, ' ', u.last_name) AS member_name
        FROM equalityrecord_mix_season ms
        LEFT JOIN equalityrecord_user u ON u.id = ms.user_id
        ${filterQuery}
        ORDER BY id DESC
        LIMIT ${limit} OFFSET ${offset};
      `);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("mix_season");

      const result = await this.sdk.get({
        user_id: req.user_id,
      });

      return {
        error: false,
        list: result.length > 0 ? result : [],
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOne(req) {
    try {
      this.sdk = this.getSDK(req);

      let where = "";
      if (req.role !== "admin") {
        where = `WHERE ms.id = ${req.params.id} AND ms.user_id = ${req.user_id}`;
      } else {
        where = `WHERE ms.id = ${req.params.id}`;
      }

      let rawSql = `
        SELECT ms.*, CONCAT(u.first_name, ' ', u.last_name) AS user_name
        FROM equalityrecord_mix_season ms
        LEFT JOIN equalityrecord_user u ON u.id = ms.user_id
        ${where}
        ;
      `;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          error: false,
          model: result[0],
        };
      } else {
        return {
          error: true,
          model: {},
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("mix_season");

      let userId = null;
      if (req.role === "member") {
        userId = req.user_id;
      } else if (req.role === "manager" || req.role === "admin") {
        if (req.body.user_id) {
          userId = Number(req.body.user_id);
        } else {
          return {
            error: true,
            message: "User Id is required to perform this action",
          };
        }
      } else {
        return {
          error: true,
          message: "You are not authorized to perform this action",
        };
      }

      const payload = {
        user_id: userId,
        name: req.body.name,
        status: req.body.status,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      };

      const result = await this.sdk.insert(payload);

      if (result) {
        return {
          error: false,
          message: "Mix season added successfully",
        };
      } else {
        return {
          error: true,
          message: "Mix season could not be added",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("mix_season");

      await this.sdk.update(
        filterEmptyFields({
          name: req.body.name,
          status: req.body.status,
          update_at: sqlDateTimeFormat(new Date()),
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Mix Season updated successfully",
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project");

      const exists = await this.sdk.get({
        mix_season_id: req.params.id,
      });

      if (exists.length === 0) {
        this.sdk.setTable("mix_season");
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Mix Season deleted successfully",
        };
      } else {
        return {
          error: true,
          message:
            "Mix Season cannot be deleted because it is assigned to project(s)",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
