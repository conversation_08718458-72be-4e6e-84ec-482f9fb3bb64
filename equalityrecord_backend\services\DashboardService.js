const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class CategoryService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  // workorder status
  // 1: 'Writer',
  // 2: 'Artist',
  // 3: 'Engineer'
  // 4: 'Rejected',
  // 5: 'Completed',
  // 6: 'Inactive',

  async getDashboardData(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      let result = await this.sdk.rawQuery(`
        SELECT
          wo.*
        FROM equalityrecord_work_order wo
        WHERE wo.user_id = ${req.user_id};
      `);

      if (result.length > 0) {
        let employees = await this.sdk.rawQuery(`
          SELECT
            e.id, e.name AS employee_name
          FROM equalityrecord_employee e;
        `);

        for (let i = 0; i < result.length; i++) {
          result[i].writer_name = "";
          result[i].artist_name = "";
          result[i].engineer_name = "";

          if (result[i].writer_id) {
            const writer = employees.find((e) => e.id === result[i].writer_id);
            if (writer) result[i].writer_name = writer.employee_name;
          }

          if (result[i].artist_id) {
            const artist = employees.find((e) => e.id === result[i].artist_id);
            if (artist) result[i].artist_name = artist.employee_name;
          }

          if (result[i].engineer_id) {
            const engineer = employees.find((e) => e.id === result[i].engineer_id);
            if (engineer) result[i].engineer_name = engineer.employee_name;
          }
        }
      }

      let currentWeekProjects = await this.sdk.rawQuery(`
        SELECT
          p.*,
          c.program AS program_name, c.position AS position_name
        FROM equalityrecord_project p
        LEFT JOIN equalityrecord_client c ON c.id = p.client_id
          WHERE p.user_id = ${req.user_id}
          ORDER BY p.mix_date DESC;
      `);

      // get survey notifications by project_id
      if (currentWeekProjects.length > 0) {
        for (let i = 0; i < currentWeekProjects.length; i++) {
          // equalityrecord_project_file
          let urls = await this.sdk.rawQuery(`
            SELECT
              pf.url
            FROM equalityrecord_project_file pf
            WHERE pf.project_id = ${currentWeekProjects[i].id}
            AND pf.type = 'master';
          `);

          currentWeekProjects[i].urls = urls.length > 0 ? urls : [];
        }
      }

      let surveyNotifications = await this.sdk.rawQuery(`
        SELECT
          sn.*, p.team_name, c.program AS program_name
        FROM equalityrecord_survey_notification sn
        INNER JOIN equalityrecord_survey s ON s.id = sn.survey_id
        INNER JOIN equalityrecord_project p ON p.id = s.project_id
        INNER JOIN equalityrecord_client c ON c.id = p.client_id
        WHERE p.user_id = ${req.user_id} AND sn.is_seen = 0;
      `);

      let producerWorkOrders = await this.sdk.rawQuery(`
        SELECT
          pwo.*
        FROM equalityrecord_producer_work_order pwo
        WHERE pwo.user_id = ${req.user_id};
      `);

      return {
        error: false,
        list: result.length > 0 ? result : [],
        current_week_projects: currentWeekProjects ? currentWeekProjects : [],
        survey_notifications: surveyNotifications ? surveyNotifications : [],
        producer_work_orders: producerWorkOrders ? producerWorkOrders : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
