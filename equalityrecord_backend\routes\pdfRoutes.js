const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const { generatePdfByUrl } = require("../controllers/pdfController");

const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// key: pdf
router.post("/generate", public, generatePdfByUrl);

module.exports = router;
