const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
const aws = require("aws-sdk");
const config = require("../../../config");

module.exports = class EditService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};

      let filterQuery = "";
      // if (req.role !== "admin") {
      //   filterQuery += `WHERE m.user_id = ${req.user_id}`;
      // }

      this.sdk.setTable("edit_type");
      const edit_type = await this.sdk.get({ user_id: req.user_id });

      if (edit_type.filter((e) => e.edit_type === "Minor 1").length === 0) {
        let data = [
          {
            edit_type: "Minor 1",
            user_id: req.user_id,
            request_range: "01/01/2024 - 07/31/24",
            number_of_lines: "20 and below",
            edit_duration: "0 months, 2 weeks, 0 days"
          },
          {
            edit_type: "Major 1",
            user_id: req.user_id,
            request_range: "01/01/2024 - 07/31/24",
            number_of_lines: "21 and up",
            edit_duration: "0 months, 3 weeks, 0 days"
          },
          {
            edit_type: "Minor 2",
            user_id: req.user_id,
            request_range: "08/01/2024 - 12/31/24",
            number_of_lines: "20 and below",
            edit_duration: "0 months, 3 weeks, 0 days"
          },
          {
            edit_type: "Major 2",
            user_id: req.user_id,
            request_range: "08/01/2024 - 12/31/24",
            number_of_lines: "21 and up",
            edit_duration: "0 months, 4 weeks, 0 days"
          },
          {
            edit_type: "Reconstruction",
            user_id: req.user_id,
            request_range: "Special",
            number_of_lines: "Special",
            edit_duration: "0 months, 5 weeks, 0 days"
          }
        ];
        for (let i = 0; i < data.length; i++) {
          await this.sdk.insert({
            ...data[i],
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });
        }

        // wait for the data to be inserted
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id) {
          filterArr.push(` m.user_id = ${filter.user_id} `);
        }
        //  if (filter.user_id && req.role === "admin") {
        //    filterArr.push(` m.user_id = ${filter.user_id} `);
        //  }
        // if (filter.name) {
        //   filterArr.push(
        //     ` LOWER(mr.name) LIKE '%${filter.name.toLowerCase()}%' `
        //   );
        // }
        if (filter.project_id) {
          filterArr.push(` m.project_id = ${filter.project_id} `);
        }
        if (filter.id) {
          filterArr.push(` m.id = ${filter.id} `);
        }
        if (filter.url) {
          filterArr.push(` m.url = ${filter.url} `);
        }
        if (filter.edit_status) {
          filterArr.push(` m.edit_status = ${filter.edit_status} `);
        }
        if (filter.type) {
          filterArr.push(` m.type = ${filter.type} `);
        }
        if (filter.is_paid) {
          filterArr.push(` m.is_paid = ${filter.is_paid} `);
        }
        if (filter.is_music) {
          filterArr.push(` m.is_music = ${filter.is_music} `);
        }
        if (filter.is_member) {
          filterArr.push(` m.is_member = ${filter.is_member} `);
        }
        if (filter.status) {
          filterArr.push(` m.status = ${filter.status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_edit_type m
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT m.*
        FROM equalityrecord_edit_type m
        ${filterQuery}
        ORDER BY m.id DESC
      LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit_type");

      const id = await this.sdk.insert({ ...req.body, create_at: sqlDateFormat(new Date()), update_at: sqlDateTimeFormat(new Date()) });

      if (id) {
        return {
          error: false,
          message: "Cycle Count added successfully.",
          id
        };
      } else {
        return {
          error: true,
          message: "Cycle Count add failed.",
          id: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async editOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit_type");

      this.sdk.setTable("edit_type");
      await this.sdk.update(
        filterEmptyFields({
          ...req.body,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Cycle Count updated successfully."
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit_type");

      const result = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_edit_type WHERE id = ${req.params.id}`);

      return {
        error: false,
        model: result.length > 0 ? result[0] : {}
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit_type");

      const result = await this.sdk.get({ id: req.params.id });

      if (result.length > 0) {
        await this.sdk.delete({}, req.params.id);
        // await this.deleteOneS3File(result[0].url);
        return {
          error: false,
          message: "Edit type deleted successfully."
        };
      } else {
        return {
          error: true,
          message: "Edit type not found"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOneS3File(url) {
    try {
      const s3 = new aws.S3({
        accessKeyId: config.aws_key,
        secretAccessKey: config.aws_secret
      });

      // https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo.png
      const fileName = url.split("/").pop();

      const params = {
        Bucket: config.aws_bucket,
        Key: fileName
      };

      s3.deleteObject(params, function (err, data) {
        if (err) console.log(err, err.stack);
        else console.log("S3 file deleted successfully");
      });

      return {
        error: false,
        message: "S3 file deleted successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllClientCountTrack(req, user_id) {
    try {
      this.sdk = this.getSDK(req);

      // pick member_ids from equalityrecord_client_member, note it is saved as [9,2,3] now go to edit and get all the records where user_id is in member_ids

      const result = await this.sdk.rawQuery(`
        SELECT 
         *
          FROM equalityrecord_client_member as ecm
          WHERE  Or client_id = ${user_id}
      `);

      let parsedMebmerIds = "";

      if (result.length > 0) {
        parsedMebmerIds =
          "(" +
          JSON.parse(result[0].member_ids).map((id) => {
            return id;
          }) +
          ")";
        result[0].member_ids + ")";
      }
      // join with  USER

      const rawSql = `SELECT up.company_name, up.user_id, up.office_email, u.first_name, u.last_name
FROM equalityrecord_profile AS up
JOIN equalityrecord_user AS u ON u.id = up.user_id
WHERE u.id IN ${parsedMebmerIds}
ORDER BY up.user_id DESC;
`;

      const result2 = await this.sdk.rawQuery(rawSql);

      console.log("result2", result2);

      for (let i = 0; i < result2.length; i++) {
        const rawSql = `SELECT *
        FROM equalityrecord_edit_type
        WHERE user_id = ${result2[i].user_id}
        ORDER BY id DESC;`;

        let result3 = await this.sdk.rawQuery(rawSql);
        result2[i].tracks = result3;
      }

      return {
        error: false,
        list: result2
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
