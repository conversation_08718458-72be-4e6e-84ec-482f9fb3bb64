const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  getAll,
  retrieveAll,
  getAllProjectFile,
  createProjectFile,
  addOne,
  updateOne,
  deleteOne,
  view,
  masterView,
  addSubProject,
  deleteSubProjects,
  getSubProjectsByProjectId,
  getAllUnAssignedSubProjects,
  getAllUnAssignedSongs,
  viewSurvey,
  getAllIdea,
  addIdea,
  updateIdea,
  getAllSubProjectIdea,
  addSubProjectIdea,
  deleteSubProjectIdea,
  viewSurveyByProjectId,
  updateSubProject,
  updateSubProjectStatus,
  getAllLyrics,
  addAndAssignSubProjectIdea,
  createOrUpdateSubProjectIdeas,
  updateSubProjectEightCount,
  updateSubProjectEmployee,
  updateSubProjectEmployeeCost,
  deleteOneFile,
  createAdminFile,
  getAllUnAssignedSubProjectsByWriterIdAndArtistId,
  updateSubProjectDetails,
  reassignSongs,
  getAllMasterFiles,
  resetSubProjectEmployee,
  addAndAssignSubProjectIdeaForMultiSubProjects,
  getAllProjectIdeasBySubProjectId,
  retrieveAllProjectsForClient,
  retrieveAllMultiFilter,
  createOrUpdateTeamDetails,
  viewTeamDetails,
  deleteOneTeamDetails,
  viewProjectDetailsForClient,
  retrieveAllForAdmin,
  getAllProjectsForClient,
  retrieveAllForProjectCalendarForAdmin,
  retrieveAllForProjectCalendarForManager,
  deleteProjectIdea,
  retrieveAllMultiFilterForManager,
  cleanup,
  viewProjectDetailsForAdmin,
  addSubProjectWithoutProjectId,
  viewProjectDetailsForManager,
  addSubProjectToProject,
  exportSongsToCsv,
  downloadAllSongs,
  moveSongToNewMixSeason,
  getSubProjectWithTypeName,
  getSongsColumn,
  getProjectCountsByMixSeason
} = require("../controllers/projectController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

router.get("/get_all", middlewares, getAll);
router.get("/client/get_all", middlewares, getAllProjectsForClient);
router.post("/retrieve", middlewares, retrieveAll);
router.post("/admin/retrieve_all", middlewares, retrieveAllForAdmin);
router.post("/calendar/admin/retrieve_all", middlewares, retrieveAllForProjectCalendarForAdmin);
router.post("/calendar/manager/retrieve_all", middlewares, retrieveAllForProjectCalendarForManager);
router.post("/retrieve_all_multi_filter", middlewares, retrieveAllMultiFilter);
router.post("/manager/retrieve_all_multi_filter", middlewares, retrieveAllMultiFilterForManager);
router.post("/add", middlewares, addOne);
router.put("/:id", middlewares, updateOne);
router.delete("/:id", middlewares, deleteOne);

router.delete("/real/cleanup", middlewares, cleanup);

router.post("/sub_project", middlewares, addSubProject);
router.post("/sub_project/without_project_id", middlewares, addSubProjectWithoutProjectId);
router.put("/sub_project", middlewares, updateSubProject);
router.get("/sub_project/export/songs", middlewares, exportSongsToCsv);
router.put("/sub_project/details", public, updateSubProjectDetails);
router.delete("/sub_project/delete", middlewares, deleteSubProjects);
router.put("/sub_project/employee", middlewares, updateSubProjectEmployee);
router.put("/sub_project/employee/reset", middlewares, resetSubProjectEmployee);
router.put("/sub_project/employee/cost", middlewares, updateSubProjectEmployeeCost);
router.put("/sub_project/eight_count", middlewares, updateSubProjectEightCount);
router.put("/sub_project/status", middlewares, updateSubProjectStatus);
router.get("/unassigned/sub_project", middlewares, getAllUnAssignedSubProjects);
router.post("/unassigned/sub_project/retrieve", middlewares, getAllUnAssignedSubProjectsByWriterIdAndArtistId);
router.post("/unassigned/songs", middlewares, getAllUnAssignedSongs);

router.post("/view/:id", middlewares, view);
router.get("/master/view", middlewares, masterView);
router.get("/file/:id", middlewares, getAllProjectFile);
router.delete("/file/:id", public, deleteOneFile);
router.post("/file", middlewares, createProjectFile);

// router.get("/admin/files", middlewares, getAllAdminFile);
router.post("/admin/file", middlewares, createAdminFile);

router.post("/survey/view", middlewares, viewSurvey);
router.get("/survey/view/:id", middlewares, viewSurveyByProjectId);

router.get("/idea/:project_id", middlewares, getAllIdea);
router.get("/idea/sub_project/:id", middlewares, getAllProjectIdeasBySubProjectId);
router.post("/idea", middlewares, addIdea);
router.put("/idea/update", middlewares, updateIdea);

router.get("/sub_project/idea/:subproject_id", middlewares, getAllSubProjectIdea);
router.post("/sub_project/idea", middlewares, addSubProjectIdea);
router.post("/sub_project/ideas", middlewares, createOrUpdateSubProjectIdeas);
router.post("/sub_project/assign/idea", middlewares, addAndAssignSubProjectIdea);
router.post("/multi/sub_project/assign/idea", middlewares, addAndAssignSubProjectIdeaForMultiSubProjects);
router.delete("/sub_project/idea", middlewares, deleteSubProjectIdea);
router.delete("/unassigned/idea", middlewares, deleteProjectIdea);

router.get("/lyrics/:project_id", middlewares, getAllLyrics);
router.get("/master_files/:project_id", middlewares, getAllMasterFiles);

router.put("/reassign/songs", middlewares, reassignSongs);

router.post("/client/retrieve_all", middlewares, retrieveAllProjectsForClient);
router.put("/client/team_details", middlewares, createOrUpdateTeamDetails);
router.get("/client/team_details/view/:id", middlewares, viewTeamDetails);
router.delete("/client/team_details/:id", middlewares, deleteOneTeamDetails);
router.post("/client/view/:id", middlewares, viewProjectDetailsForClient);
router.post("/admin/view/:id", middlewares, viewProjectDetailsForAdmin);
router.post("/manager/view/:id", middlewares, viewProjectDetailsForManager);

router.post("/download_all_songs", middlewares, downloadAllSongs);
router.post("/move_song_to_new_mix_season", middlewares, moveSongToNewMixSeason);
router.put("/sub_project/:subproject_id", middlewares, addSubProjectToProject);
router.post("/sub_project/type_name", middlewares, getSubProjectWithTypeName);
router.get("/sub_project/column", middlewares, getSongsColumn);
router.get("/sub_project/:project_id", middlewares, getSubProjectsByProjectId);
router.get("/subscription/project-counts", middlewares, getProjectCountsByMixSeason);
module.exports = router;
