const { validateObject } = require("../../../services/ValidationService");
const ProjectService = require("../services/ProjectService");
exports.getAll = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.getAll(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllProjectsForClient = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.getAllProjectsForClient(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAllForAdmin = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAllForAdmin(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
      total_completed_projects: result.total_completed_projects,
      total_count: result.total_count
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAllForProjectCalendarForAdmin = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAllForProjectCalendarForAdmin(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAllForProjectCalendarForManager = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAllForProjectCalendarForManager(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAllMultiFilter = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAllMultiFilter(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
      total_completed_projects: result.total_completed_projects,
      total_count: result.total_count
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAllMultiFilterForManager = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAllMultiFilterForManager(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
      total_completed_projects: result.total_completed_projects,
      total_count: result.total_count
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        client_id: "required",
        mix_date: "required",
        mix_season_id: "required",
        team_name: "required",
        mix_type_id: "required",
        // team_type: "required",
        // division: "required",
        user_id: "required",
        discount: "required",
        uuidv4: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addOne(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      project_id: result.project_id,
      survey_id: result.survey_id,
      uuidv4: req.body.uuidv4
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ProjectService();
    const result = await service.updateOne(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addSubProject = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        type_name: "required",
        project_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addSubProject(req);
    if (result) {
      return res.status(200).json({ error: false, message: "Sub-project added successfully" });
    } else {
      return res.status(200).json({ error: true, message: "Error creating Sub-project" });
    }
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addSubProjectWithoutProjectId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        type_name: "required",
        mix_season: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addSubProjectWithoutProjectId(req);
    return res.status(200).json(result);
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.exportSongsToCsv = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.exportSongsToCsv(req);

    return res.status(200).send(result);
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addSubProjectToProject = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.addSubProjectToProject(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateSubProject = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateSubProject(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateSubProjectDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateSubProjectDetails(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

// get sub project with type name
exports.getSubProjectWithTypeName = async (req, res) => {
  try {
    const service = new ProjectService();
    const { result, total, page, limit } = await service.getSubProjectWithTypeName(req);
    return res.status(200).json({ error: false, result, total, page, limit });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteSubProjects = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_ids: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.deleteSubProjects(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateSubProjectEmployee = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateSubProjectEmployee(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.resetSubProjectEmployee = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        employee_type: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.resetSubProjectEmployee(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateSubProjectEmployeeCost = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateSubProjectEmployeeCost(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateSubProjectEightCount = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        eight_count: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateSubProjectEightCount(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateSubProjectStatus = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        status: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateSubProjectStatus(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getSubProjectsByProjectId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getSubProjectsByProjectId(req);
    return res.status(200).json({ error: false, list: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.view = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.view(req);
    return res.status(200).json({ error: false, model: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ProjectService();
    const result = await service.deleteOne(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteOneFile = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ProjectService();
    const result = await service.deleteOneFile(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      file: result.file
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.viewSurvey = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        uuidv4: "required",
        project_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.viewSurvey(req);
    return res.status(200).json({ error: false, model: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.viewSurveyByProjectId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.viewSurveyByProjectId(req);
    return res.status(200).json({
      error: result.error,
      model: result.model,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllProjectFile = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllProjectFile(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createProjectFile = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        type: "required",
        url: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.createProjectFile(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createAdminFile = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        subproject_id: "required",
        type: "required",
        url: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.createAdminFile(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllIdea(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllProjectIdeasBySubProjectId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllProjectIdeasBySubProjectId(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        idea_key: "required",
        idea_value: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addIdea(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        idea_key: "required",
        idea_value: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.updateIdea(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllSubProjectIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllSubProjectIdea(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addSubProjectIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        idea_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addSubProjectIdea(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createOrUpdateSubProjectIdeas = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required"
        // idea_ids: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.createOrUpdateSubProjectIdeas(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addAndAssignSubProjectIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        subproject_id: "required",
        idea_key: "required",
        idea_value: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addAndAssignSubProjectIdea(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addAndAssignSubProjectIdeaForMultiSubProjects = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        subproject_ids: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.addAndAssignSubProjectIdeaForMultiSubProjects(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteSubProjectIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        idea_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.deleteSubProjectIdea(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteProjectIdea = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required|integer",
        idea_id: "required|integer"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.deleteProjectIdea(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllUnAssignedSubProjects = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.getAllUnAssignedSubProjects(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllUnAssignedSubProjectsByWriterIdAndArtistId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        writer_id: "required",
        artist_id: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllUnAssignedSubProjectsByWriterIdAndArtistId(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllUnAssignedSongs = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.getAllUnAssignedSongs(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllLyrics = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllLyrics(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllMasterFiles = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.getAllMasterFiles(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.masterView = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.masterView(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.reassignSongs = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_ids: "required",
        project_id: "required"
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ProjectService();
    const result = await service.reassignSongs(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveAllProjectsForClient = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProjectService();
    const result = await service.retrieveAllProjectsForClient(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createOrUpdateTeamDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        // notes: "required",
        // song_list: "required",
        // colors: "required",
        // mascot: "required",
        social_media: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.createOrUpdateTeamDetails(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      id: result.id
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.viewTeamDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.viewTeamDetails(req);
    return res.status(200).json({ error: false, model: result ?? null });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteOneTeamDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.deleteOneTeamDetails(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.viewProjectDetailsForClient = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.viewProjectDetailsForClient(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.viewProjectDetailsForAdmin = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.viewProjectDetailsForAdmin(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.viewProjectDetailsForManager = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.viewProjectDetailsForManager(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.cleanup = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        user_id: "required|integer",
        mix_season_id: "required|integer"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.cleanup(req);
    return res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.downloadAllSongs = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.downloadAllSongs(req);

    if (result.error) {
      return res.status(400).json({
        error: true,
        message: result.message
      });
    }

    return res.status(200).json({
      error: false,
      result: result.result,
      count: result.count
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: true,
      message: err.message
    });
  }
};

exports.moveSongToNewMixSeason = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        mix_season: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProjectService();
    const result = await service.moveSongToNewMixSeason(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({ message: err.message });
  }
};

exports.getSongsColumn = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.getSongsColumn(req);
    return res.status(200).json({ error: result.error, list: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({ message: err.message });
  }
};

exports.getProjectCountsByMixSeason = async (req, res) => {
  try {
    const service = new ProjectService();
    const result = await service.getProjectCountsByMixSeason(req);
    return res.status(200).json(result);
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};
