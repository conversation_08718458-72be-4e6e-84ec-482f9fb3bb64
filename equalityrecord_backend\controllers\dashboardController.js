const { validateObject } = require("../../../services/ValidationService");
const DashboardService = require("../services/DashboardService");

exports.getDashboardData = async (req, res) => {
  try {
    const service = new DashboardService();
    const result = await service.getDashboardData(req);
    return res
      .status(200)
      .json({
        error: result.error,
        list: result.list,
        current_week_projects: result.current_week_projects,
        survey_notifications: result.survey_notifications,
        producer_work_orders: result.producer_work_orders
      });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};
