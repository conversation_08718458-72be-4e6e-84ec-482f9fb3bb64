const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class ProducerWorkOrderService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE pwo.user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_producer_work_order pwo
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` pwo.user_id = ${filter.user_id} `);
        }
        if (filter.employee_name) {
          // Assuming the `employee_name` can be either writer name, artist name, or engineer name
          filterArr.push(`
            (LOWER(e.name) LIKE '%${filter.employee_name.toLowerCase()}%')
          `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let joinQuery = `
        LEFT JOIN equalityrecord_employee e ON e.id = pwo.producer_id
      `;

      let rawSql = `SELECT
                      pwo.*
                    FROM equalityrecord_producer_work_order pwo
                      ${joinQuery}
                      ${filterQuery}
                    ORDER BY pwo.id DESC
                    LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      const employees = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_employee;
      `);

      for (let resultItem of result) {
        if (resultItem.producer_id !== 0) {
          const producer = employees.filter((employee) => employee.id === resultItem.producer_id);
          resultItem.producer = producer[0];
        }
      }

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("producer_work_order");

      const existingWorkOrders = await this.sdk.get();

      let workorder_code = ""; // 23-000001 23 is the year, 000001 is the number of new work order in that year

      if (existingWorkOrders.length > 0) {
        const lastWorkOrder = existingWorkOrders[0];
        const lastWorkOrderCode = lastWorkOrder.workorder_code;
        const lastWorkOrderYear = lastWorkOrderCode.split("-")[0];
        const lastWorkOrderNumber = lastWorkOrderCode.split("-")[1];
        let currentYear = new Date().getFullYear().toString();
        currentYear = currentYear.substring(2, 6);
        if (lastWorkOrderYear === currentYear) {
          const newWorkOrderNumber = parseInt(lastWorkOrderNumber) + 1;
          workorder_code = `${currentYear}-${newWorkOrderNumber.toString().padStart(6, "0")}`;
        } else {
          workorder_code = `${currentYear}-000001`;
        }
      } else {
        const currentYear = new Date().getFullYear().toString();
        workorder_code = `${currentYear}-000001`;
      }

      const payload = {
        user_id: req.user_id,
        producer_id: req.body.producer_id,
        producer_cost: req.body.producer_cost,
        subproject_id: req.body.subproject_id,
        project_id: req.body.project_id,
        due_date: req.body.due_date ?? null,
        workorder_code: workorder_code,
        uuidv4: req.body.uuidv4,
        status: 1,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      if (result) {
        await this.sdk.rawQuery(`
          UPDATE equalityrecord_subproject
          SET workorder_id = ${result}
          WHERE id = ${req.body.subproject_id};
        `);
        return {
          error: false,
          message: "Producer Work order added successfully"
        };
      } else {
        return {
          error: true,
          message: "Error creating work order"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getProducerWorkOrderDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("producer_work_order");

      let result = null;

      result = await this.sdk.rawQuery(`
        SELECT
          pwo.*,
          e.name AS producer_name, e.email AS producer_email,
          ep.company_name AS user_company_name
        FROM equalityrecord_producer_work_order pwo
        LEFT JOIN equalityrecord_employee e ON e.id = pwo.producer_id
        LEFT JOIN equalityrecord_profile ep on e.user_id = ep.user_id
        WHERE pwo.uuidv4 = '${req.body.uuidv4}';
      `);
      // console.log("result", result);

      if (result.length > 0) {
        let subProject = await this.sdk.rawQuery(`
          SELECT
            sp.*,
            p.team_name, p.team_type,
            c.program AS program_name
          FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON p.id = sp.project_id
          LEFT JOIN equalityrecord_client c ON c.id = p.client_id
          WHERE sp.workorder_id = ${result[0].id};
        `);

        result[0].subProject = subProject[0];

        // retrieve instrumental (loops) files -> uploaded by producer
        this.sdk.setTable("project_file");
        let instrumentals = await this.sdk.get({
          workorder_id: result[0].id,
          employee_type: "producer",
          type: "instrumental"
        });

        result[0].instrumentals = instrumentals.length > 0 ? instrumentals : [];

        // retrieve admin instrumental (loops) files -> uploaded by admin
        this.sdk.setTable("project_file");
        let adminInstrumentals = await this.sdk.get({
          employee_type: "producer",
          type: "instrumental",
          is_from_admin: 1,
          subproject_id: result[0].subproject_id
        });

        result[0].adminInstrumentals = adminInstrumentals.length > 0 ? adminInstrumentals : [];

        if (result[0].producer_id !== 0) {
          const producer = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].producer_id};
          `);
          result[0].producer = producer[0];

          const producerCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE id = ${result[0].producer_id};
          `);
          result[0].producerCost = producerCost.length > 0 ? producerCost[0].employee_cost : 0;
        }

        return {
          error: false,
          message: "Work order exists",
          model: result[0]
        };
      } else {
        return {
          error: true,
          message: "Work order does not exist",
          model: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("producer_work_order");

      await this.sdk.update(
        filterEmptyFields({
          uuidv4: req.body.uuidv4,
          workorder_code: req.body.workorder_code,
          due_date: req.body.due_date,
          subproject_id: req.body.subproject_id,
          producer_id: req.body.producer_id,
          producer_cost: req.body.producer_cost,
          producer_submit_status: req.body.producer_submit_status,
          status: req.body.status,
          project_id: req.body.project_id,
          user_id: req.body.user_id,
          is_viewed: req.body.is_viewed,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Work order updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
