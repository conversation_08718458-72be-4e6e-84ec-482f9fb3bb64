const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { getAll, getOneBySlug, sendEmail } = require("../controllers/emailController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// Key: email
router.get("/get_all", middlewares, getAll);
router.get("/get_one", middlewares, getOneBySlug);
router.post("/send", public, sendEmail);

module.exports = router;
