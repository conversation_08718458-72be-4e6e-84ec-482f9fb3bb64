const WorkOrderService = require("../services/WorkOrderService");
const {
  sqlDateTimeFormat,
  filterEmptyFields,
} = require("../../../services/UtilService");

async function cleanUpProjects(sdk, options) {
  try {
    // 1. select all from equalityrecord_project_file (id, project_id, subproject_id, url)
    // 2. join with equalityrecord_project (id) with equalityrecord_project_file.project_id = equalityrecord_project.id
    // 3. if project is not found, delete the record from equalityrecord_project_file
    // 4. delete the file from s3
  } catch (error) {
    console.error(error);
  }
}

module.exports = cleanUpProjects;
