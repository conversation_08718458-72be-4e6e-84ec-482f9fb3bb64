const { validateObject } = require("../../../services/ValidationService");
const ManagerPermissionService = require("../services/ManagerPermissionService");

exports.getAll = async (req, res) => {
  try {
    const service = new ManagerPermissionService();
    const result = await service.getAll(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ManagerPermissionService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getOne = async (req, res) => {
  try {
    const service = new ManagerPermissionService();
    const result = await service.getOne(req);
    return res.status(200).json({
      error: result.error,
      model: result.model,
      message: result.message,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getOneManagerPermissionByManagerId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);

    const service = new ManagerPermissionService();
    const result = await service.getOneManagerPermissionByManagerId(req);
    return res.status(200).json({
      error: result.error,
      model: result.model,
      message: result.message,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.createOrUpdateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);

    const reqBodyValidation = await validateObject(
      {
        member_ids: "required|array",
      },
      req.body
    );

    if (reqBodyValidation.error) return res.status(403).json(reqBodyValidation);

    let memberIds = req.body.member_ids;
    if (memberIds) {
      memberIds = JSON.stringify(memberIds);
    } else {
      return res.status(403).json({
        error: true,
        message: "Member ids are required",
      });
    }

    if (memberIds.length > 1000) {
      return res.status(403).json({
        error: true,
        message: "Member ids are too long",
      });
    }

    const service = new ManagerPermissionService();
    const result = await service.createOrUpdateOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.removeManagerPermission = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);

    const service = new ManagerPermissionService();
    const result = await service.removeManagerPermission(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};
