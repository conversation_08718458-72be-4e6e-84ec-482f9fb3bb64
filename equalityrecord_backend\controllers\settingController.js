const { validateObject } = require("../../../services/ValidationService");
const SettingService = require("../services/SettingService");
const { getLocalPath } = require("../../../services/UtilService");

exports.retrieveAll = async (req, res) => {
  try {
    const service = new SettingService();
    const result = await service.retrieveAll(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateAll = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        settings: "required"
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new SettingService();
    const result = await service.updateAll(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.uploadFile = async (req, res) => {
  try {
    const url = getLocalPath(req.file.path);
    return res.status(200).json({ error: false, url });
  } catch (error) {
    console.log(error);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

// TODO: delete & update or create
exports.createOrUpdateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        setting_key: "required",
        setting_value: "required"
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new SettingService();
    const result = await service.createOrUpdateOne(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getSiteImages = async (req, res) => {
  try {
    const service = new SettingService();
    const result = await service.getSiteImages(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.retrieveSummary = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        start_date: "required",
        end_date: "required"
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new SettingService();
    const result = await service.retrieveSummary(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};
