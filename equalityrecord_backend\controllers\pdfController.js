const { validateObject } = require("../../../services/ValidationService");
const puppeteer = require("puppeteer");

exports.generatePdfByUrl = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        url: "required|string",
        file_name: "string",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const browser = await puppeteer.launch({
      headless: true,
      executablePath: "/usr/bin/chromium-browser",
      args: ["--no-sandbox"],
    });
    const page = await browser.newPage();
    await page.goto(req.body.url);
    const pdf = await page.pdf();

    await browser.close();

    let fileName = req.body.file_name ?? "download.pdf";
    res.writeHead(200, {
      "Content-Type": "application/pdf",
      "Content-Disposition": 'attachment; filename="' + fileName + '"',
    });
    return res.end(pdf);
  } catch (err) {
    res.writeHead(500, { "Content-Type": "text/plain" });
    res.end(err.message);
  }
};
