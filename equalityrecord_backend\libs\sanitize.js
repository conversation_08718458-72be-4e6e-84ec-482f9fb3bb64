const SqlString = require("sqlstring");

function generateConditions(operator, val, col) {
  switch (operator) {
    case "between":
      return `(${col} BETWEEN ${SqlString.escape(val[0])} AND ${SqlString.escape(val[1])})`;
    case "in":
      return `${col} IN (${val.map((item) => SqlString.escape(item)).join(",")})`;
    case "nin":
      return `${col} NOT IN (${val.map((item) => SqlString.escape(item)).join(",")})`;
    case "like":
      return `${col} LIKE ${SqlString.escape(val)}`;
    case "nlike":
      return `${col} NOT LIKE ${SqlString.escape(val)}`;
    case "gt":
      return `${col} > ${SqlString.escape(val)}`;
    case "gte":
      return `${col} >= ${SqlString.escape(val)}`;
    case "lt":
      return `${col} < ${SqlString.escape(val)}`;
    case "lte":
      return `${col} <= ${SqlString.escape(val)}`;
    case "ne":
      return `${col} <> ${SqlString.escape(val)}`;
    case "or":
      const or = Object.keys(val).map((key) => generateConditions(key, val[key], col));
      return or.length ? `(${or.join(" OR ")})` : "";
    case "and":
      const and = Object.keys(val).map((key) => generateConditions(key, val[key], col));
      return and.length ? `(${and.join(" AND ")})` : "";
    default:
      return `${col} = ${SqlString.escape(val)}`;
  }
}

function getCondition(k, v) {
  if (v !== null) {
    const c = k
      .split(".")
      .map((item) => `\`${item}\``)
      .join(".");

    if (v.constructor === Object) {
      const operator = Object.keys(v)[0];
      const val = v[operator];

      return generateConditions(operator, val, c);
    } else if (typeof v === "string") return `${c} LIKE ${SqlString.escape(`%${v}%`)}`;
    else return `${c} = ${SqlString.escape(v)}`;
  }
}

exports.getWhere = (where, join = "AND") => {
  const sanitizedData = [];
  const conditions = [];

  for (const key in where) {
    const value = where[key];

    if (key === "AND" || key === "OR") {
      const gw = getWhere(where[key], key);
      gw && conditions.push(gw);
      continue;
    }

    const condition = getCondition(key, value);
    condition && conditions.push(condition);
  }

  sanitizedData.push(Object.keys(where).length ? (conditions.length ? ` (${conditions.join(` ${join} `)})` : "") : "");
  return sanitizedData.join(` ${join} `);
};
