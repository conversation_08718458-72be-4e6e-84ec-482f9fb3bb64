const router = require("../../baas");
const dashboard = require("./routes/dashboardRoutes");
const mixType = require("./routes/mixTypeRoutes");
const client = require("./routes/clientRoutes");
const project = require("./routes/projectRoutes");
const survey = require("./routes/surveyRoutes");
const user = require("./routes/userRoutes");
const employee = require("./routes/employeeRoutes");
const workOrder = require("./routes/workOrderRoutes");
const setting = require("./routes/settingRoutes");
const mixSeason = require("./routes/mixSeasonRoutes");
const keepAlive = require("./routes/keepAliveRoutes");
const email = require("./routes/emailRoutes");
const producerWorkOrder = require("./routes/producerWorkOrderRoutes");
const media = require("./routes/mediaRoutes");
const cycle_count = require("./routes/cycleCountRoutes");
const edit = require("./routes/editRoutes");
const edit_type = require("./routes/editTypeRoutes");
const eightCount = require("./routes/eightCountRoutes");
const managerPermission = require("./routes/managerPermissionRoutes");
const pdfRoutes = require("./routes/pdfRoutes");
const subscription = require("./routes/subscriptionRoutes");

module.exports = function (app) {
  app.use(router);

  app.use("/v3/api/health_check", (req, res) => {
    res.json({ message: "Hello World" });
  });
  app.use("/v3/api/custom/equality_record/dashboard", dashboard);
  app.use("/v3/api/custom/equality_record/edit", edit);
  app.use("/v3/api/custom/equality_record/edit_type", edit_type);
  app.use("/v3/api/custom/equality_record/mix_type", mixType);
  app.use("/v3/api/custom/equality_record/client", client);
  app.use("/v3/api/custom/equality_record/project", project);
  app.use("/v3/api/custom/equality_record/survey", survey);
  app.use("/v3/api/custom/equality_record/user", user);
  app.use("/v3/api/custom/equality_record/employee", employee);
  app.use("/v3/api/custom/equality_record/work_order", workOrder);
  app.use("/v3/api/custom/equality_record/setting", setting);
  app.use("/v3/api/custom/equality_record/mix_season", mixSeason);
  app.use("/v3/api/custom/equality_record/keep_alive", keepAlive);
  app.use("/v3/api/custom/equality_record/email", email);
  app.use("/v3/api/custom/equality_record/producer_work_order", producerWorkOrder);
  app.use("/v3/api/custom/equality_record/media", media);
  app.use("/v3/api/custom/equality_record/cycle_count", cycle_count);
  app.use("/v3/api/custom/equality_record/eight-count", eightCount);
  app.use("/v3/api/custom/equality_record/manager_permission", managerPermission);
  app.use("/v3/api/custom/equality_record/pdf", pdfRoutes);
  app.use("/v3/api/custom/equality_record/subscription", subscription);
};
