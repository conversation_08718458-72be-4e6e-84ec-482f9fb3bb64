const postmark = require("postmark");
const config = require("../../../config");

module.exports = class EmailService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("email");

      const result = await this.sdk.get({});

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOneBySlug(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("email");

      const result = await this.sdk.get({
        slug: req.query.slug
      });

      return {
        error: false,
        model: result.length > 0 ? result[0] : {}
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async sendEmail(req) {
    try {
      this.sdk = this.getSDK(req);

      const client = new postmark.ServerClient(config.mail_pass);

      const data = {
        From: req.body.from,
        To: req.body.to,
        Subject: req.body.subject,
        HtmlBody: req.body.body
      };

      const response = await client.sendEmail(data);

      return {
        error: false,
        model: response,
        message: "Email sent successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
