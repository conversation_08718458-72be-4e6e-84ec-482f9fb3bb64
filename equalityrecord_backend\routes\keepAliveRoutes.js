const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const { keepAlive } = require("../controllers/keepAliveController");

const publicMiddleWare = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// key: keep_alive
router.get("/", publicMiddleWare, keepAlive);

module.exports = router;
