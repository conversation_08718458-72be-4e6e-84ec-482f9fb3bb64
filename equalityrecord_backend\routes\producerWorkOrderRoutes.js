const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const UploadService = require("../../../services/UploadService");
const upload = UploadService.local_upload();

const { retrieveAll, addOne, getProducerWorkOrderDetails, updateOne } = require("../controllers/producerWorkOrderController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];
const filesPublicMiddlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, upload.array("files")];

// Key: producer_work_order
router.post("/retrieve", middlewares, retrieveAll);
router.post("/add", middlewares, addOne);
router.put("/:id", public, updateOne);
router.post("/public/details", public, getProducerWorkOrderDetails);

module.exports = router;
