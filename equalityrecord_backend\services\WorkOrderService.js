const aws = require("aws-sdk");
const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
const config = require("../../../config");

module.exports = class WorkOrderService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE wo.user_id = ${req.user_id}`;
      }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` wo.user_id = ${filter.user_id} `);
        }
        if (filter.status) {
          if (filter.status === "completed") {
            filterArr.push(` wo.status = 5 `);
          } else if (filter.status === "pending") {
            filterArr.push(` wo.status != 5 `);
          }
        }
        if (filter.employee_name) {
          // Assuming the `employee_name` can be either writer name, artist name, or engineer name
          filterArr.push(`
            (LOWER(e1.name) LIKE '%${filter.employee_name.toLowerCase()}%' OR
             LOWER(e2.name) LIKE '%${filter.employee_name.toLowerCase()}%' OR
             LOWER(e3.name) LIKE '%${filter.employee_name.toLowerCase()}%')
          `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let joinQuery = `
        LEFT JOIN equalityrecord_employee e1 ON e1.id = wo.writer_id
        LEFT JOIN equalityrecord_employee e2 ON e2.id = wo.artist_id
        LEFT JOIN equalityrecord_employee e3 ON e3.id = wo.engineer_id
      `;

      let countRawSql = `
      SELECT COUNT(*) AS total
      FROM equalityrecord_work_order wo
        ${joinQuery}
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let rawSql = `SELECT
                      wo.*, CONCAT(u.first_name, ' ', u.last_name) AS user_name
                    FROM equalityrecord_work_order wo
                      LEFT JOIN equalityrecord_user u ON u.id = wo.user_id
                    ${joinQuery}
                    ${filterQuery}
                    ORDER BY wo.id DESC
                    LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      const employees = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_employee;
      `);

      for (let resultItem of result) {
        if (resultItem.writer_id !== 0) {
          const writer = employees.filter((employee) => employee.id === resultItem.writer_id);
          resultItem.writer = writer[0];
        }

        if (resultItem.artist_id !== 0) {
          const artist = employees.filter((employee) => employee.id === resultItem.artist_id);
          resultItem.artist = artist[0];
        }

        if (resultItem.engineer_id !== 0) {
          const engineer = employees.filter((employee) => employee.id === resultItem.engineer_id);
          resultItem.engineer = engineer[0];
        }
      }

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      const existingWorkOrders = await this.sdk.get();

      let workOrderCode = "";
      // WORK_ORDER_CODE_FORMAT
      // YEAR-USER_ID-INCREMENT_NUMBER = 24-9-00001
      let userId = req.user_id;

      if (existingWorkOrders.length > 0) {
        const lastWorkOrder = existingWorkOrders[0];
        const lastWorkOrderCode = lastWorkOrder.workorder_code; // it can be 24-9-00001 or 24-0001

        const lastWorkOrderYear = lastWorkOrderCode.split("-")[0];

        let lastWorkOrderNumber = 0;
        if (lastWorkOrderCode.split("-").length === 3) {
          lastWorkOrderNumber = lastWorkOrderCode.split("-")[2];
        } else if (lastWorkOrderCode.split("-").length === 2) {
          lastWorkOrderNumber = lastWorkOrderCode.split("-")[1];
        }

        let currentYear = new Date().getFullYear().toString();
        currentYear = currentYear.substring(2, 4);

        if (lastWorkOrderYear === currentYear) {
          const newWorkOrderNumber = parseInt(lastWorkOrderNumber) + 1;
          workOrderCode = `${currentYear}-${userId}-${newWorkOrderNumber.toString().padStart(5, "0")}`;
        } else {
          workOrderCode = `${currentYear}-${userId}-00001`;
        }
      } else {
        const currentYear = new Date().getFullYear().toString();
        workOrderCode = `${currentYear}-${userId}-00001`;
      }

      const payload = {
        user_id: userId,
        writer_id: req.body.writer_id,
        due_date: req.body.due_date,
        artist_id: req.body.artist_id,
        engineer_id: req.body.engineer_id,
        workorder_code: workOrderCode,
        uuidv4: req.body.uuidv4,
        auto_approve: req.body.auto_approve,
        status: req.body.status,
        writer_notes: req.body.writer_notes ?? null,
        artist_deadline: req.body.artist_deadline ?? null,
        engineer_deadline: req.body.engineer_deadline ?? null,
        artist_engineer_deadline: req.body.artist_engineer_deadline ?? null,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      let subProjectIds = req.body.subproject_ids;
      // [1, 2, 3, 4, 5]
      if (subProjectIds.length === 1) {
        await this.sdk.rawQuery(`UPDATE equalityrecord_subproject SET workorder_id = ${result} WHERE id = ${subProjectIds[0]};`);
      } else if (subProjectIds.length > 1) {
        await this.sdk.rawQuery(`UPDATE equalityrecord_subproject SET workorder_id = ${result} WHERE id IN (${subProjectIds.join(",")});`);
      }

      let create_at = sqlDateFormat(new Date());
      let update_at = sqlDateTimeFormat(new Date());

      // insert writer, artist, engineer into equalityrecord_subproject_employee for each subproject
      // subproject_id, employee_id, employee_type, employee_cost, create_at, update_at
      for (let subProjectId of subProjectIds) {
        if (req.body.engineer_id) {
          await this.sdk.rawQuery(`
            INSERT INTO equalityrecord_subproject_employee (subproject_id, employee_id, employee_type, employee_cost, create_at, update_at)
            VALUES (${subProjectId}, ${req.body.engineer_id}, 'engineer', '${req.body.engineer_cost}', '${create_at}', '${update_at}');
          `);
        }
      }

      let writerEmail = "";
      let writerName = "";
      const writer = await this.sdk.rawQuery(`SELECT name, email FROM equalityrecord_employee WHERE id = ${req.body.writer_id};`);
      if (writer.length > 0) {
        writerEmail = writer[0].email;
        writerName = writer[0].name;
      }

      let artistEmail = "";
      let artistName = "";
      const artist = await this.sdk.rawQuery(`SELECT name, email FROM equalityrecord_employee WHERE id = ${req.body.artist_id};`);
      if (artist.length > 0) {
        artistEmail = artist[0].email;
        artistName = artist[0].name;
      }

      let voiceOverCount = 0;
      let songCount = 0;
      let trackingCount = 0;
      let totalEightCount = 0;

      // const subProjects = await this.sdk.rawQuery(`
      //   SELECT * FROM equalityrecord_subproject WHERE workorder_id = ${result};
      // `);

      let subProjects = await this.sdk.rawQuery(`
        SELECT
          sp.*,
          p.team_name, p.team_type, p.colors, p.division,
          c.program AS program_name
        FROM equalityrecord_subproject sp
        LEFT JOIN equalityrecord_project p ON p.id = sp.project_id
        LEFT JOIN equalityrecord_client c ON c.id = p.client_id
        WHERE sp.workorder_id = ${result} ORDER BY p.mix_date ASC;
      `);

      if (subProjects.length > 0) {
        for (let subProject of subProjects) {
          if (subProject.type_name.toLowerCase().includes("voiceover")) {
            voiceOverCount += 1;
          } else if (subProject.type_name.toLowerCase().includes("song")) {
            songCount += 1;
          } else if (subProject.type_name.toLowerCase().includes("tracking")) {
            trackingCount += 1;
          }
          totalEightCount += Number(subProject.eight_count);

          let survey = await this.sdk.rawQuery(`
            SELECT * FROM equalityrecord_survey WHERE project_id = ${subProject.project_id};
          `);

          if (survey.length > 0) {
            subProject.survey = survey[0];
          } else {
            subProject.survey = {};
          }

          let ideas = await this.sdk.rawQuery(`
            SELECT * FROM equalityrecord_subproject_idea si
              LEFT JOIN equalityrecord_idea i ON i.id = si.idea_id
              WHERE si.subproject_id = ${subProject.id};
          `);

          if (ideas.length > 0) {
            subProject.ideas = ideas;
          } else {
            subProject.ideas = [];
          }
        }
      }

      const userDetails = await this.sdk.rawQuery(`
        SELECT
          u.first_name, u.last_name, u.email, p.company_name
        FROM equalityrecord_user u
          LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
          WHERE u.id = '${req.user_id}';
      `);

      if (result) {
        return {
          error: false,
          id: result,
          model: {
            workorder_code: workOrderCode,
            writer_email: writerEmail,
            writer_name: writerName,
            artist_email: artistEmail,
            artist_name: artistName,
            voiceover_count: voiceOverCount,
            song_count: songCount,
            tracking_count: trackingCount,
            total_eight_count: totalEightCount,
            user: userDetails[0],
            division: subProjects[0].division,
            sub_projects: subProjects,
            due_date: req.body.due_date
          },
          message: "Work order added successfully"
        };
      } else {
        return {
          error: true,
          message: "Error creating work order"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async remove(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      const subProjects = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject WHERE workorder_id = ${req.params.id};
      `);

      let fileUrls = [];
      if (subProjects.length > 0) {
        if (subProjects.length > 1) {
          let projectFiles = await this.sdk.rawQuery(`
            SELECT id, url FROM equalityrecord_project_file WHERE subproject_id IN (${subProjects.map((row) => row.id).join(",")} AND workorder_id = ${
            req.params.id
          });
          `);

          if (projectFiles.length > 0) {
            fileUrls = projectFiles.map((row) => row.url);
          }

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_project_file WHERE subproject_id IN (${subProjects.map((row) => row.id).join(",")} AND workorder_id = ${req.params.id});
          `);

          for (let subProjectRow of subProjects) {
            await this.sdk.rawQuery(`DELETE FROM equalityrecord_subproject_employee WHERE subproject_id = ${subProjectRow.id} AND employee_type='engineer'`);
          }
        } else if (subProjects.length === 1) {
          let projectFiles = await this.sdk.rawQuery(`
            SELECT id, url FROM equalityrecord_project_file WHERE subproject_id = ${subProjects[0].id} AND workorder_id = ${req.params.id};
          `);

          if (projectFiles.length > 0) {
            fileUrls = projectFiles.map((row) => row.url);
          }

          await this.sdk.rawQuery(`
            DELETE FROM equalityrecord_project_file WHERE subproject_id = ${subProjects[0].id} AND workorder_id = ${req.params.id};
          `);

          await this.sdk.rawQuery(`DELETE FROM equalityrecord_subproject_employee WHERE subproject_id = ${subProjects[0].id} AND employee_type='engineer'`);
        }
      }

      if (fileUrls.length > 0) {
        await this.deleteMultipleS3Files(fileUrls);
      }

      await this.sdk.rawQuery(`
        UPDATE equalityrecord_subproject
        SET workorder_id = null, lyrics = null
        WHERE workorder_id = ${req.params.id};
      `);

      await this.sdk.delete({}, req.params.id);

      return {
        error: false,
        message: "Work order removed successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async view(req) {
    try {
      this.sdk = this.getSDK(req);

      let where = "";
      if (req.role !== "admin") {
        where = `WHERE wo.id = ${req.params.id} AND wo.user_id = ${req.user_id}`;
      } else {
        where = `WHERE wo.id = ${req.params.id}`;
      }

      let rawSql = `
        SELECT wo.*
        FROM equalityrecord_work_order wo
        ${where}
        ;
      `;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        // order by p.mix_date
        let subProjects = await this.sdk.rawQuery(`
          SELECT
            sp.*,
            p.team_name, p.team_type, p.colors, p.division,
            c.program AS program_name
          FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON p.id = sp.project_id
          LEFT JOIN equalityrecord_client c ON c.id = p.client_id
          WHERE sp.workorder_id = ${result[0].id}
          ORDER BY p.mix_date ASC;
        `);

        // retrieve instrumental files -> uploaded by writer
        this.sdk.setTable("project_file");
        let instrumentals = await this.sdk.get({
          workorder_id: result[0].id,
          employee_type: "writer",
          // employee_id: (result[0].employee_id),
          type: "instrumental"
        });

        result[0].instrumentals = instrumentals.length > 0 ? instrumentals : [];

        // retrieve admin instrumental (loops) files -> uploaded by admin
        this.sdk.setTable("project_file");
        let adminInstrumentals = await this.sdk.get({
          // workorder_id: result[0].id,
          employee_type: "writer",
          type: "instrumental",
          is_from_admin: 1
        });

        // retrieve demo files -> uploaded by writer
        this.sdk.setTable("project_file");
        let demos = await this.sdk.get({
          workorder_id: Number(result[0].id),
          employee_type: "writer",
          type: "demo"
        });

        // retrieve session files -> uploaded by artist
        this.sdk.setTable("project_file");
        let sessions = await this.sdk.get({
          workorder_id: Number(result[0].id),
          employee_type: "artist",
          type: "session"
        });

        result[0].sessions = sessions.length > 0 ? sessions : [];

        // retrieve master files -> uploaded by engineer
        this.sdk.setTable("project_file");
        let masters = await this.sdk.get({
          workorder_id: Number(result[0].id),
          employee_type: "engineer",
          type: "master"
        });

        let subProjectEmployees = await this.sdk.rawQuery(`
          SELECT
            *
          FROM equalityrecord_subproject_employee;
        `);

        let writerTotalCost = 0;
        let artistTotalCost = 0;
        let engineerTotalCost = 0;

        // equalityrecord_subproject_employee table
        // id, subproject_id, employee_id, employee_type, employee_cost

        // push to subProjects and filter out the demo files by subproject_id
        if (subProjects.length > 0) {
          for (let subProject of subProjects) {
            subProject.demos = [];
            subProject.masters = [];
            subProject.admin_writer_instrumentals = [];

            subProject.employees = [];
            if (subProjectEmployees.length > 0) {
              for (let subProjectEmployee of subProjectEmployees) {
                if (Number(subProjectEmployee.subproject_id) === Number(subProject.id) && Number(subProject.workorder_id) === Number(result[0].id)) {
                  subProject.employees.push(subProjectEmployee);
                }
              }
            }

            // calculate writer, artist, engineer cost
            if (subProject.employees.length > 0) {
              for (let employee of subProject.employees) {
                if (employee.employee_type === "writer") {
                  writerTotalCost += Number(employee.employee_cost);
                } else if (employee.employee_type === "artist") {
                  artistTotalCost += Number(employee.employee_cost);
                } else if (employee.employee_type === "engineer") {
                  engineerTotalCost += Number(employee.employee_cost);
                }
              }
            }

            if (demos.length > 0) {
              for (let demo of demos) {
                if (Number(demo.subproject_id) === Number(subProject.id)) {
                  subProject.demos.push(demo);
                }
              }
            }

            if (masters.length > 0) {
              for (let master of masters) {
                if (Number(master.subproject_id) === Number(subProject.id)) {
                  subProject.masters.push(master);
                }
              }
            }

            if (adminInstrumentals.length > 0) {
              for (let adminInstrumental of adminInstrumentals) {
                if (Number(adminInstrumental.subproject_id) === Number(subProject.id)) {
                  subProject.admin_writer_instrumentals.push(adminInstrumental);
                }
              }
            }

            let survey = await this.sdk.rawQuery(`
              SELECT * FROM equalityrecord_survey WHERE project_id = ${subProject.project_id};
            `);

            if (survey.length > 0) {
              subProject.survey = survey[0];
            } else {
              subProject.survey = {};
            }

            let ideas = await this.sdk.rawQuery(`
              SELECT * FROM equalityrecord_subproject_idea si
                LEFT JOIN equalityrecord_idea i ON i.id = si.idea_id
                WHERE si.subproject_id = ${subProject.id};
            `);

            if (ideas.length > 0) {
              subProject.ideas = ideas;
            } else {
              subProject.ideas = [];
            }
          }
        }

        result[0].sub_projects = subProjects;

        const userDetails = await this.sdk.rawQuery(`
          SELECT
            u.first_name, u.last_name, u.email, p.company_name
          FROM equalityrecord_user u
            LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id = '${req.user_id}';
        `);

        result[0].user = userDetails.length > 0 ? userDetails[0] : {};

        if (result[0].writer_id !== 0) {
          const writer = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].writer_id};
          `);
          result[0].writer = writer[0];

          const writerCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE employee_id = ${result[0].writer_id};
          `);
          result[0].writerCost = writerCost.length > 0 ? writerCost[0].employee_cost : 0;
        }

        if (result[0].artist_id !== 0) {
          const artist = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].artist_id};
          `);
          result[0].artist = artist[0];

          const artistCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE employee_id = ${result[0].artist_id};
          `);
          result[0].artistCost = artistCost.length > 0 ? artistCost[0].employee_cost : 0;
        }

        if (result[0].engineer_id !== 0) {
          const engineer = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].engineer_id};
          `);
          result[0].engineer = engineer[0];

          const engineerCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE employee_id = ${result[0].engineer_id};
          `);
          result[0].engineerCost = engineerCost.length > 0 ? engineerCost[0].employee_cost : 0;
        }

        result[0].writerTotalCost = writerTotalCost;
        result[0].artistTotalCost = artistTotalCost;
        result[0].engineerTotalCost = engineerTotalCost;
      }

      return result[0];
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      // status
      // 1: 'Writer',
      // 2: 'Artist',
      // 3: 'Engineer'
      // 4: 'Rejected',
      // 5: 'Completed',
      // 6: 'Inactive',

      await this.sdk.update(
        filterEmptyFields({
          user_id: req.body.user_id,
          due_date: req.body.due_date,
          writer_id: req.body.writer_id,
          artist_id: req.body.artist_id,
          engineer_id: req.body.engineer_id,
          workorder_code: req.body.workorder_code,
          uuidv4: req.body.uuidv4,
          note: req.body.note,
          note_status: req.body.note_status,
          writer_submit_status: req.body.writer_submit_status,
          artist_submit_status: req.body.artist_submit_status,
          engineer_submit_status: req.body.engineer_submit_status,
          engineer_deadline: req.body.engineer_deadline,
          auto_approve: req.body.auto_approve,
          is_viewed: req.body.is_viewed,
          status: req.body.status,
          writer_submission_datetime: req.body.writer_submission_datetime,
          writer_artist_submission_datetime: req.body.writer_artist_submission_datetime,
          writer_artist_engineer_submission_datetime: req.body.writer_artist_engineer_submission_datetime,
          artist_submission_datetime: req.body.artist_submission_datetime,
          artist_engineer_submission_datetime: req.body.artist_engineer_submission_datetime,
          engineer_submission_datetime: req.body.engineer_submission_datetime,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Work order updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateStatus(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      // workorder status
      // 0: 'Waiting for approval',
      // 1: 'Approved',
      // 2: 'Rejected',
      // 3: 'Completed',

      const payload = {
        status: Number(req.body.status),
        update_at: sqlDateTimeFormat(new Date())
      };

      await this.sdk.update(payload, Number(req.body.id));

      return {
        error: false,
        message: "Work order status updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async addNote(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      const payload = {
        note: req.body.note,
        update_at: sqlDateTimeFormat(new Date())
      };

      await this.sdk.update(payload, Number(req.body.id));

      return {
        error: false,
        message: "Work order note added successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getWorkOrderDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("work_order");

      let result = null;

      if (req.body.employee_type === "writer") {
        result = await this.sdk.rawQuery(`
          SELECT
            wo.*,
            e.name AS writer_name, e.email AS writer_email,
            ep.company_name AS user_company_name,
            CONCAT(u.first_name, ' ', u.last_name) AS user_name
          FROM equalityrecord_work_order wo
          LEFT JOIN equalityrecord_employee e ON e.id = wo.writer_id
          LEFT JOIN equalityrecord_profile ep on e.user_id = ep.user_id
          LEFT JOIN equalityrecord_user u ON u.id = e.user_id
          WHERE wo.uuidv4 = '${req.body.uuidv4}';
        `);
      } else if (req.body.employee_type === "artist") {
        result = await this.sdk.rawQuery(`
          SELECT
            wo.*,
            e.name AS artist_name, e.email AS artist_email,
            ep.company_name AS user_company_name,
            CONCAT(u.first_name, ' ', u.last_name) AS user_name
          FROM equalityrecord_work_order wo
          LEFT JOIN equalityrecord_employee e ON e.id = wo.artist_id
          LEFT JOIN equalityrecord_profile ep on e.user_id = ep.user_id
          LEFT JOIN equalityrecord_user u ON u.id = e.user_id
          WHERE wo.uuidv4 = '${req.body.uuidv4}';
        `);
      } else if (req.body.employee_type === "engineer") {
        result = await this.sdk.rawQuery(`
          SELECT
            wo.*,
            e.name AS engineer_name, e.email AS engineer_email,
            ep.company_name AS user_company_name,
            CONCAT(u.first_name, ' ', u.last_name) AS user_name
          FROM equalityrecord_work_order wo
          LEFT JOIN equalityrecord_employee e ON e.id = wo.engineer_id
          LEFT JOIN equalityrecord_profile ep on e.user_id = ep.user_id
          LEFT JOIN equalityrecord_user u ON u.id = e.user_id
          WHERE wo.uuidv4 = '${req.body.uuidv4}';
        `);
      } else if (req.body.employee_type === "engineer_artist") {
        result = await this.sdk.rawQuery(`
          SELECT
            wo.*,
            e1.name AS engineer_name, e1.email AS engineer_email,
            e2.name AS artist_name, e2.email AS artist_email,
            ep.company_name AS user_company_name,
            CONCAT(u1.first_name, ' ', u1.last_name) AS engineer_user_name,
            CONCAT(u2.first_name, ' ', u2.last_name) AS artist_user_name
          FROM equalityrecord_work_order wo
          LEFT JOIN equalityrecord_employee e1 ON e1.id = wo.engineer_id
          LEFT JOIN equalityrecord_employee e2 ON e2.id = wo.artist_id
          LEFT JOIN equalityrecord_profile ep on e1.user_id = ep.user_id
          LEFT JOIN equalityrecord_user u1 ON u1.id = e1.user_id
          LEFT JOIN equalityrecord_user u2 ON u2.id = e2.user_id
          WHERE wo.uuidv4 = '${req.body.uuidv4}';
        `);
      }

      // console.log("result", result);

      if (result.length > 0) {
        let subProjects = await this.sdk.rawQuery(`
          SELECT
            sp.*,
            p.team_name, p.team_type,
            s.theme_of_the_routine,
            c.program AS program_name
          FROM equalityrecord_subproject sp
          LEFT JOIN equalityrecord_project p ON p.id = sp.project_id
          LEFT JOIN equalityrecord_client c ON c.id = p.client_id
          LEFT JOIN equalityrecord_survey s ON s.project_id = p.id  
          WHERE sp.workorder_id = ${result[0].id} ORDER BY p.mix_date ASC;
        `);
        // retrieve instrumental (loops) files -> uploaded by writer
        this.sdk.setTable("project_file");
        let instrumentals = await this.sdk.get({
          workorder_id: result[0].id,
          employee_type: "writer",
          // employee_id: (result[0].employee_id),
          type: "instrumental"
        });

        result[0].instrumentals = instrumentals.length > 0 ? instrumentals : [];

        // retrieve admin instrumental (loops) files -> uploaded by admin
        this.sdk.setTable("project_file");
        let adminInstrumentals = await this.sdk.get({
          // workorder_id: result[0].id,
          employee_type: "writer",
          type: "instrumental",
          is_from_admin: 1
        });

        // retrieve demo files -> uploaded by writer
        this.sdk.setTable("project_file");
        let demos = await this.sdk.get({
          workorder_id: Number(result[0].id),
          employee_type: "writer",
          type: "demo"
        });
        // demos = [
        //   { id: 1, project_id: 1, subproject_id: 1, employee_id: 1, workorder_id: 1, employee_type: writer, type: demo, url: '', description: '', create_at: '', update_at: '' },
        //   { id: 2, project_id: 1, subproject_id: 1, employee_id: 1, workorder_id: 1, employee_type: writer, type: demo, url: '', description: '', create_at: '', update_at: '' },
        // ]

        // retrieve session files -> uploaded by artist
        this.sdk.setTable("project_file");
        let sessions = await this.sdk.get({
          workorder_id: Number(result[0].id),
          employee_type: "artist",
          type: "session"
        });

        result[0].sessions = sessions.length > 0 ? sessions : [];

        // retrieve master files -> uploaded by engineer
        this.sdk.setTable("project_file");
        let masters = await this.sdk.get({
          workorder_id: Number(result[0].id),
          employee_type: "engineer",
          type: "master"
        });

        // push to subProjects and filter out the demo files by subproject_id
        if (subProjects.length > 0) {
          for (let subProject of subProjects) {
            subProject.demos = [];
            subProject.masters = [];
            subProject.admin_writer_instrumentals = [];

            if (demos.length > 0) {
              for (let demo of demos) {
                if (Number(demo.subproject_id) === Number(subProject.id)) {
                  subProject.demos.push(demo);
                }
              }
            }

            if (masters.length > 0) {
              for (let master of masters) {
                if (Number(master.subproject_id) === Number(subProject.id)) {
                  subProject.masters.push(master);
                }
              }
            }

            if (adminInstrumentals.length > 0) {
              for (let adminInstrumental of adminInstrumentals) {
                if (Number(adminInstrumental.subproject_id) === Number(subProject.id)) {
                  subProject.admin_writer_instrumentals.push(adminInstrumental);
                }
              }
            }

            let ideas = await this.sdk.rawQuery(`
              SELECT * FROM equalityrecord_subproject_idea si
                LEFT JOIN equalityrecord_idea i ON i.id = si.idea_id
                WHERE si.subproject_id = ${subProject.id};
            `);

            if (ideas.length > 0) {
              subProject.ideas = ideas;
            } else {
              subProject.ideas = [];
            }
          }
        }

        result[0].sub_projects = subProjects;

        if (result[0].writer_id !== 0) {
          const writer = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].writer_id};
          `);
          result[0].writer = writer[0];

          const writerCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE id = ${result[0].writer_id};
          `);
          result[0].writerCost = writerCost.length > 0 ? writerCost[0].employee_cost : 0;
        }

        if (result[0].artist_id !== 0) {
          const artist = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].artist_id};
          `);
          result[0].artist = artist[0];

          const artistCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE id = ${result[0].artist_id};
          `);
          result[0].artistCost = artistCost.length > 0 ? artistCost[0].employee_cost : 0;
        }

        if (result[0].engineer_id !== 0) {
          const engineer = await this.sdk.rawQuery(`
            SELECT
              e.*
            FROM equalityrecord_employee e
            WHERE e.id = ${result[0].engineer_id};
          `);
          result[0].engineer = engineer[0];

          const engineerCost = await this.sdk.rawQuery(`
            SELECT
              employee_cost
            FROM equalityrecord_subproject_employee
            WHERE id = ${result[0].engineer_id};
          `);
          result[0].engineerCost = engineerCost.length > 0 ? engineerCost[0].employee_cost : 0;
        }

        return {
          error: false,
          message: "Work order exists",
          model: result[0]
        };
      } else {
        return {
          error: true,
          message: "Work order does not exist",
          model: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteWorkOrdersWithNoSubProjects(req) {
    try {
      this.sdk = this.getSDK(req);

      // Find all work orders that have no associated sub-projects
      const workOrders = await this.sdk.rawQuery(`
        SELECT wo.id
        FROM equalityrecord_work_order wo
        LEFT JOIN equalityrecord_subproject sp ON wo.id = sp.workorder_id
        WHERE sp.id IS NULL;
      `);

      if (workOrders.length === 0) {
        return {
          error: false,
          message: "No work orders without sub-projects found",
          count: 0
        };
      }

      // Get all work order IDs
      const workOrderIds = workOrders.map((wo) => wo.id);

      // Delete any associated files from the project_file table
      const files = await this.sdk.rawQuery(`
        SELECT url FROM equalityrecord_project_file 
        WHERE workorder_id IN (${workOrderIds.join(",")});
      `);

      if (files.length > 0) {
        // Delete files from S3 if they exist
        const fileUrls = files.map((file) => file.url);
        await this.deleteMultipleS3Files(fileUrls);

        // Delete file records from database
        await this.sdk.rawQuery(`
          DELETE FROM equalityrecord_project_file 
          WHERE workorder_id IN (${workOrderIds.join(",")});
        `);
      }

      // Delete work orders
      await this.sdk.rawQuery(`
        DELETE FROM equalityrecord_work_order 
        WHERE id IN (${workOrderIds.join(",")});
      `);

      return {
        error: false,
        message: `Successfully deleted ${workOrderIds.length} work orders with no sub-projects`,
        count: workOrderIds.length,
        deleted_ids: workOrderIds
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateLyrics(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject");

      const payload = {
        lyrics: req.body.lyrics,
        update_at: sqlDateTimeFormat(new Date())
      };

      await this.sdk.update(payload, Number(req.body.subproject_id));

      return {
        error: false,
        message: "Lyrics updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async uploadFilesData(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project_file");

      let attachments = JSON.parse(req.body.attachments);

      if (attachments.length > 0) {
        for (let attachment of attachments) {
          const payload = {
            project_id: req.body.project_id ?? null,
            subproject_id: req.body.subproject_id ?? null,
            workorder_id: req.body.workorder_id ?? 0,
            employee_id: req.body.employee_id,
            employee_type: req.body.employee_type,
            type: req.body.type,
            url: attachment,
            description: req.description ?? null,
            is_from_admin: req.body.is_from_admin ?? 0,
            create_at: sqlDateTimeFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          };
          await this.sdk.insert(payload);
        }
      }

      return true;
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteFilesData(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project_file");

      await this.sdk.rawQuery(`
        DELETE FROM equalityrecord_project_file
        WHERE workorder_id = ${Number(req.body.workorder_id)}
        AND employee_type = '${req.body.employee_type}'
        AND employee_id = ${Number(req.body.employee_id)}
        AND type = ${Number(req.body.type)}
      `);

      return true;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllFilesByWorkOrderId(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_project_file WHERE workorder_id = ${Number(req.params.id)}
      `);

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllEmployeeFilesByWorkOrderId(req, employeeType) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT *
          FROM equalityrecord_project_file
          WHERE workorder_id = ${Number(req.params.id)}
          AND employee_type = '${employeeType}'
      `);

      const lyrics = await this.sdk.rawQuery(`
        SELECT
          es.id, es.lyrics, es.type, es.type_name, ec.program, ep.team_name
        FROM equalityrecord_subproject es
          INNER JOIN equalityrecord_project ep on es.project_id = ep.id
          INNER JOIN equalityrecord_client ec on ep.client_id = ec.id
        WHERE workorder_id = ${Number(req.params.id)}
      `);

      return {
        error: false,
        list: result.length > 0 ? result : [],
        lyrics: lyrics.length > 0 ? lyrics : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOneS3File(url) {
    try {
      const s3 = new aws.S3({
        accessKeyId: config.aws_key,
        secretAccessKey: config.aws_secret
      });

      // https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo.png
      const fileName = url.split("/").pop();

      const params = {
        Bucket: config.aws_bucket,
        Key: fileName
      };

      // s3.deleteObject(params, function (err, data) {
      //   if (err) console.log(err, err.stack);
      //   else console.log("S3 file deleted successfully");
      // });

      return {
        error: false,
        message: "S3 file deleted successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteMultipleS3Files(urls) {
    try {
      const s3 = new aws.S3({
        accessKeyId: config.aws_key,
        secretAccessKey: config.aws_secret
      });

      // urls = ['https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo.png', 'https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo2.png]

      if (urls.length > 0) {
        /* s3.deleteObjects(
          {
            Bucket: config.aws_bucket,
            Delete: {
              Objects: urls.map((url) => {
                const fileName = url.split("/").pop();
                return {
                  Key: fileName
                };
              })
            }
          },
          function (err, data) {
            if (err) console.log(err, err.stack);
            else console.log("S3 file(s) deleted successfully");
          }
        );
        */

        return {
          error: false,
          message: "S3 file(s) deleted successfully"
        };
      } else {
        return {
          error: false,
          message: "No S3 file(s) to delete"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateWorkOrderEmployee(req, employeeId, employeeCost, employeeType) {
    try {
      this.sdk = this.getSDK(req);

      let workorder_id = req.body.workorder_id;

      const subProjects = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_subproject WHERE workorder_id = ${workorder_id};
      `);

      let subProjectIds = [];
      if (subProjects.length > 0) {
        subProjectIds = subProjects.map((row) => row.id);
      }

      if (subProjectIds.length === 1) {
        // update equalityrecord_subproject_employee table
        // id, subproject_id, employee_id, employee_type, employee_cost

        const subProjectEmployee = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_subproject_employee WHERE subproject_id = ${subProjectIds[0]} AND employee_type = '${employeeType}';
        `);

        if (subProjectEmployee.length > 0) {
          await this.sdk.rawQuery(`
            UPDATE equalityrecord_subproject_employee
            SET employee_id = ${employeeId}, employee_cost = ${employeeCost}
            WHERE subproject_id = ${subProjectIds[0]} AND employee_type = '${employeeType}';
          `);

          await this.sdk.rawQuery(`
            UPDATE equalityrecord_work_order SET ${employeeType}_id = ${employeeId} WHERE id = ${workorder_id};
          `);
        } else {
          await this.sdk.rawQuery(`
            INSERT INTO equalityrecord_subproject_employee
              (subproject_id, employee_id, employee_type, employee_cost, create_at, update_at)
              VALUES
              (${subProjectIds[0]}, ${employeeId}, '${employeeType}', ${employeeCost}, '${sqlDateFormat(new Date())}', '${sqlDateTimeFormat(new Date())}');
          `);

          await this.sdk.rawQuery(`
            UPDATE equalityrecord_work_order SET ${employeeType}_id = ${employeeId} WHERE id = ${workorder_id};
          `);
        }
      } else if (subProjectIds.length > 1) {
        // update equalityrecord_subproject_employee table
        // id, subproject_id, employee_id, employee_type, employee_cost

        const subProjectEmployee = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_subproject_employee WHERE subproject_id IN (${subProjectIds.join(",")}) AND employee_type = '${employeeType}';
        `);

        if (subProjectEmployee.length > 0) {
          await this.sdk.rawQuery(`
            UPDATE equalityrecord_subproject_employee
            SET employee_id = ${employeeId}, employee_cost = ${employeeCost}
            WHERE subproject_id IN (${subProjectIds.join(",")}) AND employee_type = '${employeeType}';
          `);

          await this.sdk.rawQuery(`
            UPDATE equalityrecord_work_order SET ${employeeType}_id = ${employeeId} WHERE id = ${workorder_id};
          `);
        } else {
          for (let subProjectId of subProjectIds) {
            await this.sdk.rawQuery(`
              INSERT INTO equalityrecord_subproject_employee
                (subproject_id, employee_id, employee_type, employee_cost, create_at, update_at)
                VALUES
                (${subProjectId}, ${employeeId}, '${employeeType}', ${employeeCost}, '${sqlDateFormat(new Date())}', '${sqlDateTimeFormat(new Date())}');
            `);
          }

          await this.sdk.rawQuery(`
            UPDATE equalityrecord_work_order SET ${employeeType}_id = ${employeeId} WHERE id = ${workorder_id};
          `);
        }
      }

      const updatedWorkOrder = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_work_order 
        WHERE id = ${workorder_id};
      `);

      let writerEmail = "";
      let writerName = "";
      const writer = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_employee WHERE id = ${updatedWorkOrder[0].writer_id};
      `);
      if (writer.length > 0) {
        writerEmail = writer[0].email;
        writerName = writer[0].name;
      }

      let artistEmail = "";
      let artistName = "";
      const artist = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_employee WHERE id = ${updatedWorkOrder[0].artist_id};
      `);
      if (artist.length > 0) {
        artistEmail = artist[0].email;
        artistName = artist[0].name;
      }

      let engineerEmail = "";
      let engineerName = "";
      const engineer = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_employee WHERE id = ${updatedWorkOrder[0].engineer_id};
      `);
      if (engineer.length > 0) {
        engineerEmail = engineer[0].email;
        engineerName = engineer[0].name;
      }

      let voiceOverCount = 0;
      let songCount = 0;
      let trackingCount = 0;
      let totalEightCount = 0;

      let expressSubProjects = await this.sdk.rawQuery(`
        SELECT
          sp.*,
          p.team_name, p.team_type, p.colors, p.division,
          c.program AS program_name
        FROM equalityrecord_subproject sp
        LEFT JOIN equalityrecord_project p ON p.id = sp.project_id
        LEFT JOIN equalityrecord_client c ON c.id = p.client_id
        WHERE sp.workorder_id = ${workorder_id} ORDER BY p.mix_date ASC;
      `);

      if (expressSubProjects.length > 0) {
        for (let subProject of expressSubProjects) {
          if (subProject.type_name.toLowerCase().includes("voiceover")) {
            voiceOverCount += 1;
          } else if (subProject.type_name.toLowerCase().includes("tracking")) {
            trackingCount += 1;
          }

          if (subProject.is_song) {
            songCount += 1;
          }

          // else if (subProject.type_name.toLowerCase().includes("song")) {
          //   songCount += 1;
          // }

          totalEightCount += Number(subProject.eight_count);

          let survey = await this.sdk.rawQuery(`
            SELECT * FROM equalityrecord_survey WHERE project_id = ${subProject.project_id};
          `);

          if (survey.length > 0) {
            subProject.survey = survey[0];
          } else {
            subProject.survey = {};
          }

          let ideas = await this.sdk.rawQuery(`
            SELECT * FROM equalityrecord_subproject_idea si
              LEFT JOIN equalityrecord_idea i ON i.id = si.idea_id
              WHERE si.subproject_id = ${subProject.id};
          `);

          if (ideas.length > 0) {
            subProject.ideas = ideas;
          } else {
            subProject.ideas = [];
          }
        }
      }

      const userDetails = await this.sdk.rawQuery(`
        SELECT
          u.first_name, u.last_name, u.email, p.company_name
        FROM equalityrecord_user u
          LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
          WHERE u.id = '${req.user_id}';
      `);

      return {
        error: false,
        message: "Work order updated successfully",
        model: {
          workorder_code: updatedWorkOrder[0].workorder_code,
          writer_email: writerEmail,
          writer_name: writerName,
          artist_email: artistEmail,
          artist_name: artistName,
          engineer_email: engineerEmail,
          engineer_name: engineerName,
          voiceover_count: voiceOverCount,
          song_count: songCount,
          tracking_count: trackingCount,
          total_eight_count: totalEightCount,
          user: userDetails[0],
          division: expressSubProjects[0].division,
          sub_projects: expressSubProjects,
          due_date: updatedWorkOrder[0].due_date
        }
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
