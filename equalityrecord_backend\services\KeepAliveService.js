module.exports = class KeepAliveService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async keepAlive(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("cms");
      const result = await this.sdk.get();
      return {
        error: false
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
