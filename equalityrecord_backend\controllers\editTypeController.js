const { validateObject } = require("../../../services/ValidationService");
const EditTypeService = require("../services/EditTypeService");

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 100;
    const service = new EditTypeService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    // const validation = await validateObject(
    //   {
    //     url: "string",
    //     project_id: "required"
    //   },
    //   req.body
    // );

    // if (validation.error) return res.status(403).json(validation);

    const service = new EditTypeService();
    const result = await service.addOne(req);
    return res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.editOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new EditTypeService();
    const result = await service.editOne(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.viewOne = async (req, res) => {
  try {
    const service = new EditTypeService();
    const result = await service.viewOne(req);
    return res.status(200).json({
      error: result.error,
      model: result.model,
      next_item_id: result.nextItemId
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllClientCountTrack = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        client_id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new EditTypeService();
    const result = await service.getAllClientCountTrack(req, req.params.client_id);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.deleteOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new EditTypeService();
    const result = await service.deleteOne(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};
