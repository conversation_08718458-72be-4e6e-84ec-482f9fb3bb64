const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
const aws = require("aws-sdk");
const config = require("../../../config");

module.exports = class EditService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};

      let filterQuery = "";
      // if (req.role !== "admin") {
      //   filterQuery += `WHERE m.user_id = ${req.user_id}`;
      // }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id) {
          filterArr.push(` m.user_id = ${filter.user_id} `);
        }
        //  if (filter.user_id && req.role === "admin") {
        //    filterArr.push(` m.user_id = ${filter.user_id} `);
        //  }
        // if (filter.name) {
        //   filterArr.push(
        //     ` LOWER(mr.name) LIKE '%${filter.name.toLowerCase()}%' `
        //   );
        // }
        if (filter.project_id) {
          filterArr.push(` m.project_id = ${filter.project_id} `);
        }
        if (filter.url) {
          filterArr.push(` m.url = ${filter.url} `);
        }
        if (filter.edit_status) {
          filterArr.push(` m.edit_status = ${filter.edit_status} `);
        }
        if (filter.edit_type) {
          filterArr.push(` m.edit_type = ${filter.edit_type} `);
        }
        if (filter.edit_type_id) {
          filterArr.push(` m.edit_type = ${filter.edit_type_id} `);
        }
        if (filter.producer_id) {
          filterArr.push(` m.producer_id = ${filter.producer_id} `);
        }
        if (filter.is_paid) {
          filterArr.push(` m.is_paid = ${filter.is_paid} `);
        }
        if (filter.is_music) {
          filterArr.push(` m.is_music = ${filter.is_music} `);
        }
        if (filter.is_member) {
          filterArr.push(` m.is_member = ${filter.is_member} `);
        }
        if (filter.status) {
          filterArr.push(` m.status = ${filter.status} `);
        }
        if (filter.edit_type_name) {
          filterArr.push(` m.edit_type_name = '${filter.edit_type_name}' `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_edit m
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT m.*
        FROM equalityrecord_edit m
        ${filterQuery}
        ORDER BY m.id DESC
      LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit");

      const id = await this.sdk.insert({ ...req.body, create_at: sqlDateFormat(new Date()), update_at: sqlDateTimeFormat(new Date()) });

      if (id) {
        return {
          error: false,
          message: "Edit added successfully.",
          edit_id: id
        };
      } else {
        return {
          error: true,
          message: "Edit add failed.",
          edit_id: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async editOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit");

      /*
      if (req.body.edit_status === 1) {
        // add note to team details
        this.sdk.setTable("edit");
        const edit = await this.sdk.get({ id: req.params.id });

        this.sdk.setTable("team_details");

        const team_details = await this.sdk.get({ project_id: edit[0].project_id });

        if (team_details.length > 0) {
          await this.sdk.update(
            filterEmptyFields({
              notes: team_details[0].producer_notes + "\n" + req.body.producer_notes,
              update_at: sqlDateTimeFormat(new Date())
            }),
            team_details[0].id
          );
        } else {
          await this.sdk.insert({
            project_id: edit[0].project_id,
            producer_notes: edit[0].producer_notes,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });
        }
      }
      */

      await this.sdk.update(
        filterEmptyFields({
          ...req.body,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        edit_id: req.params.id,
        message: "Edit updated successfully."
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit");

      const result = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_edit WHERE id = ${req.params.id}`);

      return {
        error: false,
        model: result.length > 0 ? result[0] : {}
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("edit");
      const result = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_edit WHERE id = ${req.params.id}`);

      console.log("result", result);

      if (result.length > 0) {
        await this.sdk.delete({}, req.params.id);

        this.sdk.setTable("eight_count");
        // const eight_count = await this.sdk.get({ project_id: result[0].project_id });
        const eight_count = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_eight_count WHERE id = ${result[0].eight_count}`);
        if (eight_count.length > 0) {
          await this.sdk.delete({}, eight_count[0].id);
        }

        this.sdk.setTable("media");
        // const media = await this.sdk.get({ project_id: result[0].project_id });
        const media = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_media WHERE project_id = ${result[0].project_id}`);
        if (media.length > 0) {
          await this.sdk.delete({}, media[0].id);
        }

        for (let i = 0; i < JSON.parse(result[0].music_ids).length; i++) {
          await this.sdk.delete({}, JSON.parse(result[0].music_ids)[i]);
        }

        this.sdk.setTable("team_details");
        // const team_details = await this.sdk.get({ project_id: result[0].project_id });
        const team_details = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_team_details WHERE project_id = ${result[0].project_id}`);
        if (team_details.length > 0) {
          await this.sdk.delete({}, team_details[0].id);
        }

        // await this.deleteOneS3File(result[0].url);
        return {
          error: false,
          message: "Edit deleted successfully."
        };
      } else {
        return {
          error: true,
          message: "Edit not found."
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOneS3File(url) {
    try {
      const s3 = new aws.S3({
        accessKeyId: config.aws_key,
        secretAccessKey: config.aws_secret
      });

      // https://equalityrecords.s3.amazonaws.com/033684649013equality_records_logo.png
      const fileName = url.split("/").pop();

      const params = {
        Bucket: config.aws_bucket,
        Key: fileName
      };

      s3.deleteObject(params, function (err, data) {
        if (err) console.log(err, err.stack);
        else console.log("S3 file deleted successfully");
      });

      return {
        error: false,
        message: "S3 file deleted successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllClientCountTrack(req, user_id) {
    try {
      this.sdk = this.getSDK(req);

      // pick member_ids from equalityrecord_client_member, note it is saved as [9,2,3] now go to edit and get all the records where user_id is in member_ids

      const result = await this.sdk.rawQuery(`
        SELECT 
         *
          FROM equalityrecord_client_member as ecm
          WHERE client_id = ${user_id}
      `);

      let parsedMebmerIds = "";

      if (result.length > 0) {
        parsedMebmerIds =
          "(" +
          JSON.parse(result[0].member_ids).map((id) => {
            return id;
          }) +
          ")";
        result[0].member_ids + ")";
      }
      // join with  USER

      const rawSql = `SELECT up.company_name, up.user_id, up.office_email, u.first_name, u.last_name
FROM equalityrecord_profile AS up
JOIN equalityrecord_user AS u ON u.id = up.user_id
WHERE u.id IN ${parsedMebmerIds}
ORDER BY up.user_id DESC;
`;

      const result2 = await this.sdk.rawQuery(rawSql);

      console.log("result2", result2);

      for (let i = 0; i < result2.length; i++) {
        const rawSql = `SELECT *
        FROM equalityrecord_edit
        WHERE user_id = ${result2[i].user_id}
        ORDER BY id DESC;`;

        let result3 = await this.sdk.rawQuery(rawSql);
        result2[i].tracks = result3;
      }

      return {
        error: false,
        list: result2
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
