const {
  sqlDateFormat,
  sqlDateTimeFormat,
  filterEmptyFields,
} = require("../../../services/UtilService");
module.exports = class MediaService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};

      let filterQuery = "";
      // if (req.role !== "admin") {
      //   filterQuery += `WHERE m.user_id = ${req.user_id}`;
      // }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` m.user_id = ${filter.user_id} `);
        }
        // if (filter.name) {
        //   filterArr.push(
        //     ` LOWER(mr.name) LIKE '%${filter.name.toLowerCase()}%' `
        //   );
        // }
        if (filter.project_id) {
          filterArr.push(` m.project_id = ${filter.project_id} `);
        }
        if (filter.url) {
          filterArr.push(` m.url = ${filter.url} `);
        }
        if (filter.type) {
          filterArr.push(` m.type = ${filter.type} `);
        }
        if (filter.is_paid) {
          filterArr.push(` m.is_paid = ${filter.is_paid} `);
        }
        if (filter.is_music) {
          filterArr.push(` m.is_music = ${filter.is_music} `);
        }
        if (filter.is_member) {
          filterArr.push(` m.is_member = ${filter.is_member} `);
        }
        if (filter.status) {
          filterArr.push(` m.status = ${filter.status} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_media m
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT m.*
        FROM equalityrecord_media m
        ${filterQuery}
        ORDER BY m.id DESC
      LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("media");

      const id = await this.sdk.insert(
        filterEmptyFields({
          project_id: req.body.project_id,
          user_id: req.user_id,
          is_member: req.body.is_member,
          url: req.body.url,
          type: req.body.type,
          description: req.body.description,
          is_paid: req.body.is_paid,
          is_music: req.body.is_music,
          status: req.body.status,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date()),
        })
      );

      if (id) {
        return {
          error: false,
          message: "Media added successfully.",
          id,
        };
      } else {
        return {
          error: true,
          message: "Media add failed.",
          id: null,
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async editOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("media");

      await this.sdk.update(
        filterEmptyFields({
          user_id: req.user_id,
          url: req.body.url,
          type: req.body.type,
          description: req.body.description,
          is_paid: req.body.is_paid,
          is_music: req.body.is_music,
          status: req.body.status,
          update_at: sqlDateTimeFormat(new Date()),
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Media updated successfully.",
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("media");

      const result = await this.sdk.get({
        project_id: req.params.id,
      });

      return {
        error: false,
        model: result.length > 0 ? result[0] : {},
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("media");

      const result = await this.sdk.get({ id: req.params.id });

      if (result.length > 0) {
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Media deleted successfully.",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
