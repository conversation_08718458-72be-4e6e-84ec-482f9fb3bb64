const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  getAll,
  getOne,
  retrieveAll,
  createOrUpdateOne,
  removeManagerPermission,
  getOneManagerPermissionByManagerId,
} = require("../controllers/managerPermissionController");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];

// Key: manager_permission
router.get("/get_all", middlewares, getAll);
router.post("/retrieve_all", middlewares, retrieveAll);
router.put("/create_or_update_one/:id", middlewares, createOrUpdateOne);
router.get("/view", middlewares, getOne);
router.get("/view_admin/:id", middlewares, getOneManagerPermissionByManagerId);
router.delete("/remove/:id", middlewares, removeManagerPermission);

module.exports = router;
