const SubscriptionService = require("../services/SubscriptionService");

// Create a new subscription
exports.createSubscription = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createSubscription(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get subscription by user ID
exports.getSubscriptionByUserId = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getSubscriptionByUserId(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Update subscription
exports.updateSubscription = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.updateSubscription(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Cancel subscription
exports.cancelSubscription = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.cancelSubscription(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Create manager account
exports.createManagerAccount = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createManagerAccount(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Create invoice
exports.createInvoice = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createInvoice(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Process payment
exports.processPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.processPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get invoices
exports.getInvoices = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getInvoices(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get invoice by ID
exports.getInvoiceById = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getInvoiceById(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Record manual payment
exports.recordManualPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.recordManualPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Refund payment
exports.refundPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.refundPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Create coupon
exports.createCoupon = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createCoupon(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get all coupons
exports.getCoupons = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getCoupons(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get coupon by code
exports.getCouponByCode = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getCouponByCode(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Delete coupon
exports.deleteCoupon = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.deleteCoupon(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Create manager account with payment
exports.createManagerAccountWithPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createManagerAccountWithPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Create additional member (free for first member, payment required for additional)
exports.createAdditionalMemberWithPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createAdditionalMemberWithPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Confirm payment and create additional member
exports.confirmAdditionalMemberPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.confirmAdditionalMemberPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get public invoice by ID and token
exports.getPublicInvoice = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getPublicInvoice(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Update public invoice
exports.updatePublicInvoice = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.updatePublicInvoice(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Process public payment
exports.processPublicPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.processPublicPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Confirm public payment
exports.confirmPublicPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.confirmPublicPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Confirm payment
exports.confirmPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.confirmPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Create payment intent for invoice item's remaining balance
exports.createPaymentIntentForInvoiceItem = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.createPaymentIntentForInvoiceItem(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Confirm payment for invoice item's remaining balance
exports.confirmInvoiceItemPayment = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.confirmInvoiceItemPayment(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Update an existing invoice and its items
exports.updateInvoice = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.updateInvoice(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};

// Get company invoice payment report
exports.getCompanyInvoicePaymentReport = async (req, res) => {
  try {
    const service = new SubscriptionService();
    const result = await service.getCompanyInvoicePaymentReport(req);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      error: true,
      message: error.message
    });
  }
};
