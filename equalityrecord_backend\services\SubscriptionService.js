const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
const StripeService = require("../../../services/StripeService");

module.exports = class SubscriptionService {
  constructor() {
    this.sdk = null;
    this.stripeService = new StripeService();
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async createSubscription(req) {
    try {
      this.sdk = this.getSDK(req);

      const { userId, planId, companyId, paymentMethodId, memberCount = 1, isManager = false } = req.body;

      // Get plan details from database
      this.sdk.setTable("stripe_price");
      const plan = await this.sdk.get({ id: planId });

      if (!plan.length) {
        throw new Error("Subscription plan not found");
      }

      // Get user details
      this.sdk.setTable("user");
      const user = await this.sdk.get({ id: userId });

      if (!user.length) {
        throw new Error("User not found");
      }

      // Get or create Stripe customer
      let stripeCustomerId = user[0].stripe_customer_id;

      if (!stripeCustomerId) {
        const customerResult = await this.stripeService.createStripeCustomer({
          email: user[0].email,
          name: `${user[0].first_name} ${user[0].last_name}`,
          metadata: {
            user_id: userId,
            company_id: companyId
          }
        });

        stripeCustomerId = customerResult.id;

        // Update user with Stripe customer ID
        await this.sdk.update({ stripe_customer_id: stripeCustomerId }, userId);
      }

      // Attach payment method to customer if provided
      if (paymentMethodId) {
        await this.stripeService.attachPaymentMethodToCustomer({
          customerId: stripeCustomerId,
          paymentMethodId: paymentMethodId
        });

        // Set as default payment method
        await this.stripeService.updateStripeCustomer({
          customerId: stripeCustomerId,
          params: {
            invoice_settings: {
              default_payment_method: paymentMethodId
            }
          }
        });
      }

      // Calculate price based on member count
      const basePricePerMember = plan[0].price_per_member;
      const totalPrice = basePricePerMember * memberCount;

      // Create price in Stripe if needed
      const priceResult = await this.stripeService.createStripePrice({
        productId: plan[0].stripe_product_id,
        unitAmount: totalPrice * 100, // Convert to cents
        currency: "usd",
        recurring: {
          interval: "month"
        },
        metadata: {
          plan_id: planId,
          member_count: memberCount
        }
      });

      // Create subscription
      const subscriptionResult = await this.stripeService.createStripeSubscription({
        customerId: stripeCustomerId,
        priceId: priceResult.id,
        default_payment_method: paymentMethodId,
        metadata: {
          user_id: userId,
          company_id: companyId,
          plan_id: planId,
          member_count: memberCount,
          is_manager: isManager ? "true" : "false"
        }
      });

      // Save subscription to database
      this.sdk.setTable("stripe_subscription");
      const subscriptionId = await this.sdk.insert({
        user_id: userId,
        company_id: companyId,
        plan_id: planId,
        stripe_subscription_id: subscriptionResult.id,
        stripe_customer_id: stripeCustomerId,
        member_count: memberCount,
        is_manager: isManager ? 1 : 0,
        status: subscriptionResult.status,
        current_period_start: sqlDateTimeFormat(new Date(subscriptionResult.current_period_start * 1000)),
        current_period_end: sqlDateTimeFormat(new Date(subscriptionResult.current_period_end * 1000)),
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return {
        error: false,
        subscription: {
          id: subscriptionId,
          stripe_subscription_id: subscriptionResult.id,
          status: subscriptionResult.status,
          current_period_start: subscriptionResult.current_period_start,
          current_period_end: subscriptionResult.current_period_end
        }
      };
    } catch (error) {
      console.error("Error creating subscription:", error);
      throw new Error(error.message || "Failed to create subscription");
    }
  }

  async getSubscriptionByUserId(req) {
    try {
      this.sdk = this.getSDK(req);
      const userId = req.params.userId || req.user_id;

      this.sdk.setTable("stripe_subscription");
      const subscription = await this.sdk.get({ user_id: userId });

      if (!subscription.length) {
        return {
          error: false,
          subscription: null
        };
      }

      // Get plan details
      this.sdk.setTable("stripe_price");
      const plan = await this.sdk.get({ id: subscription[0].plan_id });

      return {
        error: false,
        subscription: {
          ...subscription[0],
          plan: plan.length ? plan[0] : null
        }
      };
    } catch (error) {
      console.error("Error getting subscription:", error);
      throw new Error(error.message || "Failed to get subscription");
    }
  }

  async updateSubscription(req) {
    try {
      this.sdk = this.getSDK(req);
      const { subscriptionId } = req.params;
      const { memberCount, planId } = req.body;

      // Get current subscription
      this.sdk.setTable("stripe_subscription");
      const subscription = await this.sdk.get({ id: subscriptionId });

      if (!subscription.length) {
        throw new Error("Subscription not found");
      }

      // Get plan details
      this.sdk.setTable("stripe_price");
      const plan = await this.sdk.get({ id: planId || subscription[0].plan_id });

      if (!plan.length) {
        throw new Error("Subscription plan not found");
      }

      // Calculate new price
      const newMemberCount = memberCount || subscription[0].member_count;
      const basePricePerMember = plan[0].price_per_member;
      const totalPrice = basePricePerMember * newMemberCount;

      // Create new price in Stripe
      const priceResult = await this.stripeService.createStripePrice({
        productId: plan[0].stripe_product_id,
        unitAmount: totalPrice * 100, // Convert to cents
        currency: "usd",
        recurring: {
          interval: "month"
        },
        metadata: {
          plan_id: plan[0].id,
          member_count: newMemberCount
        }
      });

      // Update subscription in Stripe
      const stripeSubscription = await this.stripeService.updateStripeSubscriptionAnchor({
        subscriptionId: subscription[0].stripe_subscription_id,
        priceId: priceResult.id,
        proration: "create_prorations"
      });

      // Update subscription in database
      const updateData = {
        member_count: newMemberCount,
        plan_id: planId || subscription[0].plan_id,
        status: stripeSubscription.status,
        current_period_start: sqlDateTimeFormat(new Date(stripeSubscription.current_period_start * 1000)),
        current_period_end: sqlDateTimeFormat(new Date(stripeSubscription.current_period_end * 1000)),
        updated_at: sqlDateTimeFormat(new Date())
      };

      await this.sdk.update(updateData, subscriptionId);

      return {
        error: false,
        subscription: {
          ...subscription[0],
          ...updateData
        }
      };
    } catch (error) {
      console.error("Error updating subscription:", error);
      throw new Error(error.message || "Failed to update subscription");
    }
  }

  async cancelSubscription(req) {
    try {
      this.sdk = this.getSDK(req);
      const { subscriptionId } = req.params;

      // Get current subscription
      this.sdk.setTable("stripe_subscription");
      const subscription = await this.sdk.get({ id: subscriptionId });

      if (!subscription.length) {
        throw new Error("Subscription not found");
      }

      // Cancel subscription in Stripe
      await this.stripeService.cancelStripeSubscription({
        subscriptionId: subscription[0].stripe_subscription_id
      });

      // Update subscription status in database
      await this.sdk.update(
        {
          status: "canceled",
          updated_at: sqlDateTimeFormat(new Date())
        },
        subscriptionId
      );

      return {
        error: false,
        message: "Subscription canceled successfully"
      };
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw new Error(error.message || "Failed to cancel subscription");
    }
  }

  async createManagerAccount(req) {
    try {
      this.sdk = this.getSDK(req);
      const { companyId, email, firstName, lastName, phone } = req.body;
      const userId = req.user_id;

      // Check if user is the main member of the company
      this.sdk.setTable("stripe_subscription");
      const subscription = await this.sdk.get({ user_id: userId, company_id: companyId });

      if (!subscription.length) {
        throw new Error("You are not authorized to create a manager account for this company");
      }

      // Check if manager account already exists
      this.sdk.setTable("user");
      const existingManager = await this.sdk.rawQuery(`
        SELECT u.* FROM equalityrecord_user u
        JOIN equalityrecord_subscription s ON s.user_id = u.id
        WHERE s.company_id = ${companyId} AND s.is_manager = 1
      `);

      if (existingManager.length > 0) {
        throw new Error("A manager account already exists for this company");
      }

      // Create manager user
      this.sdk.setTable("user");
      const managerUserId = await this.sdk.insert({
        email: email || subscription[0].email, // Can use same email as member
        password: await require("../../../services/PasswordService").hash(Math.random().toString(36).substring(2, 15)),
        role: "manager",
        verify: 1,
        status: 1,
        first_name: firstName,
        last_name: lastName,
        phone: phone,
        company_id: companyId,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Create manager subscription record
      this.sdk.setTable("stripe_subscription");
      await this.sdk.insert({
        user_id: managerUserId,
        company_id: companyId,
        plan_id: subscription[0].plan_id,
        stripe_subscription_id: subscription[0].stripe_subscription_id,
        stripe_customer_id: subscription[0].stripe_customer_id,
        member_count: subscription[0].member_count,
        is_manager: 1,
        status: subscription[0].status,
        current_period_start: subscription[0].current_period_start,
        current_period_end: subscription[0].current_period_end,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Update main subscription to indicate manager exists
      await this.sdk.update(
        {
          has_manager: 1,
          updated_at: sqlDateTimeFormat(new Date())
        },
        subscription[0].id
      );

      return {
        error: false,
        message: "Manager account created successfully",
        manager_id: managerUserId
      };
    } catch (error) {
      console.error("Error creating manager account:", error);
      throw new Error(error.message || "Failed to create manager account");
    }
  }

  async createManagerAccountWithPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { companyId, email, firstName, lastName, phone, paymentMethodId } = req.body;
      const userId = req.user_id;

      // Check if user is the main member of the company
      this.sdk.setTable("stripe_subscription");
      const subscription = await this.sdk.get({ user_id: userId, company_id: companyId });

      if (!subscription.length) {
        throw new Error("You are not authorized to create a manager account for this company");
      }

      // Check if manager account already exists
      this.sdk.setTable("user");
      const existingManager = await this.sdk.rawQuery(`
        SELECT u.* FROM equalityrecord_user u
        JOIN equalityrecord_subscription s ON s.user_id = u.id
        WHERE s.company_id = ${companyId} AND s.is_manager = 1
      `);

      if (existingManager.length > 0) {
        throw new Error("A manager account already exists for this company");
      }

      // Process one-time payment of $100
      const stripeService = new (require("../../../services/StripeService"))();

      // Create a payment intent for $100
      const paymentIntent = await stripeService.createPaymentIntentManual({
        amount: 10000, // $100 in cents
        currency: "usd",
        payment_method_types: ["card"]
      });

      // Confirm the payment intent with the provided payment method
      await stripeService.stripe.paymentIntents.confirm(paymentIntent.id, {
        payment_method: paymentMethodId
      });

      // Create manager user
      this.sdk.setTable("user");
      const managerUserId = await this.sdk.insert({
        email: email || subscription[0].email, // Can use same email as member
        password: await require("../../../services/PasswordService").hash(Math.random().toString(36).substring(2, 15)),
        role: "manager",
        verify: 1,
        status: 1,
        first_name: firstName,
        last_name: lastName,
        phone: phone,
        company_id: companyId,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Create manager subscription record
      this.sdk.setTable("stripe_subscription");
      await this.sdk.insert({
        user_id: managerUserId,
        company_id: companyId,
        plan_id: subscription[0].plan_id,
        stripe_subscription_id: subscription[0].stripe_subscription_id,
        stripe_customer_id: subscription[0].stripe_customer_id,
        member_count: subscription[0].member_count,
        is_manager: 1,
        status: subscription[0].status,
        current_period_start: subscription[0].current_period_start,
        current_period_end: subscription[0].current_period_end,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Update main subscription to indicate manager exists
      await this.sdk.update(
        {
          has_manager: 1,
          updated_at: sqlDateTimeFormat(new Date())
        },
        subscription[0].id
      );

      // Record the payment in the database
      this.sdk.setTable("stripe_payment");
      await this.sdk.insert({
        user_id: userId,
        amount: 10000, // $100 in cents
        currency: "usd",
        payment_intent_id: paymentIntent.id,
        payment_method_id: paymentMethodId,
        status: "succeeded",
        type: "manager_creation",
        description: "One-time payment for manager account creation",
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return {
        error: false,
        message: "Manager account created successfully with payment",
        manager_id: managerUserId,
        payment_intent: paymentIntent
      };
    } catch (error) {
      console.error("Error creating manager account with payment:", error);
      throw new Error(error.message || "Failed to create manager account with payment");
    }
  }

  async createAdditionalMemberWithPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { email, firstName, lastName, phone } = req.body;
      const userId = req.user_id;

      // Check if user is a manager
      this.sdk.setTable("user");
      const manager = await this.sdk.get({ id: userId, role: "manager" });

      // if (!manager.length) {
      //   throw new Error("Only managers can create additional members");
      // }

      // Get the manager's subscription to find the company
      this.sdk.setTable("stripe_subscription");
      const managerSubscription = await this.sdk.get({ user_id: userId, is_manager: 1 });

      if (!managerSubscription.length) {
        throw new Error("Manager subscription not found");
      }

      const companyId = managerSubscription[0].company_id;

      // Check if the manager already has members
      this.sdk.setTable("stripe_subscription");
      const existingMembers = await this.sdk.rawQuery(`
        SELECT COUNT(*) as member_count 
        FROM ${this.sdk.getTable()} 
        WHERE company_id = ${companyId} 
        AND is_manager = 0 
        AND status != 'canceled'
      `);

      // If this is the first member, create it for free
      if (existingMembers[0].member_count < 1) {
        // Create the member without payment
        const memberId = await this._createAdditionalMember({
          companyId,
          email,
          firstName,
          lastName,
          phone,
          managerSubscription: managerSubscription[0],
          paymentIntentId: null,
          userId,
          isFree: true
        });

        return {
          error: false,
          message: "First additional member created successfully (free)",
          member_id: memberId
        };
      }

      // For additional members beyond the first one, require payment
      // Process one-time payment of $100
      const stripeService = new (require("../../../services/StripeService"))();

      // Create a payment intent for $100
      const paymentIntent = await stripeService.createPaymentIntentManual({
        amount: 10000, // $100 in cents
        currency: "usd",
        payment_method_types: ["card"],
        metadata: {
          company_id: companyId,
          user_id: userId,
          type: "additional_member_creation",
          email: email,
          first_name: firstName,
          last_name: lastName,
          phone: phone,
          handle_webhook_with: "confirmAdditionalMemberPayment"
        }
      });

      console.log("paymentIntent is ", paymentIntent);
      // If no paymentMethodId, return the payment intent for client-side confirmation
      return {
        error: false,
        message: "Payment intent created. Please confirm payment to create additional member.",
        payment_intent: paymentIntent,
        client_secret: paymentIntent.client_secret,
        stripe_checkout_url: null //`https://checkout.stripe.com/pay/${paymentIntent.client_secret}`
      };
    } catch (error) {
      console.error("Error creating additional member with payment:", error);
      throw new Error(error.message || "Failed to create additional member with payment");
    }
  }

  // Helper method to create an additional member after payment confirmation
  async _createAdditionalMember(data) {
    const { companyId, email, firstName, lastName, phone, managerSubscription, paymentIntentId, userId, isFree = false } = data;

    // Create new member user
    this.sdk.setTable("user");
    const memberUserId = await this.sdk.rawQuery(
      `INSERT INTO equalityrecord_user (email, password, role, verify, status, first_name, last_name, phone, company_id, create_at, update_at) VALUES ("${email}", "${await require("../../../services/PasswordService").hash(
        Math.random().toString(36).substring(2, 15)
      )}", "member", 1, 1, "${firstName}", "${lastName}", "${phone}", ${companyId}, "${sqlDateFormat(new Date())}", "${sqlDateTimeFormat(new Date())}")`
    );
    //     const memberUserId = await this.sdk.rawQuery({
    //   email: email,
    //   password: await require("../../../services/PasswordService").hash(Math.random().toString(36).substring(2, 15)),
    //   role: "member",
    //   verify: 1,
    //   status: 1,
    //   first_name: firstName,
    //   last_name: lastName,
    //   phone: phone,
    //   company_id: companyId,
    //   create_at: sqlDateFormat(new Date()),
    //   update_at: sqlDateTimeFormat(new Date())
    // });

    // Create member subscription record
    this.sdk.setTable("stripe_subscription");
    await this.sdk.rawQuery(
      `INSERT INTO equalityrecord_stripe_subscription (user_id, company_id, plan_id, stripe_subscription_id, stripe_customer_id, member_count, is_manager, status, current_period_start, current_period_end, create_at, update_at) VALUES (${
        memberUserId.insertId
      }, ${companyId}, ${managerSubscription.plan_id}, "${managerSubscription.stripe_subscription_id}", "${managerSubscription.stripe_customer_id}", 1, 0, "${
        managerSubscription.status
      }", "${managerSubscription.current_period_start}", "${managerSubscription.current_period_end}", "${sqlDateFormat(new Date())}", "${sqlDateTimeFormat(
        new Date()
      )}")`
    );

    // Update the main subscription's member count
    const mainSubscription = await this.sdk.rawQuery(`
      SELECT * FROM equalityrecord_stripe_subscription 
      WHERE company_id = ${companyId} 
      AND is_manager = 0 
      AND user_id != ${memberUserId}
      LIMIT 1
    `);

    if (mainSubscription.length > 0) {
      this.sdk.setTable("stripe_subscription");
      await this.sdk.rawQuery(
        // {
        //   member_count: mainSubscription[0].member_count + 1,
        //   updated_at: sqlDateTimeFormat(new Date())
        // },
        `UPDATE equalityrecord_stripe_subscription SET member_count = ${mainSubscription[0].member_count + 1}, updated_at = "${sqlDateTimeFormat(
          new Date()
        )}" WHERE id = ${mainSubscription[0].id}`
      );
    }

    // Record the payment in the database (if not free)
    if (!isFree && paymentIntentId) {
      this.sdk.setTable("stripe_payment");
      await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_stripe_payment (user_id, amount, currency, payment_intent_id, status, type, description, create_at, update_at) VALUES (${userId}, 10000, "usd", "${paymentIntentId}", "succeeded", "additional_member_creation", "One-time payment for additional member creation", "${sqlDateFormat(
          new Date()
        )}", "${sqlDateTimeFormat(new Date())}")`
      );
    }

    return memberUserId.insertId;
  }

  // Confirm payment and create additional member
  async confirmAdditionalMemberPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { paymentIntentId } = req.body;
      const userId = req.user_id;

      // Check if user is a manager
      this.sdk.setTable("user");
      const manager = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_user WHERE id = ${userId} AND role = "manager"`);

      // if (!manager.length) {
      //   throw new Error("Only managers can create additional members");
      // }

      // Retrieve the payment intent to get the metadata
      const stripeService = new (require("../../../services/StripeService"))();
      const paymentIntent = await stripeService.retrievePaymentIntent(paymentIntentId);

      if (!paymentIntent || paymentIntent.status !== "succeeded") {
        throw new Error("Payment has not been completed successfully");
      }

      // Extract member details from metadata
      const { company_id, email, first_name, last_name, phone } = paymentIntent.metadata;

      // Get the manager's subscription
      this.sdk.setTable("stripe_subscription");
      const managerSubscription = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_stripe_subscription WHERE user_id = ${userId} AND is_manager = 1`);

      if (!managerSubscription.length) {
        throw new Error("Manager subscription not found");
      }

      // Create the additional member
      const memberId = await this._createAdditionalMember({
        companyId: company_id,
        email,
        firstName: first_name,
        lastName: last_name,
        phone,
        managerSubscription: managerSubscription[0],
        paymentIntentId,
        userId,
        isFree: false
      });

      return {
        error: false,
        message: "Additional member created successfully after payment confirmation",
        member_id: memberId
      };
    } catch (error) {
      console.error("Error confirming payment and creating additional member:", error);
      throw new Error(error.message || "Failed to confirm payment and create additional member");
    }
  }

  async createInvoice(req) {
    try {
      this.sdk = this.getSDK(req);
      let { clientId, items, deposit, depositPercentage, notes, termsAndConditions, invoiceDate, dueDate } = req.body;

      // Require the ProjectService at the top level to create projects
      const ProjectService = require("./ProjectService");
      const projectService = new ProjectService();

      // For UUID generation
      const { v4: uuidv4 } = require("uuid");

      // Validate client exists
      this.sdk.setTable("client");
      const client = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_client WHERE id = ${clientId}`);

      if (!client.length) {
        throw new Error("Client not found");
      }

      let actualClientId = clientId;

      // Calculate subtotal, considering discounts
      const subtotal = items.reduce((sum, item) => {
        const itemPrice = item.price * item.quantity;
        const discountAmount = item.discount || 0;
        // const mixSeasonDiscount = item.mixSeasonDiscount || 0;
        return sum + (itemPrice - discountAmount);
      }, 0);

      // If manager, validate they can assign these producers
      if (req.role === "manager") {
        const managerMembers = await this.sdk.rawQuery(`SELECT mp.member_ids FROM equalityrecord_manager_permission mp WHERE mp.manager_id = ${req.user_id}`);

        if (!managerMembers.length) {
          throw new Error("No managed members found");
        }

        const managedMemberIds = JSON.parse(managerMembers[0].member_ids);

        // Validate all producers in items are managed by this manager
        for (const item of items) {
          // Check main producer
          if (item.producer && !managedMemberIds.includes(parseInt(item.producer))) {
            throw new Error(`Producer ${item.producer} is not managed by you`);
          }

          // Check additional producers
          if (item.producers && Array.isArray(item.producers)) {
            for (const producerId of item.producers) {
              if (!managedMemberIds.includes(parseInt(producerId))) {
                throw new Error(`Producer ${producerId} is not managed by you`);
              }
            }
          }
        }
      } else {
        // For regular members, force all items to use their own ID as producer
        items = items.map((item) => ({
          ...item,
          producer: req.user_id,
          producers: item.producers || [req.user_id]
        }));
      }

      // Generate access token
      const accessToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

      // Create invoice
      this.sdk.setTable("invoice");
      const invoiceId = await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_invoice (client_id, user_id, subtotal, total, deposit_amount, deposit_percentage, access_token, notes, terms_and_conditions, mix_season_id, status, invoice_date, due_date, create_at, update_at) VALUES (${actualClientId}, ${
          req.user_id
        }, ${subtotal}, ${subtotal}, ${subtotal * (depositPercentage || 0)}, ${depositPercentage || null}, "${accessToken}", "${notes || null}", "${
          termsAndConditions || null
        }", "${items[0].mixSeasonId || null}", "pending", "${invoiceDate || sqlDateFormat(new Date())}", "${dueDate || null}", "${sqlDateFormat(
          new Date()
        )}", "${sqlDateTimeFormat(new Date())}")`
      );

      // Track created projects
      const createdProjects = [];

      // Add invoice items and create projects for each item
      this.sdk.setTable("invoice_item");
      for (const item of items) {
        // Calculate item total with discounts
        const itemPrice = item.price * item.quantity;
        const discountAmount = item.discount || 0;
        const itemTotal = itemPrice - discountAmount;

        this.sdk.setTable("invoice_item");
        const invoiceItemId = await this.sdk.rawQuery(
          `INSERT INTO equalityrecord_invoice_item (invoice_id, description, quantity, price, total, producer, status, producers, discount, is_special, special_type, mix_season_id, mix_date, team_name, division, music_survey_due, routine_submission_due, estimated_completion, create_at) VALUES (${invoiceId}, "${
            item.description || ""
          }", ${item.quantity}, ${item.price}, ${itemTotal}, ${item.producer}, 5, "${
            item.producers ? JSON.stringify(item.producers) : JSON.stringify([item.producer])
          }", ${discountAmount}, ${item.isSpecial ? 1 : 0}, "${item.specialType || null}", "${item.mixSeasonId || null}", "${item.mixDate || null}", "${
            item.teamName || null
          }", "${item.division || null}", "${item.musicSurveyDue || null}", "${item.routineSubmissionDue || null}", "${
            item.estimatedCompletion || null
          }", "${sqlDateFormat(new Date())}")`
        );

        if (!item.isSpecial) {
          // Create project for this invoice item if required fields are present
          if (item.mixDate && item.teamName && item.mixSeasonId && item.mixTypeId) {
            try {
              // Create a project request object based on the invoice item
              const projectReq = {
                ...req, // Copy original request
                body: {
                  client_id: clientId,
                  user_id: item.producer,
                  mix_date: item.mixDate,
                  team_name: item.teamName,
                  mix_season_id: item.mixSeasonId,
                  mix_type_id: item.mixTypeId,
                  division: item.division || null,
                  team_type: item.teamType || 0,
                  discount: discountAmount || 0,
                  team_details_date: item.teamDetailsDate || null,
                  routine_submission_date: item.routineSubmissionDue || null,
                  estimated_delivery_date: item.estimatedCompletion || null,
                  payment_status: 5, // Mark as paid since it's associated with an invoice
                  uuidv4: uuidv4(), // Required for survey creation
                  is_song_project: item.specialType === "song" ? 1 : 0,
                  colors: item.colors || null,
                  invoice_id: invoiceId,
                  invoice_item_id: invoiceItemId
                }
              };

              // Create the project
              const projectResult = await projectService.addOne(projectReq);

              console.log("projectResult", projectResult);

              if (!projectResult.error) {
                // Update invoice item with the project ID
                this.sdk.setTable("invoice_item");
                await this.sdk.rawQuery(`UPDATE equalityrecord_invoice_item SET project_id = ${projectResult.project_id} WHERE id = ${invoiceItemId}`);

                createdProjects.push({
                  invoice_item_id: invoiceItemId,
                  project_id: projectResult.project_id,
                  survey_id: projectResult.survey_id
                });

                console.log(`Project created for invoice item ${invoiceItemId}: Project ID ${projectResult.project_id}`);
              } else {
                console.error(`Failed to create project for invoice item ${invoiceItemId}: ${projectResult.message}`);
              }
            } catch (projectError) {
              console.error(`Error creating project for invoice item ${invoiceItemId}:`, projectError.message);
              // Continue with other items even if one project creation fails
            }
          }
        }
      }

      // Generate unique payment link
      const paymentLink = `https://equalityrecords.com/invoice/pay/${invoiceId}`;

      // Update invoice with payment link
      this.sdk.setTable("invoice");
      await this.sdk.rawQuery(`UPDATE equalityrecord_invoice SET payment_link = "${paymentLink}" WHERE id = ${invoiceId}`);

      return {
        error: false,
        invoice_id: invoiceId,
        payment_link: paymentLink,
        created_projects: createdProjects.length > 0 ? createdProjects : undefined
      };
    } catch (error) {
      console.error("Error creating invoice:", error);
      throw new Error(error.message || "Failed to create invoice");
    }
  }

  async processPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const {
        // invoiceItemId,
        invoiceId,
        type,
        paymentMethodId,
        paymentType, // 'deposit', 'full',  'custom'
        paymentMethod, //  'check', 'stripe', 'bank_transfer', etc.
        attachmentUrl //
      } = req.body;

      // Get invoice details
      // this.sdk.setTable("invoice_item");
      // const invoiceItem = await this.sdk.get({ id: invoiceItemId });

      // if (!invoiceItem.length) {
      //   throw new Error("Invoice item not found");
      // }

      // Get invoice details
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_invoice WHERE id = ${invoiceId}`);

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Get client details
      this.sdk.setTable("client");
      const client = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_client WHERE id = ${invoice[0].client_id}`);

      this.sdk.setTable("user");
      const user = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_user WHERE id = ${invoice[0].user_id}`);

      if (!client.length) {
        throw new Error("Client not found");
      }

      // Get company information from user profile
      this.sdk.setTable("profile");
      const profile = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_profile WHERE user_id = ${invoice[0].user_id}`);

      const companyInfo = profile.length
        ? {
            company_name: profile[0].company_name,
            company_logo: profile[0].company_logo,
            office_email: profile[0].office_email,
            edit_policy_link: profile[0].edit_policy_link,
            company_address: user[0].company_address,
            phone: user[0].phone
          }
        : null;

      this.sdk.setTable("mix_season");
      const mixSeason = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_mix_season WHERE id = ${invoice[0].mix_season_id}`);

      // Determine payment amount
      let paymentAmount = 0;
      if (paymentType === "deposit") {
        paymentAmount = invoice[0].deposit_amount;
      } else if (paymentType === "full") {
        paymentAmount = invoice[0].total;
      }
      // else if (paymentType === "custom") {
      //   paymentAmount = invoiceItem[0].price;
      // }
      else {
        throw new Error("Invalid payment type");
      }

      let stripeFee = 0;
      let totalWithFee = 0;
      let checkoutSession = {
        id: null,
        status: null,
        url: null
      };

      if (paymentMethod != "check") {
        // Calculate Stripe fee
        const stripeFee = parseFloat(paymentAmount) * 0.029 + 0.3; // 2.9% + $0.30
        const totalWithFee = parseFloat(paymentAmount) + parseFloat(stripeFee);

        // Get or create Stripe
        let stripeCustomerId = user[0].stripe_uid;

        if (!stripeCustomerId) {
          const customerResult = await this.stripeService.createStripeCustomer({
            email: client[0].email,
            name: client[0].name,
            metadata: {
              client_id: client[0].id,
              user_id: user[0].id,
              handle_webhook_with: "confirmPayment"
            }
          });

          stripeCustomerId = customerResult.id;

          // Update client with Stripe customer ID
          this.sdk.setTable("user");
          await this.sdk.rawQuery(`UPDATE equalityrecord_user SET stripe_uid = "${stripeCustomerId}" WHERE id = ${user[0].id}`);
        }

        // Process payment
        // when payment is done
        checkoutSession = await this.stripeService.createCheckoutSession({
          line_items: [
            {
              price_data: {
                currency: "usd",
                product_data: {
                  name: "Invoice Payment"
                },
                unit_amount: Math.round(totalWithFee * 100)
              },
              quantity: 1
            }
          ],
          mode: "payment",
          payment_method_types: ["card"],
          success_url: `https://equalitydev.manaknightdigital.com/invoice/client-success?invoice_id=${invoiceId}`,
          cancel_url: `https://equalitydev.manaknightdigital.com/invoice/client-cancel?invoice_id=${invoiceId}`,
          metadata: {
            invoice_id: invoiceId,
            payment_type: paymentType
          }
        });
      }

      // Update invoice with payment details
      this.sdk.setTable("invoice");
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_invoice SET payment_id = "${checkoutSession.id}", payment_status = "${
          checkoutSession.status
        }", payment_method = "${paymentMethod}", payment_type = "${paymentType}", attachment_url = "${attachmentUrl}", payment_amount = "${paymentAmount}", payment_date = "${sqlDateTimeFormat(
          new Date()
        )}", status = "${
          checkoutSession.status === "succeeded" ? (paymentAmount >= invoice[0].total ? "paid" : "partially_paid") : "pending"
        }", update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${invoice[0].id}`
      );
      //   update_at: sqlDateTimeFormat(new Date())
      // });

      // Create payment record
      this.sdk.setTable("payment");
      const paymentId = await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_payment (invoice_id, client_id, amount, stripe_fee, total_charged, payment_method, payment_id, status, create_at, update_at) VALUES (${invoiceId}, ${
          client[0].id
        }, ${paymentAmount}, ${stripeFee}, ${totalWithFee}, "${paymentMethod}", "${checkoutSession.id}", "${
          checkoutSession.status ?? "pending"
        }", "${sqlDateTimeFormat(new Date())}", "${sqlDateTimeFormat(new Date())}")`
      );
      //   invoice_id: invoiceId,
      //   // invoice_item_id: invoiceItem[0].id,
      //   client_id: client[0].id,
      //   amount: paymentAmount,
      //   stripe_fee: stripeFee,
      //   total_charged: totalWithFee,
      //   payment_method: paymentMethod,
      //   payment_id: checkoutSession.id,
      //   status: checkoutSession.status ?? "pending",
      //   create_at: sqlDateTimeFormat(new Date()),
      //   update_at: sqlDateTimeFormat(new Date())
      // });

      return {
        error: false,
        payment_id: paymentId,
        status: checkoutSession.status ?? "pending",
        checkout_session: checkoutSession.id,
        stripe_checkout_url: checkoutSession.url,
        company_info: companyInfo,
        mix_season: mixSeason[0]
      };
    } catch (error) {
      console.error("Error processing payment:", error);
      throw new Error(error.message || "Failed to process payment");
    }
  }

  async confirmPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { paymentId } = req.body;

      // Get payment details
      this.sdk.setTable("payment");
      const payment = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_payment WHERE id = ${paymentId}`);

      if (!payment.length) {
        throw new Error("Payment not found");
      }

      // Verify payment status
      if (payment[0].status === "succeeded") {
        throw new Error("Payment is already succeeded");
      }

      // Confirm payment through Stripe
      const stripeService = new (require("../../../services/StripeService"))();
      const checkoutSession = await stripeService.retrieveCheckoutSession(payment[0].payment_id);

      // Update payment status
      this.sdk.setTable("payment");
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_payment SET status = "${checkoutSession.payment_status === "paid" ? "succeeded" : "failed"}", update_at = "${sqlDateTimeFormat(
          new Date()
        )}" WHERE id = ${paymentId}`
      );

      // update invoice status
      this.sdk.setTable("invoice");
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_invoice SET status = "${checkoutSession.payment_status === "paid" ? "succeeded" : "failed"}" WHERE id = ${payment[0].invoice_id}`
      );

      if (checkoutSession.payment_status === "paid") {
        let amount_left = payment[0].amount - payment[0].total_charged;

        if (amount_left > 0) {
          // get invoice items
          this.sdk.setTable("invoice_item");
          const invoiceItems = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_invoice_item WHERE invoice_id = ${payment[0].invoice_id}`);

          // update invoice item status
          for (const item of invoiceItems) {
            this.sdk.setTable("invoice_item");
            await this.sdk.rawQuery(
              `UPDATE equalityrecord_invoice_item SET status = 2, amount_left = ${amount_left / invoiceItems.length} WHERE id = ${item.id}`
            );

            this.sdk.setTable("project");
            const project = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_project WHERE invoice_item_id = ${item.id}`);
            if (project.length > 0) {
              // update project payment status
              this.sdk.setTable("project");
              await this.sdk.rawQuery(`UPDATE equalityrecord_project SET payment_status = 2 WHERE id = ${project[0].id}`);
            }
          }
        }
        return {
          error: false,
          status: "succeeded"
        };
      } else {
        return {
          error: true,
          status: "failed",
          message: "Payment failed"
        };
      }
    } catch (error) {
      console.error("Error confirming payment:", error);
      throw new Error(error.message || "Failed to confirm payment");
    }
  }

  async getInvoices(req) {
    try {
      this.sdk = this.getSDK(req);
      const { page = 1, limit = 10 } = req.query;
      const offset = (page - 1) * limit;

      let query = `
        SELECT 
    i.*, 
    c.program, 
    c.name AS client_name, 
    c.email AS client_email,
    GROUP_CONCAT(DISTINCT CONCAT(u.first_name, ' ', u.last_name)) AS producers
FROM equalityrecord_invoice i
JOIN equalityrecord_client c ON i.client_id = c.id
LEFT JOIN equalityrecord_invoice_item ii ON i.id = ii.invoice_id
LEFT JOIN equalityrecord_user u ON u.id IN (ii.producer)
      `;

      // Apply filters based on role
      if (req.role === "client") {
        query += ` WHERE c.id = ${req.client_id}`;
      } else if (req.role === "member") {
        query += ` WHERE ii.producer = ${req.user_id}`;
      } else if (req.role === "manager") {
        const managerMembers = await this.sdk.rawQuery(`
          SELECT mp.member_ids 
          FROM equalityrecord_manager_permission mp 
          WHERE mp.manager_id = ${req.user_id}
        `);

        if (managerMembers.length) {
          const managedMemberIds = JSON.parse(managerMembers[0].member_ids);
          query += ` WHERE ii.producer IN (${managedMemberIds.join(",")})`;
        } else {
          query += ` WHERE 1=0`; // No members to manage
        }
      }

      // Apply additional filters if provided
      if (req.body.filter) {
        const filter = req.body.filter;
        const whereClause = query.includes("WHERE") ? " AND " : " WHERE ";

        if (filter.program) {
          query += `${whereClause} c.program LIKE '%${filter.program}%'`;
        }

        if (filter.status) {
          query += `${whereClause} i.status = '${filter.status}'`;
        }

        if (filter.invoice_number) {
          query += `${whereClause} i.id = ${filter.invoice_number}`;
        }
      }

      // Group by invoice ID to combine producer names
      query += ` GROUP BY i.id`;

      // Add sorting and pagination
      query += ` ORDER BY i.create_at DESC LIMIT ${limit} OFFSET ${offset}`;

      // Execute query
      const invoices = await this.sdk.rawQuery(query);

      // Get total count
      const countQuery = query.split("GROUP BY")[0];
      const countResult = await this.sdk.rawQuery(`SELECT COUNT(id) as total FROM (${countQuery} GROUP BY i.id) as subquery`);

      // Get items for each invoice
      for (const invoice of invoices) {
        const itemsQuery = `
          SELECT ii.*, u.first_name as producer_first_name, u.last_name as producer_last_name
          FROM equalityrecord_invoice_item ii
          LEFT JOIN equalityrecord_user u ON ii.producer = u.id
          WHERE ii.invoice_id = ${invoice.id}
        `;
        invoice.items = await this.sdk.rawQuery(itemsQuery);
      }

      return {
        error: false,
        invoices,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(countResult[0].total / limit)
      };
    } catch (error) {
      console.error("Error getting invoices:", error);
      throw new Error(error.message || "Failed to get invoices");
    }
  }

  async getInvoiceById(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceId } = req.params;

      // Get invoice details
      const query = `
        SELECT i.*, c.program, c.name as client_name, c.email as client_email
        FROM equalityrecord_invoice i
        JOIN equalityrecord_client c ON i.client_id = c.id
        WHERE i.id = ${invoiceId}
      `;

      const invoice = await this.sdk.rawQuery(query);

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Get invoice items
      this.sdk.setTable("invoice_item");
      const items = await this.sdk.get({ invoice_id: invoiceId });

      // pick team_name from invoice_item where status is 1
      const teamNames = items.filter((item) => item.status === 3 || item.status === 1).map((item) => item.team_name);

      // Get payments
      this.sdk.setTable("payment");
      const payments = await this.sdk.get({ invoice_id: invoiceId });

      return {
        error: false,
        invoice: {
          ...invoice[0],
          team_names: teamNames
        },
        items,
        payments
      };
    } catch (error) {
      console.error("Error getting invoice:", error);
      throw new Error(error.message || "Failed to get invoice");
    }
  }

  async recordManualPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const {
        invoiceId,
        amount,
        paymentMethod, // 'check', 'cash', 'bank_transfer', etc.
        notes
      } = req.body;

      // Get invoice details
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceId });

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Create payment record
      this.sdk.setTable("payment");
      const paymentId = await this.sdk.insert({
        invoice_id: invoiceId,
        client_id: invoice[0].client_id,
        amount: amount,
        payment_method: paymentMethod,
        notes: notes,
        status: "succeeded",
        create_at: sqlDateTimeFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Update invoice status
      const totalPaid = await this.getTotalPaidForInvoice(invoiceId);
      const newStatus = totalPaid >= invoice[0].total ? "paid" : "partially_paid";

      this.sdk.setTable("invoice");
      await this.sdk.update(
        {
          payment_amount: totalPaid,
          payment_date: sqlDateTimeFormat(new Date()),
          status: newStatus,
          update_at: sqlDateTimeFormat(new Date())
        },
        invoiceId
      );

      return {
        error: false,
        payment_id: paymentId,
        status: "succeeded",
        invoice_status: newStatus
      };
    } catch (error) {
      console.error("Error recording manual payment:", error);
      throw new Error(error.message || "Failed to record manual payment");
    }
  }

  async getTotalPaidForInvoice(invoiceId) {
    this.sdk.setTable("payment");
    const payments = await this.sdk.get({ invoice_id: invoiceId, status: "succeeded" });

    let totalPaid = 0;
    payments.forEach((payment) => {
      totalPaid += parseFloat(payment.amount);
    });

    return totalPaid;
  }

  async refundPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const {
        paymentId,
        amount, // Optional, if not provided, full refund
        reason
      } = req.body;

      // Get payment details
      this.sdk.setTable("payment");
      const payment = await this.sdk.get({ id: paymentId });

      if (!payment.length) {
        throw new Error("Payment not found");
      }

      // Only refund Stripe payments
      if (!payment[0].payment_id) {
        throw new Error("Only Stripe payments can be refunded automatically");
      }

      // Process refund through Stripe
      const refundResult = await this.stripeService.createStripeRefund({
        paymentIntent: payment[0].payment_id,
        amount: amount ? Math.round(amount) : parseFloat(payment[0].amount), // Convert to cents if provided
        reason: reason || "requested_by_customer"
      });

      // Record refund
      this.sdk.setTable("refund");
      const refundId = await this.sdk.insert({
        payment_id: paymentId,
        invoice_id: payment[0].invoice_id,
        amount: amount || payment[0].amount,
        reason: reason,
        refund_id: refundResult.id,
        status: refundResult.status,
        create_at: sqlDateTimeFormat(new Date())
      });

      // Update payment status
      this.sdk.setTable("payment");
      await this.sdk.update(
        {
          refunded: 1,
          refund_amount: amount || payment[0].amount,
          update_at: sqlDateTimeFormat(new Date())
        },
        paymentId
      );

      // Update invoice status
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: payment[0].invoice_id });

      if (invoice.length > 0) {
        const totalPaid = await this.getTotalPaidForInvoice(payment[0].invoice_id);
        const newStatus = totalPaid <= 0 ? "refunded" : totalPaid < invoice[0].total ? "partially_paid" : "paid";

        await this.sdk.update(
          {
            payment_amount: totalPaid,
            status: newStatus,
            update_at: sqlDateTimeFormat(new Date())
          },
          payment[0].invoice_id
        );
      }

      return {
        error: false,
        refund_id: refundId,
        status: refundResult.status
      };
    } catch (error) {
      console.error("Error refunding payment:", error);
      throw new Error(error.message || "Failed to refund payment");
    }
  }

  async createCoupon(req) {
    try {
      this.sdk = this.getSDK(req);

      // Verify user is admin
      if (req.role !== "admin") {
        throw new Error("Only administrators can create coupons");
      }

      const {
        name,
        code,
        discountType, // 'percentage' or 'fixed_amount'
        discountAmount, // percentage (1-100) or fixed amount in dollars
        durationInMonths, // how many months the coupon is valid for recurring payments
        maxRedemptions, // maximum number of times this coupon can be redeemed
        expiresAt, // when the coupon expires (timestamp)
        applyToSubscriptions, // boolean - can be applied to subscriptions
        applyToInvoices, // boolean - can be applied to invoices
        minimumAmount, // minimum amount required to use the coupon (in dollars)
        restrictedToPlans, // array of plan IDs this coupon can be used with
        restrictedToUsers // array of user IDs this coupon can be used with
      } = req.body;

      // Validate required fields
      if (!name || !code || !discountType || !discountAmount) {
        throw new Error("Name, code, discount type, and discount amount are required");
      }

      // Validate discount type
      if (discountType !== "percentage" && discountType !== "fixed_amount") {
        throw new Error("Discount type must be either 'percentage' or 'fixed_amount'");
      }

      // Validate discount amount
      if (discountType === "percentage" && (discountAmount < 1 || discountAmount > 100)) {
        throw new Error("Percentage discount must be between 1 and 100");
      }

      if (discountType === "fixed_amount" && discountAmount <= 0) {
        throw new Error("Fixed amount discount must be greater than 0");
      }

      // check if coupon name already exists
      this.sdk.setTable("coupon");
      const existingCoupon = await this.sdk.get({ code: code.toUpperCase() });
      if (existingCoupon.length > 0) {
        throw new Error("Coupon code already exists");
      }

      // Create coupon in Stripe
      let stripeParams = {
        name: name,
        id: code.toUpperCase(), // Stripe uses the code as the coupon ID
        duration: durationInMonths ? "repeating" : "once",
        metadata: {
          created_by: req.user_id,
          apply_to_subscriptions: applyToSubscriptions ? "true" : "false",
          apply_to_invoices: applyToInvoices ? "true" : "false"
        }
      };

      // Set discount type and amount
      if (discountType === "percentage") {
        stripeParams.percent_off = discountAmount;
      } else if (discountType === "fixed_amount") {
        stripeParams.amount_off = Math.round(discountAmount * 100); // Convert to cents
        stripeParams.currency = "usd";
      }

      // Set duration in months if provided
      if (durationInMonths) {
        stripeParams.duration_in_months = durationInMonths;
      }

      // Set max redemptions if provided
      if (maxRedemptions) {
        stripeParams.max_redemptions = maxRedemptions;
      }

      // Set expiration if provided
      if (expiresAt) {
        stripeParams.redeem_by = Math.floor(new Date(expiresAt).getTime() / 1000);
      }

      // Set minimum amount if provided
      if (minimumAmount) {
        stripeParams.minimum_amount = Math.round(minimumAmount * 100); // Convert to cents
        stripeParams.minimum_amount_currency = "usd";
      }

      // Create the coupon in Stripe
      const stripeCoupon = await this.stripeService.createStripeCoupon(stripeParams);

      // Save coupon to database
      this.sdk.setTable("coupon");
      const couponId = await this.sdk.insert({
        name: name,
        code: code.toUpperCase(),
        discount_type: discountType,
        discount_amount: discountAmount,
        duration_in_months: durationInMonths || null,
        max_redemptions: maxRedemptions || null,
        expires_at: expiresAt ? sqlDateTimeFormat(new Date(expiresAt)) : null,
        apply_to_subscriptions: applyToSubscriptions ? 1 : 0,
        apply_to_invoices: applyToInvoices ? 1 : 0,
        minimum_amount: minimumAmount || null,
        stripe_coupon_id: stripeCoupon.id,
        created_by: req.user_id,
        create_at: sqlDateTimeFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Save plan restrictions if provided
      if (restrictedToPlans && restrictedToPlans.length > 0) {
        this.sdk.setTable("coupon_plan");
        for (const planId of restrictedToPlans) {
          await this.sdk.insert({
            coupon_id: couponId,
            plan_id: planId,
            create_at: sqlDateTimeFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });
        }
      }

      // Save user restrictions if provided
      if (restrictedToUsers && restrictedToUsers.length > 0) {
        this.sdk.setTable("coupon_user");
        for (const userId of restrictedToUsers) {
          await this.sdk.insert({
            coupon_id: couponId,
            user_id: userId,
            create_at: sqlDateTimeFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });
        }
      }

      return {
        error: false,
        coupon: {
          id: couponId,
          stripe_coupon_id: stripeCoupon.id,
          name: name,
          code: code.toUpperCase(),
          discount_type: discountType,
          discount_amount: discountAmount
        }
      };
    } catch (error) {
      console.error("Error creating coupon:", error);
      throw new Error(error.message || "Failed to create coupon");
    }
  }

  async getCoupons(req) {
    try {
      this.sdk = this.getSDK(req);

      // Verify user is admin
      if (req.role !== "admin") {
        throw new Error("Only administrators can view all coupons");
      }

      const { page = 1, limit = 10, active = true } = req.query;
      const offset = (page - 1) * limit;

      // Build query
      let query = `
        SELECT c.*, u.first_name as created_by_first_name, u.last_name as created_by_last_name
        FROM equalityrecord_coupon c
        LEFT JOIN equalityrecord_user u ON c.created_by = u.id
      `;

      // Filter by active status if requested
      if (active === "true" || active === true) {
        const now = sqlDateTimeFormat(new Date());
        query += ` WHERE (c.expires_at IS NULL OR c.expires_at > '${now}')`;
      }

      // Add sorting and pagination
      query += ` ORDER BY c.create_at DESC LIMIT ${limit} OFFSET ${offset}`;

      // Execute query
      const coupons = await this.sdk.rawQuery(query);

      // Get total count
      const countQuery = query
        .split("ORDER BY")[0]
        .replace("c.*, u.first_name as created_by_first_name, u.last_name as created_by_last_name", "COUNT(*) as total");
      const countResult = await this.sdk.rawQuery(countQuery);

      // Get plan restrictions for each coupon
      for (const coupon of coupons) {
        this.sdk.setTable("coupon_plan");
        const planRestrictions = await this.sdk.get({ coupon_id: coupon.id });

        if (planRestrictions.length > 0) {
          const planIds = planRestrictions.map((pr) => pr.plan_id);
          this.sdk.setTable("stripe_price");
          const plans = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_stripe_price WHERE id IN (${planIds.join(",")})`);
          coupon.restricted_plans = plans;
        } else {
          coupon.restricted_plans = [];
        }

        // Get user restrictions
        this.sdk.setTable("coupon_user");
        const userRestrictions = await this.sdk.get({ coupon_id: coupon.id });

        if (userRestrictions.length > 0) {
          const userIds = userRestrictions.map((ur) => ur.user_id);
          this.sdk.setTable("user");
          const users = await this.sdk.rawQuery(`
            SELECT id, first_name, last_name, email 
            FROM equalityrecord_user 
            WHERE id IN (${userIds.join(",")})
          `);
          coupon.restricted_users = users;
        } else {
          coupon.restricted_users = [];
        }
      }

      return {
        error: false,
        coupons,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(countResult[0].total / limit)
      };
    } catch (error) {
      console.error("Error getting coupons:", error);
      throw new Error(error.message || "Failed to get coupons");
    }
  }

  async getCouponByCode(req) {
    try {
      this.sdk = this.getSDK(req);
      const { code } = req.params;

      if (!code) {
        throw new Error("Coupon code is required");
      }

      // Get coupon from database
      this.sdk.setTable("coupon");
      const coupon = await this.sdk.get({ code: code.toUpperCase() });

      if (!coupon.length) {
        throw new Error("Coupon not found");
      }

      // Check if coupon is expired
      if (coupon[0].expires_at && new Date(coupon[0].expires_at) < new Date()) {
        return {
          error: false,
          valid: false,
          message: "Coupon has expired",
          coupon: coupon[0]
        };
      }

      // Check if coupon has reached max redemptions
      if (coupon[0].max_redemptions) {
        // Get redemption count from Stripe
        const stripeCoupon = await this.stripeService.retrieveStripeCoupon({
          couponId: coupon[0].stripe_coupon_id
        });

        if (stripeCoupon.times_redeemed >= coupon[0].max_redemptions) {
          return {
            error: false,
            valid: false,
            message: "Coupon has reached maximum redemptions",
            coupon: coupon[0]
          };
        }
      }

      // Check if coupon is restricted to specific plans
      this.sdk.setTable("coupon_plan");
      const planRestrictions = await this.sdk.get({ coupon_id: coupon[0].id });

      if (planRestrictions.length > 0) {
        const planIds = planRestrictions.map((pr) => pr.plan_id);
        this.sdk.setTable("stripe_price");
        const plans = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_stripe_price WHERE id IN (${planIds.join(",")})`);
        coupon[0].restricted_plans = plans;
      } else {
        coupon[0].restricted_plans = [];
      }

      // Check if coupon is restricted to specific users
      this.sdk.setTable("coupon_user");
      const userRestrictions = await this.sdk.get({ coupon_id: coupon[0].id });

      if (userRestrictions.length > 0) {
        const userIds = userRestrictions.map((ur) => ur.user_id);

        // If user is logged in, check if they can use this coupon
        if (req.user_id && !userIds.includes(parseInt(req.user_id))) {
          return {
            error: false,
            valid: false,
            message: "Coupon is not available for your account",
            coupon: coupon[0]
          };
        }

        coupon[0].restricted_to_users = true;
      } else {
        coupon[0].restricted_to_users = false;
      }

      return {
        error: false,
        valid: true,
        coupon: coupon[0]
      };
    } catch (error) {
      console.error("Error getting coupon:", error);
      throw new Error(error.message || "Failed to get coupon");
    }
  }

  async deleteCoupon(req) {
    try {
      this.sdk = this.getSDK(req);

      // Verify user is admin
      if (req.role !== "admin") {
        throw new Error("Only administrators can delete coupons");
      }

      const { couponId } = req.params;

      // Get coupon from database
      this.sdk.setTable("coupon");
      const coupon = await this.sdk.get({ id: couponId });

      if (!coupon.length) {
        throw new Error("Coupon not found");
      }

      // Delete coupon from Stripe
      await this.stripeService.deleteStripeCoupon({
        couponId: coupon[0].stripe_coupon_id
      });

      // Delete plan restrictions
      this.sdk.setTable("coupon_plan");
      await this.sdk.delete({ coupon_id: couponId });

      // Delete user restrictions
      this.sdk.setTable("coupon_user");
      await this.sdk.delete({ coupon_id: couponId });

      // Delete coupon from database
      this.sdk.setTable("coupon");
      await this.sdk.delete({ id: couponId });

      return {
        error: false,
        message: "Coupon deleted successfully"
      };
    } catch (error) {
      console.error("Error deleting coupon:", error);
      throw new Error(error.message || "Failed to delete coupon");
    }
  }

  async getPublicInvoice(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceId, token } = req.params;

      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceId, access_token: token });

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Get invoice details
      //
      const query = `
        SELECT i.*, c.program, c.name as client_name, c.email as client_email, u.first_name as created_by_first_name, u.last_name as created_by_last_name, u.phone as created_by_phone, p.company_logo as created_by_company_logo, p.company_name as created_by_company_name
        FROM equalityrecord_invoice i
        JOIN equalityrecord_client c ON i.client_id = c.id
        JOIN equalityrecord_user u ON i.user_id = u.id
        JOIN equalityrecord_profile p ON i.user_id = p.user_id
        WHERE i.id = ${invoiceId}
      `;

      const invoiceDetails = await this.sdk.rawQuery(query);

      // Get invoice items
      this.sdk.setTable("invoice_item");
      const items = await this.sdk.get({ invoice_id: invoiceId });

      // Get payments
      this.sdk.setTable("payment");
      const payments = await this.sdk.get({ invoice_id: invoiceId });

      // Get company information from user profile
      this.sdk.setTable("profile");
      const profile = await this.sdk.get({ user_id: invoiceDetails[0].user_id });

      this.sdk.setTable("user");
      const user = await this.sdk.get({ id: invoiceDetails[0].user_id });

      const companyInfo = profile.length
        ? {
            company_name: profile[0].company_name,
            company_logo: profile[0].company_logo,
            office_email: profile[0].office_email,
            edit_policy_link: profile[0].edit_policy_link,
            company_address: user[0].company_address,
            phone: user[0].phone
          }
        : null;

      this.sdk.setTable("mix_season");
      const mixSeason = await this.sdk.get({ id: invoiceDetails[0].mix_season_id });

      return {
        error: false,
        invoice: invoiceDetails[0],
        items,
        payments,
        company_info: companyInfo,
        mix_season: mixSeason[0]
      };
    } catch (error) {
      console.error("Error getting public invoice:", error);
      throw new Error(error.message || "Failed to get invoice");
    }
  }

  async updatePublicInvoice(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceId, token } = req.params;
      const updateData = req.body;

      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceId, access_token: token });

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Validate update data  initials, signature,feedback notes
      const allowedFields = ["initials", "signature", "feedback_notes", "attachment_url"];
      const filteredUpdateData = {};

      Object.keys(updateData).forEach((key) => {
        if (allowedFields.includes(key)) {
          filteredUpdateData[key] = updateData[key];
        }
      });

      if (Object.keys(filteredUpdateData).length === 0) {
        throw new Error("No valid fields to update");
      }

      // Add updated_at timestamp
      filteredUpdateData.update_at = sqlDateTimeFormat(new Date());

      // Update the invoice
      await this.sdk.update(filteredUpdateData, invoiceId);

      // Get the updated invoice
      const updatedInvoice = await this.sdk.get({ id: invoiceId });

      return {
        error: false,
        message: "Invoice updated successfully",
        invoice: updatedInvoice[0]
      };
    } catch (error) {
      console.error("Error updating public invoice:", error);
      throw new Error(error.message || "Failed to update invoice");
    }
  }

  async processPublicPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceId, token } = req.params;
      const {
        paymentMethodId,
        amount,
        paymentType, // 'deposit', 'full', or 'custom'
        clientName,
        clientEmail
      } = req.body;

      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceId, access_token: token });

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Get client details
      this.sdk.setTable("client");
      const client = await this.sdk.get({ id: invoice[0].client_id });

      if (!client.length) {
        throw new Error("Client not found");
      }

      // Determine payment amount
      let paymentAmount = 0;
      if (paymentType === "deposit") {
        paymentAmount = invoice[0].deposit_amount;
      } else if (paymentType === "full") {
        paymentAmount = invoice[0].total;
      } else if (paymentType === "custom") {
        paymentAmount = amount;
      } else {
        throw new Error("Invalid payment type");
      }

      // Calculate Stripe fee
      const stripeFee = Math.round(paymentAmount * 0.029 + 30); // 2.9% + 30 cents

      // Process payment with Stripe
      const stripeService = new (require("../../../services/StripeService"))();

      // Create a payment intent
      const paymentIntent = await stripeService.createPaymentIntentManual({
        amount: paymentAmount,
        currency: "usd",
        payment_method_types: ["card"],
        metadata: {
          invoice_id: invoiceId,
          client_id: client[0].id,
          client_name: clientName || client[0].name,
          client_email: clientEmail || client[0].email,
          payment_type: paymentType
        }
      });

      // If paymentMethodId is provided, confirm the payment immediately
      if (paymentMethodId) {
        await stripeService.stripe.paymentIntents.confirm(paymentIntent.id, {
          payment_method: paymentMethodId
        });

        // Record the payment
        this.sdk.setTable("payment");
        await this.sdk.insert({
          invoice_id: invoiceId,
          amount: paymentAmount,
          fee: stripeFee,
          net_amount: paymentAmount - stripeFee,
          payment_method: "credit_card",
          payment_intent_id: paymentIntent.id,
          status: "succeeded",
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        // Update invoice status if needed
        const totalPaid = await this.getTotalPaidForInvoice(invoiceId);

        if (totalPaid >= invoice[0].total) {
          this.sdk.setTable("invoice");
          await this.sdk.update(
            {
              status: "paid",
              update_at: sqlDateTimeFormat(new Date())
            },
            invoiceId
          );
        } else if (totalPaid > 0) {
          this.sdk.setTable("invoice");
          await this.sdk.update(
            {
              status: "partially_paid",
              update_at: sqlDateTimeFormat(new Date())
            },
            invoiceId
          );
        }

        return {
          error: false,
          message: "Payment processed successfully",
          payment_intent: paymentIntent
        };
      }

      // If no paymentMethodId, return the payment intent for client-side confirmation
      return {
        error: false,
        message: "Payment intent created. Please confirm payment.",
        payment_intent: paymentIntent,
        client_secret: paymentIntent.client_secret,
        stripe_checkout_url: null //`https://checkout.stripe.com/pay/${paymentIntent.client_secret}`
      };
    } catch (error) {
      console.error("Error processing public payment:", error);
      throw new Error(error.message || "Failed to process payment");
    }
  }

  async confirmPublicPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceId, token } = req.params;
      const { paymentIntentId } = req.body;

      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceId, access_token: token });

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // Retrieve the payment intent
      const stripeService = new (require("../../../services/StripeService"))();
      const paymentIntent = await stripeService.retrievePaymentIntent(paymentIntentId);

      if (!paymentIntent || paymentIntent.status !== "succeeded") {
        throw new Error("Payment has not been completed successfully");
      }

      // Verify the payment intent is for this invoice
      if (paymentIntent.metadata.invoice_id !== invoiceId) {
        throw new Error("Payment intent does not match this invoice");
      }

      const paymentAmount = paymentIntent.amount;
      const stripeFee = Math.round(paymentAmount * 0.029 + 30); // 2.9% + 30 cents

      // Record the payment
      this.sdk.setTable("payment");
      await this.sdk.insert({
        invoice_id: invoiceId,
        amount: paymentAmount,
        fee: stripeFee,
        net_amount: paymentAmount - stripeFee,
        payment_method: "credit_card",
        payment_intent_id: paymentIntentId,
        status: "succeeded",
        create_at: sqlDateTimeFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Update invoice status if needed
      const totalPaid = await this.getTotalPaidForInvoice(invoiceId);

      if (totalPaid >= invoice[0].total) {
        this.sdk.setTable("invoice");
        await this.sdk.update(
          {
            status: "paid",
            update_at: sqlDateTimeFormat(new Date())
          },
          invoiceId
        );
      } else if (totalPaid > 0) {
        this.sdk.setTable("invoice");
        await this.sdk.update(
          {
            status: "partially_paid",
            update_at: sqlDateTimeFormat(new Date())
          },
          invoiceId
        );
      }

      return {
        error: false,
        message: "Payment confirmed successfully"
      };
    } catch (error) {
      console.error("Error confirming public payment:", error);
      throw new Error(error.message || "Failed to confirm payment");
    }
  }

  async createPaymentIntentForInvoiceItem(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceItemId } = req.body;

      // Get invoice item details
      this.sdk.setTable("invoice_item");
      const invoiceItem = await this.sdk.get({ id: invoiceItemId });

      if (!invoiceItem.length) {
        throw new Error("Invoice item not found");
      }

      // Check if there's any amount left to pay
      if (!invoiceItem[0].amount_left || parseFloat(invoiceItem[0].amount_left) <= 0) {
        throw new Error("No remaining balance to pay for this invoice item");
      }

      // Get invoice details for metadata
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceItem[0].invoice_id });

      if (!invoice.length) {
        throw new Error("Parent invoice not found");
      }

      // Get user/client details
      this.sdk.setTable("user");
      const user = await this.sdk.get({ id: invoice[0].user_id });

      if (!user.length) {
        throw new Error("User not found");
      }

      const amount = parseFloat(invoiceItem[0].amount_left);

      // Calculate Stripe fee
      const stripeFee = Math.round(amount * 0.029 + 0.3); // 2.9% + $0.30
      const totalWithFee = amount + stripeFee;

      // Create checkout session through Stripe
      const stripeService = new (require("../../../services/StripeService"))();

      // Create a checkout session
      const checkoutSession = await stripeService.createCheckoutSession({
        payment_method_types: ["card"],
        mode: "payment",
        line_items: [
          {
            price_data: {
              currency: "usd",
              product_data: {
                name: "Invoice Item Payment"
              },
              unit_amount: Math.round(totalWithFee * 100)
            },
            quantity: 1
          }
        ],
        success_url: `https://equalitydev.manaknightdigital.com/invoice/client-success?invoice_id=${invoice[0].id}`,
        cancel_url: `https://equalitydev.manaknightdigital.com/invoice/client-cancel?invoice_id=${invoice[0].id}`,
        metadata: {
          invoice_id: invoice[0].id,
          invoice_item_id: invoiceItemId,
          user_id: user[0].id,
          payment_type: "balance",
          amount_left: amount,
          handle_webhook_with: "confirmInvoiceItemPayment"
        }
      });

      // Record the checkout session in the database
      this.sdk.setTable("payment");
      const paymentId = await this.sdk.insert({
        invoice_id: invoice[0].id,
        invoice_item_id: invoiceItemId,
        amount: amount,
        stripe_fee: stripeFee,
        total_charged: totalWithFee,
        payment_method: "credit_card",
        payment_id: checkoutSession.id,
        status: checkoutSession.status,
        create_at: sqlDateTimeFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return {
        error: false,
        payment_id: paymentId,
        checkout_session: checkoutSession,
        stripe_checkout_url: checkoutSession.url,
        amount: amount
      };
    } catch (error) {
      console.error("Error creating checkout session for invoice item:", error);
      throw new Error(error.message || "Failed to create checkout session");
    }
  }

  async confirmInvoiceItemPayment(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceItemId, checkoutSessionId } = req.body;

      // Validate input
      if (!invoiceItemId || !checkoutSessionId) {
        throw new Error("Invoice item ID and checkout session ID are required");
      }

      // Get invoice item
      this.sdk.setTable("invoice_item");
      const invoiceItem = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_invoice_item WHERE id = ${invoiceItemId}`);

      if (!invoiceItem.length) {
        throw new Error("Invoice item not found");
      }

      // Get payment record
      this.sdk.setTable("payment");
      const payment = await this.sdk.rawQuery(
        `SELECT * FROM equalityrecord_payment WHERE payment_id = "${checkoutSessionId}" AND invoice_item_id = ${invoiceItemId}`
      );

      if (!payment.length) {
        throw new Error("Payment record not found");
      }

      // Verify payment status with Stripe
      const stripeService = new (require("../../../services/StripeService"))();
      const checkoutSession = await stripeService.retrieveCheckoutSession(checkoutSessionId);

      if (checkoutSession.payment_status !== "paid") {
        throw new Error("Payment has not been completed successfully");
      }

      // Update payment status
      this.sdk.setTable("payment");
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_payment SET status = "succeeded", update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${payment[0].id}`
      );

      // Update invoice item - clear remaining balance
      this.sdk.setTable("invoice_item");
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_invoice_item SET amount_left = 0, status = 3, update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${invoiceItemId}`
      );

      // update project payment status
      this.sdk.setTable("project");
      const project = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_project WHERE id = ${invoiceItem[0].project_id}`);
      if (project.length > 0) {
        this.sdk.setTable("project");
        await this.sdk.rawQuery(
          `UPDATE equalityrecord_project SET payment_status = 3, update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${project[0].id}`
        );
      }

      // update amount_paid
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_invoice WHERE id = ${invoiceItem[0].invoice_id}`);
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_invoice SET payment_amount = ${parseFloat(invoice[0].payment_amount) + parseFloat(invoiceItem[0].amount_left)} WHERE id = ${
          invoiceItem[0].invoice_id
        }`
      );
      // Check if all invoice items are paid
      const invoiceId = invoiceItem[0].invoice_id;

      const unpaidItemsQuery = `
        SELECT COUNT(*) as count 
        FROM equalityrecord_invoice_item 
        WHERE invoice_id = ${invoiceId} AND (amount_left > 0 OR status != 'paid')
      `;

      const unpaidItems = await this.sdk.rawQuery(unpaidItemsQuery);

      // Update invoice status if all items are paid
      if (unpaidItems[0].count === 0) {
        this.sdk.setTable("invoice");
        await this.sdk.rawQuery(`UPDATE equalityrecord_invoice SET status = "paid", update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${invoiceId}`);
      }

      return {
        error: false,
        message: "Payment confirmed successfully",
        status: "succeeded",
        invoice_item_id: invoiceItemId
      };
    } catch (error) {
      console.error("Error confirming invoice item payment:", error);
      throw new Error(error.message || "Failed to confirm payment");
    }
  }

  async updateInvoice(req) {
    try {
      this.sdk = this.getSDK(req);
      const { invoiceId } = req.params;
      const { clientId, clientName, clientEmail, programName, invoiceDate, dueDate, items, depositPercentage, notes, termsAndConditions, checks } = req.body;

      // Validate invoice exists
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.get({ id: invoiceId });

      if (!invoice.length) {
        throw new Error("Invoice not found");
      }

      // If client is changing, validate client exists
      let actualClientId = invoice[0].client_id;
      if (clientId && clientId !== invoice[0].client_id) {
        this.sdk.setTable("client");
        const client = await this.sdk.get({ id: clientId });

        if (!client.length) {
          throw new Error("Client not found");
        }
        actualClientId = clientId;
      }

      // Recalculate subtotal based on items if provided
      let subtotal = invoice[0].subtotal;
      if (items && items.length > 0) {
        subtotal = items.reduce((sum, item) => {
          const itemPrice = item.price * item.quantity;
          const discountAmount = item.discount || 0;
          return sum + (itemPrice - discountAmount);
        }, 0);
      }

      // Prepare invoice update data
      const invoiceUpdateData = {
        client_id: actualClientId,
        checks: checks,
        subtotal: subtotal,
        total: subtotal,
        deposit_amount: (subtotal * (depositPercentage || invoice[0].deposit_percentage)) / 100,
        deposit_percentage: depositPercentage || invoice[0].deposit_percentage,
        update_at: sqlDateTimeFormat(new Date())
      };

      // Add optional fields if provided
      if (notes !== undefined) invoiceUpdateData.notes = notes;
      if (termsAndConditions !== undefined) invoiceUpdateData.terms_and_conditions = termsAndConditions;
      if (invoiceDate) invoiceUpdateData.invoice_date = invoiceDate;
      if (dueDate) invoiceUpdateData.due_date = dueDate;

      // Update invoice
      await this.sdk.update(invoiceUpdateData, invoiceId);

      // If items are provided, update them
      if (items && items.length > 0) {
        // Update each item
        for (const item of items) {
          // Ensure item has an ID
          if (!item.id) {
            continue; // Skip items without ID - we're only updating
          }

          // Calculate item total with discounts
          const itemPrice = item.price * item.quantity;
          const discountAmount = item.discount || 0;
          const itemTotal = itemPrice - discountAmount;

          // Prepare item update data
          const itemData = {
            description: item.description || item.name || "",
            quantity: item.quantity,
            price: item.price,
            total: itemTotal,
            discount: discountAmount,
            update_at: sqlDateTimeFormat(new Date())
          };

          // Add optional fields if provided
          if (item.producer) itemData.producer = item.producer;
          if (item.producers) {
            itemData.producers = item.producers.length ? JSON.stringify(item.producers) : JSON.stringify([item.producer || req.user_id]);
          }
          if (item.isSpecial !== undefined) itemData.is_special = item.isSpecial ? 1 : 0;
          if (item.specialType !== undefined) itemData.special_type = item.specialType || null;
          if (item.mixDate !== undefined) itemData.mix_date = item.mixDate || null;
          if (item.teamName !== undefined) itemData.team_name = item.teamName || null;
          if (item.division !== undefined) itemData.division = item.division || null;
          if (item.musicSurveyDue !== undefined) itemData.music_survey_due = item.musicSurveyDue || null;
          if (item.routineSubmissionDue !== undefined) itemData.routine_submission_due = item.routineSubmissionDue || null;
          if (item.estimatedCompletion !== undefined) itemData.estimated_completion = item.estimatedCompletion || null;
          if (item.status !== undefined) itemData.status = item.status || null;

          // Update item in database
          this.sdk.setTable("invoice_item");
          await this.sdk.update(itemData, item.id);
        }
      }

      // Get updated invoice data to return
      this.sdk.setTable("invoice");
      const updatedInvoice = await this.sdk.get({ id: invoiceId });

      // Get updated invoice items
      this.sdk.setTable("invoice_item");
      const updatedItems = await this.sdk.get({ invoice_id: invoiceId });

      return {
        error: false,
        message: "Invoice updated successfully",
        invoice: updatedInvoice[0],
        items: updatedItems
      };
    } catch (error) {
      console.error("Error updating invoice:", error);
      throw new Error(error.message || "Failed to update invoice");
    }
  }

  async getCompanyInvoicePaymentReport(req) {
    try {
      this.sdk = this.getSDK(req);
      const { startDate, endDate } = req.body; // Remove companyId from destructuring

      // Determine company information based on user role and relationships
      let companyMembers = [];
      let managerId = null;
      let companyId = null;

      if (req.role === "manager") {
        // For managers, use their own ID as the company ID and get their managed members
        const managerPermissions = await this.sdk.rawQuery(`
          SELECT mp.manager_id, mp.member_ids
          FROM equalityrecord_manager_permission mp
          WHERE mp.manager_id = ${req.user_id}
        `);

        if (!managerPermissions.length) {
          throw new Error("No managed members found");
        }

        managerId = req.user_id;
        companyId = req.user_id; // Manager's ID serves as company ID
        companyMembers = JSON.parse(managerPermissions[0].member_ids || "[]");
        companyMembers.push(managerId); // Include manager in the list
      } else if (req.role === "member") {
        // For members, find which company they belong to
        const memberCompanyInfo = await this.sdk.rawQuery(`
          SELECT mp.manager_id, mp.member_ids
          FROM equalityrecord_manager_permission mp
          WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
        `);

        if (!memberCompanyInfo.length) {
          // Member is not part of any company, treat them as individual
          companyMembers = [req.user_id];
          companyId = req.user_id;
          managerId = null;
        } else {
          // Member belongs to a company
          managerId = memberCompanyInfo[0].manager_id;
          companyId = managerId; // Use manager's ID as company ID
          companyMembers = JSON.parse(memberCompanyInfo[0].member_ids || "[]");
          companyMembers.push(managerId); // Include manager in the list
        }
      } else if (req.role === "admin") {
        // For admin, we need to specify which company to report on
        // Since we removed companyId parameter, admin will see all companies
        // or we can default to their own data if they have any
        const adminCompanyInfo = await this.sdk.rawQuery(`
          SELECT mp.manager_id, mp.member_ids
          FROM equalityrecord_manager_permission mp
          WHERE mp.manager_id = ${req.user_id}
        `);

        if (adminCompanyInfo.length) {
          // Admin also has a company
          managerId = req.user_id;
          companyId = req.user_id;
          companyMembers = JSON.parse(adminCompanyInfo[0].member_ids || "[]");
          companyMembers.push(managerId);
        } else {
          // Admin doesn't have a company, return error or empty result
          throw new Error("Admin must specify company data or doesn't have company to report on");
        }
      }

      // else {
      //   // Other roles don't have access
      //   throw new Error("Access denied. Only managers, members, and admins can view company reports");
      // }

      if (companyMembers.length === 0) {
        throw new Error("No company members found");
      }

      // Get member names for all company members first
      const memberNamesQuery = `
        SELECT id, first_name, last_name
        FROM equalityrecord_user
        WHERE id IN (${companyMembers.join(",")})
      `;
      const memberNames = await this.sdk.rawQuery(memberNamesQuery);

      // Initialize memberTotals with all company members set to 0
      const memberTotals = {};
      memberNames.forEach((member) => {
        memberTotals[member.id.toString()] = {
          name: `${member.first_name} ${member.last_name}`.trim(),
          total: 0
        };
      });

      // Build date filter
      let dateFilter = "";
      if (startDate && endDate) {
        dateFilter = `AND p.create_at >= '${startDate}' AND p.create_at <= '${endDate}'`;
      } else if (startDate) {
        dateFilter = `AND p.create_at >= '${startDate}'`;
      } else if (endDate) {
        dateFilter = `AND p.create_at <= '${endDate}'`;
      }

      // Get all payments for invoices created by company members
      const paymentQuery = `
        SELECT 
          p.amount,
          p.invoice_id,
          p.status,
          p.create_at as payment_date,
          i.user_id as invoice_creator,
          ii.producer,
          ii.is_special,
          ii.total as item_total,
          u.first_name,
          u.last_name
        FROM equalityrecord_payment p
        JOIN equalityrecord_invoice i ON i.id = p.invoice_id
        JOIN equalityrecord_invoice_item ii ON ii.invoice_id = i.id
        JOIN equalityrecord_user u ON u.id = ii.producer
        WHERE p.status = 'succeeded' 
        AND ii.producer IN (${companyMembers.join(",")})
        ${dateFilter}
        ORDER BY p.create_at DESC
      `;

      const payments = await this.sdk.rawQuery(paymentQuery);

      // Initialize the response structure
      const response = {
        companyId: companyId.toString(),
        dateRange: {
          startDate: startDate || null,
          endDate: endDate || null
        },
        totalAmount: 0.0,
        memberBreakdown: {},
        specialItems: {
          totalAmount: 0.0,
          itemCount: 0
        }
      };

      // Process payments and build breakdown
      const processedInvoices = new Set(); // To avoid double counting invoice payments
      let specialItemsTotal = 0;
      let specialItemsCount = 0;

      for (const payment of payments) {
        const memberId = payment.producer.toString();
        const memberName = `${payment.first_name} ${payment.last_name}`.trim();
        const isSpecial = payment.is_special === 1;

        // Calculate the proportion of this payment that applies to this specific item
        // Get total invoice amount to calculate proportion
        const invoiceTotal = await this.sdk.rawQuery(`
          SELECT SUM(ii.total) as total
          FROM equalityrecord_invoice_item ii
          WHERE ii.invoice_id = ${payment.invoice_id}
        `);

        const totalInvoiceAmount = parseFloat(invoiceTotal[0].total || 0);
        const itemAmount = parseFloat(payment.item_total || 0);
        const paymentAmount = parseFloat(payment.amount || 0);

        // Calculate proportional payment for this item
        const proportionalPayment = totalInvoiceAmount > 0 ? (itemAmount / totalInvoiceAmount) * paymentAmount : 0;

        // Add to member total (member should already exist from initialization)
        if (memberTotals[memberId]) {
          memberTotals[memberId].total += proportionalPayment;
        }

        // Add to special items if applicable
        if (isSpecial) {
          specialItemsTotal += proportionalPayment;
          specialItemsCount += 1;
        }

        // Add to overall total
        response.totalAmount += proportionalPayment;
      }

      // Build member breakdown in the required format - include ALL members
      const memberKeys = Object.keys(memberTotals);
      memberKeys.forEach((memberId, index) => {
        const memberKey = index === 0 ? "memberA" : index === 1 ? "memberB" : `member${String.fromCharCode(67 + index - 2)}`;
        response.memberBreakdown[memberKey] = {
          memberId: memberId,
          memberName: memberTotals[memberId].name,
          totalAmount: parseFloat(memberTotals[memberId].total.toFixed(2))
        };
      });

      // Set special items totals
      response.specialItems.totalAmount = parseFloat(specialItemsTotal.toFixed(2));
      response.specialItems.itemCount = specialItemsCount;

      // Round total amount
      response.totalAmount = parseFloat(response.totalAmount.toFixed(2));

      return {
        error: false,
        report: response
      };
    } catch (error) {
      console.error("Error generating company invoice payment report:", error);
      throw new Error(error.message || "Failed to generate company invoice payment report");
    }
  }
};
