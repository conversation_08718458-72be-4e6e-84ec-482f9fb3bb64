const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class MixTypeService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);

      let result;
      if (req.role !== "admin") {
        result = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_mix_type
        WHERE user_id = ${req.user_id}
        ORDER BY name ASC;`);
      } else {
        result = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_mix_type
        ORDER BY name ASC;`);
      }

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllMixTypesForClient(req) {
    try {
      this.sdk = this.getSDK(req);

      this.sdk.setTable("user");
      let userExists = await this.sdk.get({ id: req.user_id });

      if (userExists.length === 0) {
        return {
          error: true,
          message: "Client not found",
          list: []
        };
      }

      let mixTypeIds = [];
      this.sdk.setTable("project");
      let clientProjects = await this.sdk.get({
        client_id: userExists[0].client_id
      });

      if (clientProjects.length === 0) {
        return {
          error: true,
          list: []
        };
      }

      for (let i = 0; i < clientProjects.length; i++) {
        mixTypeIds.push(clientProjects[i].mix_type_id);
      }

      if (mixTypeIds.length === 0) {
        return {
          error: true,
          list: []
        };
      }

      let mixTypes = [];
      if (mixTypeIds.length === 1) {
        mixTypes = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_mix_type
          WHERE id = ${mixTypeIds[0]}
          ORDER BY name ASC;
        `);
      } else {
        mixTypes = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_mix_type
          WHERE id IN (${mixTypeIds.join(",")})
          ORDER BY name ASC;
        `);
      }

      return {
        error: false,
        list: mixTypes.length > 0 ? mixTypes : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE mt.user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_mix_type mt
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      // const total = countResult[0].total;
      // const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` user_id = ${filter.user_id} `);
        }
        if (filter.name) {
          filterArr.push(` LOWER(mt.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.is_voiceover) {
          filterArr.push(` mt.is_voiceover = ${filter.is_voiceover} `);
        }
        if (filter.voiceover) {
          filterArr.push(` mt.voiceover = ${filter.voiceover} `);
        }
        if (filter.is_song) {
          filterArr.push(` mt.is_song = ${filter.is_song} `);
        }
        if (filter.song) {
          filterArr.push(` mt.song = ${filter.song} `);
        }
        if (filter.is_tracking) {
          filterArr.push(` mt.is_tracking = ${filter.is_tracking} `);
        }
        if (filter.tracking) {
          filterArr.push(` mt.tracking = ${filter.tracking} `);
        }
        if (filter.color) {
          filterArr.push(` mt.color LIKE '%${filter.color}%' `);
        }
        if (filter.price) {
          filterArr.push(` mt.price = ${filter.price} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let rawSql = `SELECT
                      mt.*, CONCAT(u.first_name, ' ', u.last_name) AS member_name
                    FROM
                      equalityrecord_mix_type mt
                      LEFT JOIN equalityrecord_user u ON u.id = mt.user_id
                    ${filterQuery}
                    ORDER BY mt.id DESC
                    LIMIT ${limit} OFFSET ${offset};
      `;

      let rawSql2 = `SELECT
                      mt.*, CONCAT(u.first_name, ' ', u.last_name) AS member_name 
                    FROM
                      equalityrecord_mix_type mt
                      LEFT JOIN equalityrecord_user u ON u.id = mt.user_id
                    ${filterQuery}
                    ORDER BY mt.id DESC
                   ;
      `;

      const result = await this.sdk.rawQuery(rawSql);
      const result2 = await this.sdk.rawQuery(rawSql2);

      const total = result2.length;
      const numPages = Math.ceil(total / limit);
      // const offset = (page - 1) * limit;

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllMultiFilter(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE mt.user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_mix_type mt
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` user_id = ${filter.user_id} `);
        }
        if (filter.name) {
          filterArr.push(` LOWER(mt.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.is_voiceover) {
          filterArr.push(` mt.is_voiceover = ${filter.is_voiceover} `);
        }
        if (filter.voiceover) {
          filterArr.push(` mt.voiceover = ${filter.voiceover} `);
        }
        if (filter.is_song) {
          filterArr.push(` mt.is_song = ${filter.is_song} `);
        }
        if (filter.song) {
          filterArr.push(` mt.song = ${filter.song} `);
        }
        if (filter.is_tracking) {
          filterArr.push(` mt.is_tracking = ${filter.is_tracking} `);
        }
        if (filter.tracking) {
          filterArr.push(` mt.tracking = ${filter.tracking} `);
        }
        // if (filter.color) {
        //   filterArr.push(` mt.color LIKE '%${filter.color}%' `);
        // }
        // user can submit multi-colors
        // filter.colors = ['red', 'blue', 'green']
        if (filter.colors) {
          if (filter.colors.length === 1) {
            filterArr.push(` mt.color LIKE '%${filter.colors[0]}%' `);
          } else if (filter.colors.length > 1) {
            let colorFilterArr = [];
            for (let i = 0; i < filter.colors.length; i++) {
              colorFilterArr.push(` mt.color LIKE '%${filter.colors[i]}%' `);
            }
            filterArr.push(` (${colorFilterArr.join(" OR ")}) `);
          }
        }
        if (filter.price) {
          filterArr.push(` mt.price = ${filter.price} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let rawSql = `SELECT
                      mt.*
                    FROM
                      equalityrecord_mix_type mt
                    ${filterQuery}
                    ORDER BY mt.id DESC
                    LIMIT ${limit} OFFSET ${offset};
      `;

      let rawSql2 = `SELECT
                      mt.*
                    FROM
                      equalityrecord_mix_type mt
                    ${filterQuery}
                    ORDER BY mt.id DESC
      `;

      const result = await this.sdk.rawQuery(rawSql);
      const result2 = await this.sdk.rawQuery(rawSql2);

      const total = result2.length;
      const numPages = Math.ceil(total / limit);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllMultiFilterForManager(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      let filterQuery = "";
      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not authorized to perform this action"
        };
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_mix_type mt
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      // const total = countResult[0].total;
      // const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (!filter.member_ids) {
        return {
          error: true,
          message: "At least one member id is required to perform this action"
        };
      }

      if (Object.keys(filter).length > 0) {
        if (filter.member_ids && req.role === "manager" && filter.member_ids.length === 1) {
          filterArr.push(` mt.user_id = ${filter.member_ids[0]} `);
        } else if (filter.member_ids && req.role === "manager" && filter.member_ids.length > 1) {
          filterArr.push(` mt.user_id IN (${filter.member_ids.join(",")}) `);
        }

        if (filter.name) {
          filterArr.push(` LOWER(mt.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.is_voiceover) {
          filterArr.push(` mt.is_voiceover = ${filter.is_voiceover} `);
        }
        if (filter.voiceover) {
          filterArr.push(` mt.voiceover = ${filter.voiceover} `);
        }
        if (filter.is_song) {
          filterArr.push(` mt.is_song = ${filter.is_song} `);
        }
        if (filter.song) {
          filterArr.push(` mt.song = ${filter.song} `);
        }
        if (filter.is_tracking) {
          filterArr.push(` mt.is_tracking = ${filter.is_tracking} `);
        }
        if (filter.tracking) {
          filterArr.push(` mt.tracking = ${filter.tracking} `);
        }
        // if (filter.color) {
        //   filterArr.push(` mt.color LIKE '%${filter.color}%' `);
        // }
        // user can submit multi-colors
        // filter.colors = ['red', 'blue', 'green']
        if (filter.colors) {
          if (filter.colors.length === 1) {
            filterArr.push(` mt.color LIKE '%${filter.colors[0]}%' `);
          } else if (filter.colors.length > 1) {
            let colorFilterArr = [];
            for (let i = 0; i < filter.colors.length; i++) {
              colorFilterArr.push(` mt.color LIKE '%${filter.colors[i]}%' `);
            }
            filterArr.push(` (${colorFilterArr.join(" OR ")}) `);
          }
        }
        if (filter.price) {
          filterArr.push(` mt.price = ${filter.price} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let rawSql = `SELECT
                      mt.*
                    FROM
                      equalityrecord_mix_type mt
                    ${filterQuery}
                    ORDER BY mt.id DESC
                    LIMIT ${limit} OFFSET ${offset};
      `;
      let rawSql2 = `SELECT
                      mt.*
                    FROM
                      equalityrecord_mix_type mt
                    ${filterQuery}
                    ORDER BY mt.id DESC
      `;

      const result = await this.sdk.rawQuery(rawSql);
      const result2 = await this.sdk.rawQuery(rawSql2);

      const total = result2.length;
      const numPages = Math.ceil(total / limit);
      // const offset = (page - 1) * limit;

      // pagination

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOne(req) {
    try {
      this.sdk = this.getSDK(req);

      let where = "";
      if (req.role !== "admin") {
        where = `WHERE id = ${req.params.id} AND user_id = ${req.user_id}`;
      } else {
        where = `WHERE id = ${req.params.id}`;
      }

      let rawSql = `
        SELECT *
        FROM equalityrecord_mix_type
        ${where}
        ;
      `;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          error: false,
          model: result[0]
        };
      } else {
        return {
          error: true,
          model: {}
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("mix_type");

      let subProjectsArr = JSON.parse(req.body.sub_projects);

      let isVoiceover = 0;
      let isSong = 0;
      let isTracking = 0;
      let voiceoverCount = 0;
      let songCount = 0;
      let trackingCount = 0;
      if (subProjectsArr.length > 0) {
        let voiceovers = subProjectsArr.filter((subProject) => subProject.type === "voiceover");

        let songs = subProjectsArr.filter((subProject) => subProject.type === "song");

        let trackings = subProjectsArr.filter((subProject) => subProject.type === "tracking");

        if (voiceovers.length > 0) {
          isVoiceover = 1;
          voiceoverCount = voiceovers.length;
        }
        if (songs.length > 0) {
          isSong = 1;
          songCount = songs.length;
        }
        if (trackings.length > 0) {
          isTracking = 1;
          trackingCount = trackings.length;
        }
      }

      const payload = filterEmptyFields({
        user_id: req.user_id,
        name: req.body.name,
        is_voiceover: isVoiceover,
        voiceover: voiceoverCount,
        is_song: isSong,
        song: songCount,
        is_tracking: isTracking,
        tracking: trackingCount,
        color: req.body.color,
        price: req.body.price,
        sub_projects: req.body.sub_projects,
        is_old: req.body.is_old ?? 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      const result = await this.sdk.insert(payload);

      if (result) {
        return {
          error: false,
          message: "Mix type added successfully"
        };
      } else {
        return {
          error: true,
          message: "Mix type could not be added"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("mix_type");

      const exists = await this.sdk.get({ id: req.params.id });
      if (exists.length === 0) {
        return {
          error: true,
          message: "Mix type not found"
        };
      }

      let isOld = exists[0].is_old;
      if (req.body.sub_projects && JSON.parse(req.body.sub_projects).length > 0) {
        isOld = 1;
      }

      let subProjectsArr = JSON.parse(req.body.sub_projects);

      let isVoiceover = 0;
      let isSong = 0;
      let isTracking = 0;
      let voiceoverCount = 0;
      let songCount = 0;
      let trackingCount = 0;
      if (subProjectsArr.length > 0) {
        let voiceovers = subProjectsArr.filter((subProject) => subProject.type === "voiceover");

        let songs = subProjectsArr.filter((subProject) => subProject.type === "song");

        let trackings = subProjectsArr.filter((subProject) => subProject.type === "tracking");

        if (voiceovers.length > 0) {
          isVoiceover = 1;
          voiceoverCount = voiceovers.length;
        }
        if (songs.length > 0) {
          isSong = 1;
          songCount = songs.length;
        }
        if (trackings.length > 0) {
          isTracking = 1;
          trackingCount = trackings.length;
        }
      }

      await this.sdk.update(
        filterEmptyFields({
          name: req.body.name,
          is_voiceover: isVoiceover,
          voiceover: voiceoverCount,
          is_song: isSong,
          song: songCount,
          is_tracking: isTracking,
          tracking: trackingCount,
          color: req.body.color,
          price: req.body.price,
          sub_projects: req.body.sub_projects,
          is_old: isOld,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Mix type updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project");

      const projectExists = await this.sdk.get({
        mix_type_id: req.params.id
      });

      if (projectExists.length === 0) {
        this.sdk.setTable("mix_type");
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Mix type deleted successfully"
        };
      } else {
        return {
          error: true,
          message: "Mix type cannot be deleted because it is being used by a project"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
