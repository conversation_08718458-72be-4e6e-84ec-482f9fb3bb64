const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  getAll,
  retrieveAll,
  retrieveAllForManager,
  getOne,
  addOne,
  updateOne,
  deleteOne,
  getAllProducers,
  getClientMembers,
  createOrUpdateClientMember,
  seedClientsToClientMember,
  retrieveAllProject,
  getAllProject
} = require("../controllers/clientController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// Key: client
router.get("/get_all", middlewares, getAll);
router.get("/get_all/project", middlewares, getAllProject);
router.post("/retrieve", middlewares, retrieveAll);
router.post("/retrieve/project", middlewares, retrieveAllProject);
router.post("/manager/retrieve", middlewares, retrieveAllForManager);
router.get("/:id", public, getOne);
router.post("/add", middlewares, addOne);
router.put("/:id", public, updateOne);
router.delete("/:id", middlewares, deleteOne);
router.get("/get_all/producers", middlewares, getAllProducers);
router.get("/members/:id", middlewares, getClientMembers);
router.post("/member/create_or_update", middlewares, createOrUpdateClientMember);
router.patch("/member/seed", middlewares, seedClientsToClientMember);

module.exports = router;
