const { validateObject } = require("../../../services/ValidationService");
const WorkOrderService = require("../services/WorkOrderService");
const { getLocalPath } = require("../../../services/UtilService");

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new WorkOrderService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        uuidv4: "required",
        writer_id: "required",
        due_date: "required",
        artist_id: "required",
        engineer_id: "required",
        writer_cost: "required",
        artist_cost: "required",
        engineer_cost: "required",
        subproject_ids: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.addOne(req);
    return res.status(200).json({
      error: result.error,
      id: result.id ?? null,
      model: result.model,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.remove = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.remove(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.updateOne(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.view = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.view(req);
    return res.status(200).json({ error: false, model: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateStatus = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
        status: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.updateStatus(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.addNote = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
        note: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.addNote(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getWorkOrderDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        employee_type: "required",
        uuidv4: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.getWorkOrderDetails(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateLyrics = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        subproject_id: "required",
        lyrics: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.updateLyrics(req);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.uploadFiles = async (req, res) => {
  try {
    let urlArray = [];
    let urlArrayObject = [];

    for (const file of req.files) {
      const url = getLocalPath(file.path);
      urlArray.push(url);
      urlArrayObject.push({ url: url });
    }
    return res.status(200).json({ error: false, attachments: urlArrayObject });
  } catch (error) {
    console.log(error);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.deleteOneS3File = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        url: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.deleteOneS3File(req.body.url);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (error) {
    console.log(error);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.deleteMultipleS3Files = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        urls: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.deleteMultipleS3Files(req.body.urls);
    return res.status(200).json({ error: result.error, message: result.message });
  } catch (error) {
    console.log(error);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.uploadFilesData = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        workorder_id: "required",
        employee_type: "required",
        type: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    await service.uploadFilesData(req);
    return res.status(200).json({ error: false, message: "Files data uploaded" });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.deleteFilesData = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        workorder_id: "required",
        employee_id: "required",
        employee_type: "required",
        type: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    await service.deleteFilesData(req);
    return res.status(200).json({ error: false, message: "Files data deleted" });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllFilesByWorkOrderId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.getAllFilesByWorkOrderId(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllWriterFilesByWorkOrderId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.getAllEmployeeFilesByWorkOrderId(req, "writer");
    return res.status(200).json({ error: false, list: result.list, lyrics: result.lyrics });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllArtistFilesByWorkOrderId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.getAllEmployeeFilesByWorkOrderId(req, "artist");
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllEngineerFilesByWorkOrderId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.getAllEmployeeFilesByWorkOrderId(req, "engineer");
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllProducerFilesByWorkOrderId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.getAllEmployeeFilesByWorkOrderId(req, "producer");
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateWriter = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        workorder_id: "required",
        writer_id: "required",
        writer_cost: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.updateWorkOrderEmployee(req, req.body.writer_id, req.body.writer_cost, "writer");
    return res.status(200).json({ error: false, message: result.message, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateArtist = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        workorder_id: "required",
        artist_id: "required",
        artist_cost: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.updateWorkOrderEmployee(req, req.body.artist_id, req.body.artist_cost, "artist");
    return res.status(200).json({ error: false, message: result.message, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateEngineer = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        workorder_id: "required",
        engineer_id: "required",
        engineer_cost: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new WorkOrderService();
    const result = await service.updateWorkOrderEmployee(req, req.body.engineer_id, req.body.engineer_cost, "engineer");
    return res.status(200).json({ error: false, message: result.message, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.deleteWorkOrdersWithNoSubProjects = async (req, res) => {
  try {
    // Only admin should be able to access this endpoint
    if (req.role !== "admin") {
      return res.status(403).json({
        error: true,
        message: "Unauthorized. Only admins can perform this operation."
      });
    }

    const service = new WorkOrderService();
    const result = await service.deleteWorkOrdersWithNoSubProjects(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      count: result.count,
      deleted_ids: result.deleted_ids || []
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: true,
      message: err.message
    });
  }
};
