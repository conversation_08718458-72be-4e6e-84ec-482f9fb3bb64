const PasswordService = require("../../../services/PasswordService");
const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class ClientService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  jsonContainsBuilder(memberIds) {
    let memberIdsStr = "";
    if (memberIds.length === 1) {
      memberIdsStr = memberIds[0];
      return ` JSON_CONTAINS(cm.member_ids, '${memberIdsStr}')`;
    } else if (memberIds.length > 1) {
      let memberIdsArr = [];
      memberIds.forEach((m) => {
        memberIdsArr.push(` JSON_CONTAINS(cm.member_ids, '${m}')`);
      });
      return `(${memberIdsArr.join(" OR ")})`;
      // return memberIdsArr.join(" OR ");
    }
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Build the base WHERE clause for company sharing
      let baseWhereClause = "";
      if (isPartOfCompany) {
        // If user is part of a company, get both personal clients and company shared clients
        baseWhereClause = `WHERE (JSON_CONTAINS(cm.member_ids, '${req.user_id}') 
          OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1))`;
      } else {
        // If user is not part of a company, get only personal clients
        baseWhereClause = `WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}')`;
      }

      // Handle member role filtering
      if (req.role === "member") {
        if (!filter.member_ids) {
          filter.member_ids = [req.user_id];
        }
      }

      // Apply additional filters
      let filterArr = [];
      if (Object.keys(filter).length > 0) {
        if (filter.member_ids && filter.member_ids.length > 0) {
          filterArr.push(this.jsonContainsBuilder(filter.member_ids));
        }
        if (filter.program) {
          filterArr.push(` LOWER(c.program) LIKE '%${filter.program.toLowerCase()}%' `);
        }
        if (filter.position) {
          filterArr.push(` LOWER(c.position) LIKE '%${filter.position.toLowerCase()}%' `);
        }
        if (filter.name) {
          filterArr.push(` LOWER(c.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.email) {
          filterArr.push(` LOWER(c.email) LIKE '%${filter.email.toLowerCase()}%' `);
        }
        if (filter.phone) {
          filterArr.push(` LOWER(c.phone) LIKE '%${filter.phone.toLowerCase()}%' `);
        }
        if (filter.has_auth) {
          filterArr.push(` cm.has_auth = ${filter.has_auth} `);
        }
      }

      let filterQuery = baseWhereClause;
      if (filterArr.length > 0) {
        filterQuery += " AND " + filterArr.join(" AND ");
      }

      const offset = (page - 1) * limit;

      // Single optimized query with COUNT() OVER() for pagination and member details
      let result = await this.sdk.rawQuery(`
        SELECT 
          c.*, 
          cm.member_ids, 
          cm.has_auth,
          COUNT(*) OVER() as total_count
        FROM equalityrecord_client c
        LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        ${filterQuery}
        ORDER BY c.program ASC
        LIMIT ${limit} OFFSET ${offset};
      `);

      if (result.length > 0) {
        // Collect all unique member IDs from all clients
        let allMemberIds = new Set();
        result.forEach((client) => {
          if (client.member_ids) {
            let memberIds = JSON.parse(client.member_ids);
            memberIds.forEach((id) => allMemberIds.add(id));
          }
        });

        // Single query to fetch all member details
        let membersMap = new Map();
        if (allMemberIds.size > 0) {
          const memberIdsArray = Array.from(allMemberIds);
          const membersResult = await this.sdk.rawQuery(`
            SELECT 
              id, 
              CONCAT(first_name, ' ', last_name) as name
            FROM equalityrecord_user
            WHERE id IN (${memberIdsArray.join(",")})
          `);

          // Create a map for quick lookup
          membersResult.forEach((member) => {
            membersMap.set(member.id, {
              id: member.id,
              name: member.name
            });
          });
        }

        // Assign members to each client
        result.forEach((client) => {
          if (client.member_ids) {
            let memberIds = JSON.parse(client.member_ids);
            client.members = memberIds.map((id) => membersMap.get(id)).filter(Boolean);
          } else {
            client.members = [];
          }
        });

        const total = result[0].total_count;
        const numPages = Math.ceil(total / limit);

        // Remove total_count from results
        result.forEach((client) => delete client.total_count);

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllProject(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Build the base WHERE clause for company sharing and projects
      let baseWhereClause = "";
      if (isPartOfCompany) {
        // If user is part of a company, get both personal clients and company shared clients with projects
        baseWhereClause = `WHERE (JSON_CONTAINS(cm.member_ids, '${req.user_id}') 
          OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1))
          AND EXISTS (
            SELECT 1 
            FROM equalityrecord_project 
            WHERE equalityrecord_project.client_id = c.id
          )`;
      } else {
        // If user is not part of a company, get only personal clients with projects
        baseWhereClause = `WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}')
          AND EXISTS (
            SELECT 1 
            FROM equalityrecord_project 
            WHERE equalityrecord_project.client_id = c.id
          )`;
      }

      // Handle member role filtering
      if (req.role === "member") {
        if (!filter.member_ids) {
          filter.member_ids = [req.user_id];
        }
      }

      // Apply additional filters
      let filterArr = [];
      if (Object.keys(filter).length > 0) {
        if (filter.member_ids && filter.member_ids.length > 0) {
          filterArr.push(this.jsonContainsBuilder(filter.member_ids));
        }
        if (filter.program) {
          filterArr.push(` LOWER(c.program) LIKE '%${filter.program.toLowerCase()}%' `);
        }
        if (filter.position) {
          filterArr.push(` LOWER(c.position) LIKE '%${filter.position.toLowerCase()}%' `);
        }
        if (filter.name) {
          filterArr.push(` LOWER(c.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.email) {
          filterArr.push(` LOWER(c.email) LIKE '%${filter.email.toLowerCase()}%' `);
        }
        if (filter.phone) {
          filterArr.push(` LOWER(c.phone) LIKE '%${filter.phone.toLowerCase()}%' `);
        }
        if (filter.has_auth) {
          filterArr.push(` cm.has_auth = ${filter.has_auth} `);
        }
      }

      let filterQuery = baseWhereClause;
      if (filterArr.length > 0) {
        filterQuery += " AND " + filterArr.join(" AND ");
      }

      const offset = (page - 1) * limit;

      // Single optimized query with COUNT() OVER() for pagination and member details
      let result = await this.sdk.rawQuery(`
        SELECT
          c.*, 
          cm.member_ids, 
          cm.has_auth,
          COUNT(*) OVER() as total_count
        FROM 
          equalityrecord_client c
        LEFT JOIN 
          equalityrecord_client_member cm ON cm.client_id = c.id
        ${filterQuery}
        ORDER BY 
          c.program ASC
        LIMIT 
          ${limit} 
        OFFSET 
          ${offset};
      `);

      if (result.length > 0) {
        // Collect all unique member IDs from all clients
        let allMemberIds = new Set();
        result.forEach((client) => {
          if (client.member_ids) {
            let memberIds = JSON.parse(client.member_ids);
            memberIds.forEach((id) => allMemberIds.add(id));
          }
        });

        // Single query to fetch all member details
        let membersMap = new Map();
        if (allMemberIds.size > 0) {
          const memberIdsArray = Array.from(allMemberIds);
          const membersResult = await this.sdk.rawQuery(`
            SELECT 
              id, 
              CONCAT(first_name, ' ', last_name) as name
            FROM equalityrecord_user
            WHERE id IN (${memberIdsArray.join(",")})
          `);

          // Create a map for quick lookup
          membersResult.forEach((member) => {
            membersMap.set(member.id, {
              id: member.id,
              name: member.name
            });
          });
        }

        // Assign members to each client
        result.forEach((client) => {
          if (client.member_ids) {
            let memberIds = JSON.parse(client.member_ids);
            client.members = memberIds.map((id) => membersMap.get(id)).filter(Boolean);
          } else {
            client.members = [];
          }
        });

        const total = result[0].total_count;
        const numPages = Math.ceil(total / limit);

        // Remove total_count from results
        result.forEach((client) => delete client.total_count);

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
  async retrieveAllForManager(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   program: "Program Name",
      //   position: "manager",
      //   name: "John Doe",
      //   email: '<EMAIL>',
      //   phone: '01751336666'
      // };
      let filterQuery = "";
      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not authorized to access this resource"
        };
      }
      if (req.role === "manager") {
        if (!filter.member_ids) {
          return {
            error: true,
            message: "At least one member id is required for manager"
          };
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_client c
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.member_ids && filter.member_ids.length > 0) {
          filterArr.push(this.jsonContainsBuilder(filter.member_ids));
        }

        if (filter.program) {
          filterArr.push(` LOWER(c.program) LIKE '%${filter.program.toLowerCase()}%' `);
        }
        if (filter.position) {
          filterArr.push(` LOWER(c.position) LIKE '%${filter.position.toLowerCase()}%' `);
        }
        if (filter.name) {
          filterArr.push(` LOWER(c.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.email) {
          filterArr.push(` LOWER(c.email) LIKE '%${filter.email.toLowerCase()}%' `);
        }
        if (filter.phone) {
          filterArr.push(` LOWER(c.phone) LIKE '%${filter.phone.toLowerCase()}%' `);
        }
        if (filter.has_auth) {
          filterArr.push(` cm.has_auth = ${filter.has_auth} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let result = await this.sdk.rawQuery(`
        SELECT
          c.*, cm.member_ids, cm.has_auth
        FROM equalityrecord_client c
          LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        ${filterQuery}
        ORDER BY c.program ASC
        LIMIT ${limit} OFFSET ${offset};
      `);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          if (result[i].member_ids) {
            let memberIds = JSON.parse(result[i].member_ids);
            let members = []; // [{ id: 1, name: 'John Doe' }]
            this.sdk.setTable("user");
            if (memberIds.length === 1) {
              let member = await this.sdk.get({ id: memberIds[0] });
              if (member.length > 0) {
                members.push({
                  id: member[0].id,
                  name: member[0].first_name + " " + member[0].last_name
                });
              }
            } else if (memberIds.length > 1) {
              console.log("HER5E", memberIds);
              for (let i = 0; i < memberIds.length; i++) {
                let member = await this.sdk.get({ id: memberIds[i] });
                if (member.length > 0) {
                  members.push({
                    id: member[0].id,
                    name: member[0].first_name + " " + member[0].last_name
                  });
                }
              }
            }
            result[i].members = members;
          }
        }

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllForManagerProject(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   program: "Program Name",
      //   position: "manager",
      //   name: "John Doe",
      //   email: '<EMAIL>',
      //   phone: '01751336666'
      // };
      let filterQuery = "";
      if (req.role !== "manager") {
        return {
          error: true,
          message: "You are not authorized to access this resource"
        };
      }
      if (req.role === "manager") {
        if (!filter.member_ids) {
          return {
            error: true,
            message: "At least one member id is required for manager"
          };
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_client c
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.member_ids && filter.member_ids.length > 0) {
          filterArr.push(this.jsonContainsBuilder(filter.member_ids));
        }

        if (filter.program) {
          filterArr.push(` LOWER(c.program) LIKE '%${filter.program.toLowerCase()}%' `);
        }
        if (filter.position) {
          filterArr.push(` LOWER(c.position) LIKE '%${filter.position.toLowerCase()}%' `);
        }
        if (filter.name) {
          filterArr.push(` LOWER(c.name) LIKE '%${filter.name.toLowerCase()}%' `);
        }
        if (filter.email) {
          filterArr.push(` LOWER(c.email) LIKE '%${filter.email.toLowerCase()}%' `);
        }
        if (filter.phone) {
          filterArr.push(` LOWER(c.phone) LIKE '%${filter.phone.toLowerCase()}%' `);
        }
        if (filter.has_auth) {
          filterArr.push(` cm.has_auth = ${filter.has_auth} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let result = await this.sdk.rawQuery(`
        SELECT
    c.*, 
    cm.member_ids, 
    cm.has_auth
FROM 
    equalityrecord_client c
LEFT JOIN 
    equalityrecord_client_member cm ON cm.client_id = c.id
WHERE 
    EXISTS (
        SELECT 1 
        FROM  equalityrecord_project 
        WHERE  equalityrecord_project.client_id = c.id
    )
    ${filterQuery}
ORDER BY 
    c.program ASC
LIMIT 
    ${limit} 
OFFSET 
    ${offset};

      `);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          if (result[i].member_ids) {
            let memberIds = JSON.parse(result[i].member_ids);
            let members = []; // [{ id: 1, name: 'John Doe' }]
            this.sdk.setTable("user");
            if (memberIds.length === 1) {
              let member = await this.sdk.get({ id: memberIds[0] });
              members.push({
                id: member[0].id,
                name: member[0].first_name + " " + member[0].last_name
              });
            } else if (memberIds.length > 1) {
              for (let i = 0; i < memberIds.length; i++) {
                let member = await this.sdk.get({ id: memberIds[i] });
                members.push({
                  id: member[0].id,
                  name: member[0].first_name + " " + member[0].last_name
                });
              }
            }
            result[i].members = members;
          }
        }

        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Query modification to include company shared clients
      let sqlQuery = "";

      if (isPartOfCompany) {
        // If user is part of a company, get both personal clients and company shared clients
        sqlQuery = `SELECT
          c.*
        FROM equalityrecord_client c
        LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}') 
          OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1)
        ORDER BY c.program ASC`;
      } else {
        // If user is not part of a company, get only personal clients
        sqlQuery = `SELECT
          c.*
        FROM equalityrecord_client c
        LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}')
        ORDER BY c.program ASC`;
      }

      const result = await this.sdk.rawQuery(sqlQuery);

      return {
        list: result
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllProject(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Query modification to include company shared clients
      let sqlQuery = "";

      if (isPartOfCompany) {
        // If user is part of a company, get both personal clients and company shared clients with projects
        sqlQuery = `SELECT
          c.*
        FROM equalityrecord_client c
        LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        WHERE (JSON_CONTAINS(cm.member_ids, '${req.user_id}') 
          OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1))
          AND EXISTS (
            SELECT 1
            FROM equalityrecord_project p
            WHERE p.client_id = c.id
          )
        ORDER BY c.program ASC`;
      } else {
        // If user is not part of a company, get only personal clients with projects
        sqlQuery = `SELECT
          c.*
        FROM equalityrecord_client c
        LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}')
          AND EXISTS (
            SELECT 1
            FROM equalityrecord_project p
            WHERE p.client_id = c.id
          )
        ORDER BY c.program ASC`;
      }

      const result = await this.sdk.rawQuery(sqlQuery);

      return {
        list: result
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllProducers(req) {
    try {
      this.sdk = this.getSDK(req);
      const result = await this.sdk.rawQuery(`
        SELECT *
          FROM equalityrecord_employee
          WHERE is_producer=1
          ORDER BY id DESC;
      `);

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOne(req) {
    try {
      this.sdk = this.getSDK(req);

      let where = `WHERE c.id = ${req.params.id}`;
      // if (req.role !== "admin") {
      //   where = `WHERE id = ${req.params.id} AND user_id = ${req.user_id}`;
      // } else {
      //   where = `WHERE id = ${req.params.id}`;
      // }

      let rawSql = `
        SELECT c.id, c.program, c.position,
        c.name, c.email, c.phone,
        c.create_at, c.update_at,
        cm.member_ids, cm.has_auth
        FROM equalityrecord_client c
        LEFT JOIN equalityrecord_client_member cm ON cm.client_id = c.id
        ${where};
      `;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        let memberIds = JSON.parse(result[0].member_ids);
        let members = []; // [{ id: 1, name: 'John Doe' }]
        this.sdk.setTable("user");
        if (memberIds.length === 1) {
          let member = await this.sdk.get({ id: memberIds[0] });
          members.push({
            id: member[0].id,
            name: member[0].first_name + " " + member[0].last_name
          });
        } else if (memberIds.length > 1) {
          let member = await this.sdk.get({ id: memberIds });
          member.forEach((m) => {
            members.push({
              id: m.id,
              name: m.first_name + " " + m.last_name
            });
          });
        }
        result[0].members = members;
        return {
          error: false,
          model: result[0]
        };
      } else {
        return {
          error: true,
          model: {}
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("client");

      const existClient = await this.sdk.get({ email: req.body.email });
      if (existClient.length > 0) {
        throw new Error("Email exists");
      }

      const payload = {
        program: req.body.program,
        position: req.body.position,
        name: req.body.first_name + " " + req.body.last_name,
        email: req.body.email,
        phone: req.body.phone,
        has_auth: req.body.has_auth,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      };

      const result = await this.sdk.insert(payload);

      if (result) {
        // Check if the user is part of a company
        const companyInfo = await this.sdk.rawQuery(`
          SELECT mp.manager_id, mp.member_ids
          FROM equalityrecord_manager_permission mp
          WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}') OR mp.manager_id = ${req.user_id}
        `);

        let isPartOfCompany = companyInfo.length > 0;
        let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;
        let companyMemberIds = isPartOfCompany ? JSON.parse(companyInfo[0].member_ids) : [];

        // Add manager to company members if not already included
        if (isPartOfCompany && !companyMemberIds.includes(companyManagerId)) {
          companyMemberIds.push(companyManagerId);
        }

        // patch into client_member
        let memberIds = [];
        if (req.body.member_ids && req.body.member_ids.length > 0) {
          memberIds = req.body.member_ids;
        } else {
          memberIds = [parseInt(req.body.user_id)];
        }

        // If user is part of a company, add all company members
        if (isPartOfCompany) {
          // Combine specified members with all company members
          memberIds = [...new Set([...memberIds, ...companyMemberIds])];
        }

        this.sdk.setTable("client_member");
        let clientMemberId = await this.sdk.insert({
          client_id: result,
          member_ids: JSON.stringify(memberIds),
          has_auth: req.body.has_auth ? 1 : 0,
          company_id: isPartOfCompany ? companyManagerId : null,
          is_company_shared: isPartOfCompany ? 1 : 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        if (req.body.has_auth === 1) {
          this.sdk.setTable("user");
          const exist = await this.sdk.get({
            email: req.body.email
          });

          if (exist.length > 0) {
            throw new Error("Email exists");
          } else {
            this.sdk.setTable("user");
            let randomNumEightDigit = Math.floor(10000000 + Math.random() * 90000000);
            randomNumEightDigit = randomNumEightDigit.toString();
            let password = req.body.password ?? randomNumEightDigit;
            const hashPassword = await PasswordService.hash(password);
            const userId = await this.sdk.insert({
              email: req.body.email,
              password: hashPassword,
              role: "client",
              verify: 1,
              status: 1,
              type: 0,
              first_name: req.body.first_name,
              last_name: req.body.last_name,
              phone: req.body.phone,
              client_id: result,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date())
            });

            this.sdk.setTable("profile");

            await this.sdk.insert({
              user_id: userId,
              create_at: sqlDateFormat(new Date()),
              update_at: sqlDateTimeFormat(new Date()),
              company_name: req.body.program
            });

            if (userId) {
              // patch into client_member
              this.sdk.setTable("client_member");
              await this.sdk.update(
                {
                  client_user_id: userId
                },
                clientMemberId
              );
              return {
                error: false,
                message: isPartOfCompany
                  ? "Client added, shared with all company members & client user created successfully"
                  : "Client added & client user created successfully",
                password: password ?? null
              };
            } else {
              return {
                error: false,
                message: "Client added but user could not be created"
              };
            }
          }
        } else {
          return {
            error: false,
            message: isPartOfCompany ? "Client added and shared with all company members" : "Client added successfully"
          };
        }
      } else {
        return {
          error: true,
          message: "Client could not be added"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("client");

      const clientExists = await this.sdk.get({ id: req.params.id });
      if (clientExists.length === 0) {
        throw new Error("Client not found");
      }

      if (req.body.has_auth === 1) {
        this.sdk.setTable("user");
        const exist = await this.sdk.get({
          email: clientExists[0].email
        });

        if (exist.length > 0) {
          this.sdk.setTable("client");
          await this.sdk.update(
            filterEmptyFields({
              program: req.body.program,
              position: req.body.position,
              name: req.body.first_name + " " + req.body.last_name,
              phone: req.body.phone,
              update_at: sqlDateTimeFormat(new Date())
            }),
            req.params.id
          );
          return {
            error: false,
            message: "Client updated successfully but email already exists at user table. User creation skipped."
          };
        } else {
          this.sdk.setTable("user");
          let randomNumEightDigit = Math.floor(10000000 + Math.random() * 90000000);
          randomNumEightDigit = randomNumEightDigit.toString();
          let password = req.body.password ?? randomNumEightDigit;
          const hashPassword = await PasswordService.hash(password);
          const userId = await this.sdk.insert({
            email: clientExists[0].email,
            password: hashPassword,
            role: "client",
            verify: 1,
            status: 1,
            type: 0,
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            phone: req.body.phone,
            client_id: req.params.id ? Number(req.params.id) : null,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });

          this.sdk.setTable("profile");
          await this.sdk.insert({
            user_id: userId,
            create_at: sqlDateFormat(new Date()),
            update_at: sqlDateTimeFormat(new Date())
          });

          if (userId) {
            this.sdk.setTable("client");
            await this.sdk.update(
              filterEmptyFields({
                program: req.body.program,
                position: req.body.position,
                name: req.body.first_name + " " + req.body.last_name,
                phone: req.body.phone,
                has_auth: req.body.has_auth,
                update_at: sqlDateTimeFormat(new Date())
              }),
              req.params.id
            );

            return {
              error: false,
              message: "Client updated successfully and user created successfully",
              password: password
            };
          } else {
            this.sdk.setTable("client");
            await this.sdk.update(
              filterEmptyFields({
                program: req.body.program,
                position: req.body.position,
                name: req.body.first_name + " " + req.body.last_name,
                phone: req.body.phone,
                update_at: sqlDateTimeFormat(new Date())
              }),
              req.params.id
            );
            return {
              error: false,
              message: "Client updated successfully but user could not be created"
            };
          }
        }
      } else {
        this.sdk.setTable("client");
        await this.sdk.update(
          filterEmptyFields({
            program: req.body.program,
            position: req.body.position,
            name: req.body.first_name + " " + req.body.last_name,
            phone: req.body.phone,
            has_auth: 0,
            update_at: sqlDateTimeFormat(new Date())
          }),
          req.params.id
        );

        this.sdk.setTable("user");
        const userExists = await this.sdk.get({ email: clientExists[0].email });

        if (userExists.length > 0) {
          this.sdk.setTable("user");
          await this.sdk.update(
            filterEmptyFields({
              first_name: req.body.first_name,
              last_name: req.body.last_name,
              phone: req.body.phone,
              status: 0,
              update_at: sqlDateTimeFormat(new Date())
            }),
            userExists[0].id
          );

          return {
            error: false,
            message: "Client updated successfully and user login set to disabled (if exists)"
          };
        } else {
          return {
            error: false,
            message: "Client updated successfully"
          };
        }
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("project");

      const projectExists = await this.sdk.get({
        client_id: req.params.id
      });

      this.sdk.setTable("user");
      const userExists = await this.sdk.get({
        email: req.body.email
      });

      if (projectExists.length === 0 && userExists.length === 0) {
        this.sdk.setTable("client");
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Client deleted successfully"
        };
      } else {
        return {
          error: true,
          message: "Client cannot be deleted because it is being used by a project"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async seedClientsToClientMember(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("client");

      const clients = await this.sdk.get({});

      if (clients.length === 0) {
        return {
          error: true,
          message: "No clients found to seed"
        };
      }

      // this.sdk.setTable("client_member");
      // client_user_id fetch from user table by client_id
      let clientUserIds = []; // [{ client_id: 1, client_user_id: 1 }]
      let userSql = `
        SELECT id AS client_user_id, client_id
        FROM equalityrecord_user
        WHERE role = 'client' AND client_id IS NOT NULL;
      `;
      const clientUsers = await this.sdk.rawQuery(userSql);

      // push into clientUserIds with client_id, client_user_id keys values
      clientUsers.forEach((cu) => {
        clientUserIds.push({
          client_id: cu.client_id,
          client_user_id: cu.client_user_id
        });
      });

      // console.log("clientUserIds", clientUserIds);

      this.sdk.setTable("client_member");
      for (let i = 0; i < clients.length; i++) {
        let checkIfClientExists = await this.sdk.get({
          client_id: clients[i].id
        });

        if (checkIfClientExists.length > 0) continue;

        let client = clients[i];
        let clientUserId = clientUserIds.find((cu) => cu.client_id === client.id)?.client_user_id;
        let memberOneId = clients[i].user_id ?? null;
        let memberTwoId = clients[i].member_two_user_id ?? null;

        let memberIds = []; // [1, 2]

        if (req.body.user_id) {
          memberIds.push(parseInt(req.body.user_id));
        }

        if (memberOneId) {
          memberIds.push(memberOneId);
        }
        if (memberTwoId) {
          memberIds.push(memberTwoId);
        }

        // console.log("memberIds", memberIds);

        let clientMember = {
          client_id: Number(client.id),
          client_user_id: clientUserId ? Number(clientUserId) : null,
          member_ids: JSON.stringify(memberIds),
          has_auth: client.has_auth ? 1 : 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        };
        console.log("clientMember", clientMember);

        await this.sdk.insert(clientMember);
      }

      return {
        error: false,
        message: "Clients seeded to client_member successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async createOrUpdateClientMember(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("client_member");

      let memberIds = req.body.member_ids;
      if (!memberIds || memberIds.length === 0) {
        throw new Error("At least one member id is required");
      }

      let payload = {
        client_id: req.body.client_id,
        client_user_id: req.body.client_user_id ?? null,
        member_ids: memberIds ? JSON.stringify(memberIds) : null,
        has_auth: req.body.has_auth && req.body.has_auth ? 1 : 0,
        update_at: sqlDateTimeFormat(new Date())
      };

      const checkIfClientExists = await this.sdk.get({
        client_id: req.body.client_id
      });

      if (checkIfClientExists.length > 0) {
        await this.sdk.update(filterEmptyFields(payload), checkIfClientExists[0].id);
        return {
          error: false,
          message: "Client member updated successfully"
        };
      } else {
        payload.create_at = sqlDateFormat(new Date());
        await this.sdk.insert(payload);
        return {
          error: false,
          message: "Client member created successfully"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getClientMembers(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("client_member");

      const rawSql = `
        SELECT
          ecm.*, ec.name AS client_name
        FROM equalityrecord_client ec
        INNER JOIN equalityrecord_client_member ecm ON ecm.client_id = ec.id
        WHERE ec.id = ${req.params.id}
      `;

      const clientMember = await this.sdk.rawQuery(rawSql);

      if (clientMember.length > 0) {
        let memberIds = JSON.parse(clientMember[0].member_ids);

        let members = [];
        this.sdk.setTable("user");
        if (memberIds.length === 1) {
          let member = await this.sdk.get({ id: memberIds[0] });
          members.push({
            id: member[0].id,
            full_name: member[0].first_name + " " + member[0].last_name
          });
        } else if (memberIds.length > 1) {
          let rawSql = `
            SELECT id, CONCAT(first_name, ' ', last_name) AS full_name
            FROM equalityrecord_user
            WHERE id IN (${memberIds.join(",")})
          `;
          members = await this.sdk.rawQuery(rawSql);
        }

        clientMember[0].members = members;

        return {
          error: false,
          model: clientMember[0]
        };
      } else {
        return {
          error: true,
          model: {}
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
