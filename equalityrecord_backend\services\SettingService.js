const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class SettingService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("setting");

      let userId = null;
      if (req.role === "member") {
        userId = req.user_id;
      } else {
        userId = req.body.user_id ? Number(req.body.user_id) : null;
      }

      if (!userId) {
        return {
          error: true,
          message: "User ID is required."
        };
      }

      const result = await this.sdk.get({
        user_id: userId
      });

      if (result.length > 0) {
        return {
          error: false,
          list: result
        };
      } else {
        return {
          error: false,
          list: []
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateAll(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("setting");

      const { settings } = req.body;
      const exists = await this.sdk.get({
        user_id: req.user_id
      });

      if (exists.length > 0) {
        if (settings.length > 0) {
          const updateQuery = `
            UPDATE
              equalityrecord_setting
            SET
              setting_value = CASE
                ${settings.map((row) => `WHEN setting_key = '${row.setting_key}' THEN '${row.setting_value}'`).join(" ")}
                ELSE setting_value
              END,
              update_at = '${sqlDateTimeFormat(new Date())}'
            WHERE
              user_id = ${req.user_id} AND
              id IN (${exists.map((row) => row.id)});
          `;

          this.sdk.rawQuery(updateQuery);
        } else {
          return {
            error: true,
            message: "No settings to update."
          };
        }
      } else {
        if (settings.length > 0) {
          const insertQuery = `
            INSERT INTO equalityrecord_setting (
              user_id,
              setting_key,
              setting_value,
              create_at,
              update_at
            ) VALUES
              ${settings
                .map(
                  (row) =>
                    `(
                      ${req.user_id},
                      '${row.setting_key}',
                      '${row.setting_value}',
                      '${sqlDateTimeFormat(new Date())}',
                      '${sqlDateTimeFormat(new Date())}'
                    )`
                )
                .join(", ")};
          `;

          this.sdk.rawQuery(insertQuery);
        } else {
          return {
            error: true,
            message: "No settings to insert."
          };
        }
      }

      return {
        error: false,
        message: "Successfully updated all settings."
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async createOrUpdateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("setting");

      const exists = await this.sdk.get({
        setting_key: req.body.setting_key,
        user_id: req.user_id
      });

      if (exists.length > 0) {
        await this.sdk.update(
          {
            setting_value: req.body.setting_value,
            update_at: sqlDateTimeFormat(new Date())
          },
          exists[0].id
        );

        return {
          error: false,
          message: "Setting updated successfully"
        };
      } else {
        await this.sdk.insert({
          user_id: req.user_id,
          setting_key: req.body.setting_key,
          setting_value: req.body.setting_value,
          create_at: sqlDateTimeFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        return {
          error: false,
          message: "Setting created successfully"
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getSiteImages(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT
          setting_key,
          setting_value
        FROM
          equalityrecord_setting
        WHERE
          setting_key IN ('site_logo', 'landing_image', 'dev_site_logo')
      `);

      if (result.length > 0) {
        return {
          error: false,
          list: result
        };
      } else {
        return {
          error: false,
          list: []
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveSummary(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT
  SUM(project_discount) AS total_discount,
  SUM(project_total) AS total_total,
  SUM(project_expenses) AS total_expenses,
  SUM(project_file_cost) AS total_file_cost
FROM (
  SELECT
    p.discount AS project_discount,
    m.price AS project_total,
    COALESCE(se.total_employee_cost, 0) AS project_expenses,
    COALESCE(s.total_file_cost, 0) AS project_file_cost
  FROM equalityrecord_project p
  LEFT JOIN equalityrecord_mix_type m ON p.mix_type_id = m.id
  LEFT JOIN (
    SELECT
      s.project_id,
      SUM(COALESCE(se.total_employee_cost, 0)) AS total_employee_cost,
      SUM(COALESCE(s.file_cost, 0)) AS total_file_cost
    FROM equalityrecord_subproject s
    LEFT JOIN (
      SELECT
        se.subproject_id,
        SUM(COALESCE(se.employee_cost, 0)) AS total_employee_cost
      FROM equalityrecord_subproject_employee se
      GROUP BY se.subproject_id
    ) se ON s.id = se.subproject_id
    GROUP BY s.project_id
  ) se ON p.id = se.project_id
  WHERE p.mix_date BETWEEN '${req.body.start_date}' AND '${req.body.end_date}'
    AND p.user_id = ${req.user_id}
) AS subquery;
      `);

      if (result.length > 0) {
        return {
          error: false,
          list: result
        };
      } else {
        return {
          error: false,
          list: []
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
