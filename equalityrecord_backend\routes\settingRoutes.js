const router = require('express').Router();
const ProjectMiddleware = require('../../../middleware/ProjectMiddleware');
const UrlMiddleware = require('../../../middleware/UrlMiddleware');
const HostMiddleware = require('../../../middleware/HostMiddleware');
const TokenMiddleware = require('../../../middleware/TokenMiddleware');
const {
  retrieveAll,
  updateAll,
  createOrUpdateOne,
  uploadFile,
  getSiteImages,
  retrieveSummary,
} = require('../controllers/settingController');
const UploadService = require('../../../services/UploadService');

const upload = UploadService.local_upload();

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];
const imageMiddlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
  upload.single('file'),
];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// Key: setting
router.post('/retrieve_all', middlewares, retrieveAll);
router.put('/update_all', middlewares, updateAll);
router.get('/public/site_images', public, getSiteImages);
router.post('/summary', middlewares, retrieveSummary);

router.post('/upload/file', imageMiddlewares, uploadFile);
router.put('/create_or_update_one', middlewares, createOrUpdateOne);

module.exports = router;
