const { validateObject } = require("../../../services/ValidationService");
const EmailService = require("../services/EmailService");

exports.getAll = async (req, res) => {
  try {
    const service = new EmailService();
    const result = await service.getAll(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getOneBySlug = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        slug: "required"
      },
      req.query
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new EmailService();
    const result = await service.getOneBySlug(req);
    return res.status(200).json({ error: false, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.sendEmail = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        from: "required",
        to: "required",
        subject: "required",
        body: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new EmailService();
    const result = await service.sendEmail(req);
    return res.status(200).json({ error: result.error, model: result.model, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(200);
    res.json({
      error: true,
      message: err.message
    });
  }
};
