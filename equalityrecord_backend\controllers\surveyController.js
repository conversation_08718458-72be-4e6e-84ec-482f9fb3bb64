const { validateObject } = require("../../../services/ValidationService");
const SurveyService = require("../services/SurveyService");

exports.getAll = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        user_id: "required",
      },
      req.query
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.getDashboardData(req);
    return res.status(200).json({ error: false, model: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.add = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        uuidv4: "required",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.add(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateOneLockDate = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        lock_date: "required",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.updateOneLockDate(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        theme_of_the_routine: "required",
        ideas: "required|array",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    if (req.body.ideas.length === 0) {
      return res.status(403).json({
        error: true,
        message: "Ideas are required",
      });
    }

    const service = new SurveyService();
    const result = await service.updateOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateOneFromClient = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
        theme_of_the_routine: "required",
        ideas: "required|array",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    if (req.body.ideas.length === 0) {
      return res.status(403).json({
        error: true,
        message: "Ideas are required",
      });
    }

    const service = new SurveyService();
    const result = await service.updateOneFromClient(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateOneThemeOfTheRoutine = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.updateOneThemeOfTheRoutine(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        uuidv4: "required",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.getDetails(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      model: result.model,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.view = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.view(req);
    return res
      .status(200)
      .json({ error: false, model: result.survey, ideas: result.ideas });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getSurveyEmailTemplate = async (req, res) => {
  try {
    const service = new SurveyService();
    const result = await service.getSurveyEmailTemplate(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateSurveyEmailTemplate = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.updateSurveyEmailTemplate(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getAllSurveyNotificationsByProjectId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        project_id: "required",
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.getAllSurveyNotificationsByProjectId(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateSurveyNotification = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new SurveyService();
    const result = await service.updateSurveyNotification(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};
