const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  createSubscription,
  getSubscriptionByUserId,
  updateSubscription,
  cancelSubscription,
  createManagerAccount,
  createManagerAccountWithPayment,
  createAdditionalMemberWithPayment,
  confirmAdditionalMemberPayment,
  createInvoice,
  processPayment,
  getInvoices,
  getInvoiceById,
  recordManualPayment,
  refundPayment,
  createCoupon,
  getCoupons,
  getCouponByCode,
  deleteCoupon,
  getPublicInvoice,
  updatePublicInvoice,
  processPublicPayment,
  confirmPublicPayment,
  confirmPayment,
  createPaymentIntentForInvoiceItem,
  confirmInvoiceItemPayment,
  updateInvoice,
  getCompanyInvoicePaymentReport
} = require("../controllers/subscriptionController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// Subscription management routes
// Create a new subscription for a user
router.post("/", middlewares, createSubscription);

// Get subscription details for a user
router.get("/user/:userId?", middlewares, getSubscriptionByUserId);

// Update an existing subscription (change plan, add members, etc.)
router.put("/:subscriptionId", middlewares, updateSubscription);

// Cancel a subscription
router.delete("/:subscriptionId", middlewares, cancelSubscription);

// Create a manager account for a company
router.post("/manager", middlewares, createManagerAccount);

// Create a manager account with one-time payment
router.post("/manager/payment", middlewares, createManagerAccountWithPayment);

// Create an additional member (free for first member, payment required for additional)
router.post("/member/create", middlewares, createAdditionalMemberWithPayment);

// Confirm payment and create additional member
router.post("/member/payment/confirm", middlewares, confirmAdditionalMemberPayment);

// Invoice management routes
// Create a new invoice
router.post("/invoice", middlewares, createInvoice);

// Get all invoices with pagination and filtering
router.post("/invoices", middlewares, getInvoices);

// Get invoice details by ID
router.get("/invoice/:invoiceId", middlewares, getInvoiceById);

// Update an existing invoice and its items
router.put("/invoice/:invoiceId", middlewares, updateInvoice);

// Payment processing routes
// Process a payment for an invoice
router.post("/public/payment", public, processPayment);

// confirm a payment for an invoice
router.post("/public/payment/confirm", public, confirmPayment);

// Record a manual payment (check, cash, etc.)
router.post("/payment/manual", middlewares, recordManualPayment);

// Refund a payment
router.post("/payment/refund", middlewares, refundPayment);

// Coupon management routes
// Create a new coupon (admin only)
router.post("/coupon", middlewares, createCoupon);

// Get all coupons with pagination (admin only)
router.get("/coupons", middlewares, getCoupons);

// Get coupon details by code
router.get("/coupon/:code", middlewares, getCouponByCode);

// Delete a coupon (admin only)
router.delete("/coupon/:couponId", middlewares, deleteCoupon);

// Public routes for client-facing pages
// Payment page for clients (no authentication required)
router.get("/invoice/pay/:invoiceId/:token", public, getInvoiceById);

// New public routes for invoice access and updates
// Get invoice by ID and token (public)
router.get("/public/invoice/:invoiceId/:token", public, getPublicInvoice);

// Update invoice by ID and token (public)
router.put("/public/invoice/:invoiceId/:token", public, updatePublicInvoice);

// Process payment for invoice (public)
router.post("/public/invoice/:invoiceId/:token/payment", public, processPublicPayment);

// Confirm payment for invoice (public)
router.post("/public/invoice/:invoiceId/:token/payment/confirm", public, confirmPublicPayment);

// Create payment intent for invoice item's remaining balance
router.post("/invoice-item/payment", middlewares, createPaymentIntentForInvoiceItem);

// Confirm payment for invoice item's remaining balance
router.post("/invoice-item/payment/confirm", middlewares, confirmInvoiceItemPayment);

// Company reports
// Get company invoice payment report with breakdown by members and special items
router.post("/company/payment-report", middlewares, getCompanyInvoicePaymentReport);

module.exports = router;
