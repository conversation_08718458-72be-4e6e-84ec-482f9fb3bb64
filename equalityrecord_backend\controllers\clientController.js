const { validateObject } = require("../../../services/ValidationService");
const ClientService = require("../services/ClientService");

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ClientService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.retrieveAllProject = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ClientService();
    const result = await service.retrieveAllProject(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.retrieveAllForManager = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ClientService();
    const result = await service.retrieveAllForManager(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

/*
exports.retrieveAllForManagerProject = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ClientService();
    const result = await service.retrieveAllForManagerProject(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};
*/

exports.getAll = async (req, res) => {
  try {
    const service = new ClientService();
    const result = await service.getAll(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllProject = async (req, res) => {
  try {
    const service = new ClientService();
    const result = await service.getAllProject(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getAllProducers = async (req, res) => {
  try {
    const service = new ClientService();
    const result = await service.getAllProducers(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ClientService();
    const result = await service.getOne(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    // const validation = await validateObject(
    //   {
    //     first_name: "required",
    //     last_name: "required",
    //     program: "required",
    //     position: "required",
    //     email: "required",
    //     user_id: "required",
    //     phone: "required",
    //     has_auth: "required|integer"
    //   },
    //   req.body
    // );

    // if (validation.error) return res.status(403).json(validation);

    const service = new ClientService();
    const result = await service.addOne(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      password: result.password ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ClientService();
    const result = await service.updateOne(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      password: result.password ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.deleteOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ClientService();
    const result = await service.deleteOne(req);

    if (!result.error) {
      return res.status(200).json({ error: false, message: result.message });
    } else {
      return res.status(200).json({ error: true, message: result.message });
    }
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.seedClientsToClientMember = async (req, res) => {
  try {
    if (req.role !== "admin" && req.user_id !== 4) {
      return res.status(403).json({
        error: true,
        message: "You do not have permission to perform this action."
      });
    }

    const service = new ClientService();
    const result = await service.seedClientsToClientMember(req);
    return res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.createOrUpdateClientMember = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        client_id: "required|integer",
        client_user_id: "integer",
        member_ids: "required|array",
        has_auth: "integer"
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ClientService();
    const result = await service.createOrUpdateClientMember(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      password: result.password ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};

exports.getClientMembers = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new ClientService();
    const result = await service.getClientMembers(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message
    });
  }
};
