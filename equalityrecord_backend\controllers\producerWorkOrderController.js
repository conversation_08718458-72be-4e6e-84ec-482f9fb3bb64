const { validateObject } = require("../../../services/ValidationService");
const ProducerWorkOrderService = require("../services/ProducerWorkOrderService");
const { getLocalPath } = require("../../../services/UtilService");

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new ProducerWorkOrderService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({ error: false, list: result.list, num_pages: result.num_pages, page: result.page, total: result.total });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        uuidv4: "required",
        subproject_id: "required",
        producer_id: "required",
        producer_cost: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProducerWorkOrderService();
    const result = await service.addOne(req);
    return res.status(200).json({ error: result.error, id: result.id ?? null, model: result.model, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getProducerWorkOrderDetails = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        uuidv4: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProducerWorkOrderService();
    const result = await service.getProducerWorkOrderDetails(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new ProducerWorkOrderService();
    const result = await service.updateOne(req);
    return res.status(200).json({ error: false, model: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};
