const { validateObject } = require("../../../services/ValidationService");
const UserService = require("../services/UserService");
const { getLocalPath } = require("../../../services/UtilService");

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new UserService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getUserDetailsById = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new UserService();
    const result = await service.getUserDetailsById(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllClientByMemberId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new UserService();
    const result = await service.getAllClientByMemberId(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllClients = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.getAllClients(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllClientsProject = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.getAllClientsProject(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getUserSubscription = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.getUserSubscription(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        first_name: "required",
        last_name: "required",
        email: "required",
        password: "required"
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new UserService();
    const result = await service.addOne(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      user_id: result.user_id ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.updateOne(req);

    if (result) {
      res.status(200).json({
        error: result.error,
        message: result.message
      });
    } else {
      res.status(403).json({
        error: true,
        message: "User update failed"
      });
    }
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.assignClientToMembersByClientUserId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.assignClientToMembersByClientUserId(req);

    res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.deleteOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.deleteOne(req);

    res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.uploadFile = async (req, res) => {
  try {
    const url = getLocalPath(req.file.path);
    return res.status(200).json({ error: false, url: url });
  } catch (error) {
    console.log(error);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.uploadPhoto = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new UserService();
    const result = await service.uploadPhoto(req);
    return res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getAllMembersByManagerId = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new UserService();
    const result = await service.getAllMembersByManagerId(req);
    return res.status(200).json({
      error: result.error,
      list: result.list,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createManagerWithPermissions = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        email: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.createManagerWithPermissions(req);

    return res.status(200).json({
      error: result.error,
      message: result.message,
      user_id: result.user_id ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.addMemberToCompany = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        member_id: "required|integer"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.addMemberToCompany(req);

    return res.status(200).json({
      error: result.error,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getCompanyInfo = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.getCompanyInfo(req);

    return res.status(200).json({
      error: result.error,
      is_company: result.is_company,
      is_manager: result.is_manager,
      company: result.company,
      message: result.message
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createCompanyMemberWithPayment = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        email: "required",
        first_name: "required",
        last_name: "required",
        payment_info: "required"
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.createCompanyMemberWithPayment(req);

    return res.status(200).json({
      error: result.error,
      message: result.message,
      user_id: result.user_id ?? null,
      checkout_session_id: result.checkout_session_id ?? null,
      stripe_checkout_url: result.stripe_checkout_url ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.confirmCompanyMemberPayment = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.confirmCompanyMemberPayment(req);

    return res.status(200).json({
      error: result.error,
      message: result.message,
      user_id: result.user_id ?? null
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.createInvoiceSubscriptionPayment = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.createInvoiceSubscriptionPayment(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      checkout_session_id: result.checkout_session_id,
      stripe_checkout_url: result.stripe_checkout_url
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.confirmInvoiceSubscriptionPayment = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required"
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);

    const service = new UserService();
    const result = await service.confirmInvoiceSubscriptionPayment(req);

    return res.status(200).json({
      error: result.error,
      message: result.message,
      has_invoice_subscription: result.has_invoice_subscription
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.fetchAllLegalDocuments = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.fetchAllLegalDocuments(req);
    return res.status(200).json({
      error: result.error,
      message: result.message,
      data: result.data
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};

exports.getUserClients = async (req, res) => {
  try {
    const service = new UserService();
    const result = await service.getUserClients(req);
    return res.status(200).json({ error: result.error, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message
    });
  }
};
