const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  getAll,
  retrieveAll,
  retrieveAllMultiFilter,
  getOne,
  addOne,
  updateOne,
  deleteOne,
  getAllMixTypesForClient,
  retrieveAllMultiFilterForManager,
} = require("../controllers/mixTypeController");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];

// Key: mix_type
router.get("/get_all", middlewares, getAll);
router.get("/client/get_all", middlewares, getAllMixTypesForClient);
router.post("/retrieve", middlewares, retrieveAll);
router.post("/retrieve_all_multi_filter", middlewares, retrieveAllMultiFilter);
router.post(
  "/manager/retrieve_all_multi_filter",
  middlewares,
  retrieveAllMultiFilterForManager
);
router.get("/:id", middlewares, getOne);
router.post("/add", middlewares, addOne);
router.put("/:id", middlewares, updateOne);
router.delete("/:id", middlewares, deleteOne);

module.exports = router;
