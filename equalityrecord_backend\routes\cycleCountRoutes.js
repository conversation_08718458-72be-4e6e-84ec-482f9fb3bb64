const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const { retrieveAll, addOne, editOne, viewOne, deleteOne, getAllClientCountTrack } = require("../controllers/cycleController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];

// Key: media
router.post("/retrieve-all", middlewares, retrieveAll);
router.post("/", middlewares, addOne);
router.put("/:id", middlewares, editOne);
router.get("/view/:id", middlewares, viewOne);
router.delete("/:id", middlewares, deleteOne);
router.get("/client/:client_id", middlewares, getAllClientCountTrack);

module.exports = router;
