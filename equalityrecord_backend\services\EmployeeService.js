const {
  sqlDateFormat,
  sqlDateTimeFormat,
  filterEmptyFields,
} = require("../../../services/UtilService");

module.exports = class MixTypeService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   name: "<PERSON>",
      //   email: '<EMAIL>',
      //   user_type: 'writer' | 'artist' | 'engineer' | 'producer',
      // };
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE e.user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_employee e
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` e.user_id = ${filter.user_id} `);
        }
        if (filter.name) {
          filterArr.push(
            ` LOWER(e.name) LIKE '%${filter.name.toLowerCase()}%' `
          );
        }
        if (filter.email) {
          filterArr.push(
            ` LOWER(e.email) LIKE '%${filter.email.toLowerCase()}%' `
          );
        }
        if (filter.user_type && filter.user_type !== "all") {
          filterArr.push(` e.is_${filter.user_type} = 1`);
        }
        if (filter.date_start && !filter.date_end) {
          filterArr.push(` spe.create_at >= '${filter.date_start}' `);
        }
        if (!filter.date_start && filter.date_end) {
          filterArr.push(` spe.create_at <= '${filter.date_end}' `);
        }
        if (filter.date_start && filter.date_end) {
          filterArr.push(
            ` spe.create_at >= '${filter.date_start}' AND spe.create_at <= '${filter.date_end}' `
          );
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      // let rawSql1 = `
      //   SELECT e.* FROM equalityrecord_employee e
      //   ${filterQuery}
      //   ORDER BY e.id DESC
      //   LIMIT ${limit} OFFSET ${offset};
      // `;

      // let rawSql2 = `SELECT
      //     e.*,
      //     SUM(CASE WHEN is_writer THEN spe.employee_cost ELSE 0 END) AS writer_earnings,
      //     SUM(CASE WHEN is_artist THEN spe.employee_cost ELSE 0 END) AS artist_earnings,
      //     SUM(CASE WHEN is_engineer THEN spe.employee_cost ELSE 0 END) AS engineer_earnings,
      //     SUM(CASE WHEN is_producer THEN spe.employee_cost ELSE 0 END) AS producer_earnings,
      //     (SUM(CASE WHEN is_writer THEN spe.employee_cost ELSE 0 END) +
      //       SUM(CASE WHEN is_artist THEN spe.employee_cost ELSE 0 END) +
      //       SUM(CASE WHEN is_engineer THEN spe.employee_cost ELSE 0 END) +
      //       SUM(CASE WHEN is_producer THEN spe.employee_cost ELSE 0 END)) AS total_earnings
      //   FROM
      //     equalityrecord_employee AS e
      //   LEFT JOIN
      //     equalityrecord_subproject_employee AS spe ON e.id = spe.employee_id
      //     ${filterQuery}
      //   GROUP BY
      //     e.id,
      //     e.name,
      //     e.email
      //   ORDER BY
      //     e.name ASC
      //   LIMIT ${limit} OFFSET ${offset};
      // `;

      let rawSql3 = `SELECT
          DISTINCT
            e.*,
            CONCAT(u.first_name, ' ', u.last_name) AS member_name,
            SUM(IF(spe.employee_type='writer', spe.employee_cost, 0)) AS writer_earnings,
            SUM(IF(spe.employee_type='artist', spe.employee_cost, 0)) AS artist_earnings,
            SUM(IF(spe.employee_type='engineer', spe.employee_cost, 0)) AS engineer_earnings,
            SUM(IF(spe.employee_type='producer', spe.employee_cost, 0)) AS producer_earnings,
            (SUM(IF(spe.employee_type='writer', spe.employee_cost, 0)) +
              SUM(IF(spe.employee_type='artist', spe.employee_cost, 0)) +
              SUM(IF(spe.employee_type='engineer', spe.employee_cost, 0)) +
              SUM(IF(spe.employee_type='producer', spe.employee_cost, 0))) AS total_earnings
          FROM
            equalityrecord_employee AS e
          LEFT JOIN
            equalityrecord_subproject_employee AS spe ON e.id = spe.employee_id
          LEFT JOIN equalityrecord_user u ON u.id = e.user_id
            ${filterQuery}
          GROUP BY
            e.id,
            e.name,
            e.email
          ORDER BY
            e.name ASC
          LIMIT ${limit} OFFSET ${offset}
          ;`;

      const result = await this.sdk.rawQuery(rawSql3);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveAllMultiFilter(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   name: "John Doe",
      //   email: '<EMAIL>',
      //   user_types: ['writer', 'artist', 'engineer', 'producer']
      // };
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE e.user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_employee e
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` e.user_id = ${filter.user_id} `);
        }
        if (filter.name) {
          filterArr.push(
            ` LOWER(e.name) LIKE '%${filter.name.toLowerCase()}%' `
          );
        }
        if (filter.email) {
          filterArr.push(
            ` LOWER(e.email) LIKE '%${filter.email.toLowerCase()}%' `
          );
        }
        // if (filter.user_type && filter.user_type !== 'all') {
        //   filterArr.push(` e.is_${filter.user_type} = 1`);
        // }
        if (filter.user_types && filter.user_types.length === 1) {
          filterArr.push(` e.is_${filter.user_types[0]} = 1`);
        } else if (filter.user_types && filter.user_types.length > 1) {
          let userTypes = filter.user_types;
          let userTypesQuery = [];
          userTypes.forEach((userType) => {
            userTypesQuery.push(` e.is_${userType} = 1`);
          });
          filterArr.push(` (${userTypesQuery.join(" OR ")}) `);
        }

        if (filter.date_start && !filter.date_end) {
          filterArr.push(` spe.create_at >= '${filter.date_start}' `);
        }
        if (!filter.date_start && filter.date_end) {
          filterArr.push(` spe.create_at <= '${filter.date_end}' `);
        }
        if (filter.date_start && filter.date_end) {
          filterArr.push(
            ` spe.create_at >= '${filter.date_start}' AND spe.create_at <= '${filter.date_end}' `
          );
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      // let rawSql1 = `
      //   SELECT e.* FROM equalityrecord_employee e
      //   ${filterQuery}
      //   ORDER BY e.id DESC
      //   LIMIT ${limit} OFFSET ${offset};
      // `;

      // let rawSql2 = `SELECT
      //     e.*,
      //     SUM(CASE WHEN is_writer THEN spe.employee_cost ELSE 0 END) AS writer_earnings,
      //     SUM(CASE WHEN is_artist THEN spe.employee_cost ELSE 0 END) AS artist_earnings,
      //     SUM(CASE WHEN is_engineer THEN spe.employee_cost ELSE 0 END) AS engineer_earnings,
      //     SUM(CASE WHEN is_producer THEN spe.employee_cost ELSE 0 END) AS producer_earnings,
      //     (SUM(CASE WHEN is_writer THEN spe.employee_cost ELSE 0 END) +
      //       SUM(CASE WHEN is_artist THEN spe.employee_cost ELSE 0 END) +
      //       SUM(CASE WHEN is_engineer THEN spe.employee_cost ELSE 0 END) +
      //       SUM(CASE WHEN is_producer THEN spe.employee_cost ELSE 0 END)) AS total_earnings
      //   FROM
      //     equalityrecord_employee AS e
      //   LEFT JOIN
      //     equalityrecord_subproject_employee AS spe ON e.id = spe.employee_id
      //     ${filterQuery}
      //   GROUP BY
      //     e.id,
      //     e.name,
      //     e.email
      //   ORDER BY
      //     e.name ASC
      //   LIMIT ${limit} OFFSET ${offset};
      // `;

      let rawSql3 = `SELECT
          DISTINCT
            e.*,
            SUM(IF(spe.employee_type='writer', spe.employee_cost, 0)) AS writer_earnings,
            SUM(IF(spe.employee_type='artist', spe.employee_cost, 0)) AS artist_earnings,
            SUM(IF(spe.employee_type='engineer', spe.employee_cost, 0)) AS engineer_earnings,
            SUM(IF(spe.employee_type='producer', spe.employee_cost, 0)) AS producer_earnings,
            (SUM(IF(spe.employee_type='writer', spe.employee_cost, 0)) +
              SUM(IF(spe.employee_type='artist', spe.employee_cost, 0)) +
              SUM(IF(spe.employee_type='engineer', spe.employee_cost, 0)) +
              SUM(IF(spe.employee_type='producer', spe.employee_cost, 0))) AS total_earnings
          FROM
            equalityrecord_employee AS e
          LEFT JOIN
            equalityrecord_subproject_employee AS spe ON e.id = spe.employee_id
            ${filterQuery}
          GROUP BY
            e.id,
            e.name,
            e.email
          ORDER BY
            e.name ASC
          LIMIT ${limit} OFFSET ${offset}
          ;`;

      const result = await this.sdk.rawQuery(rawSql3);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total,
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0,
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("employee");

      const result = await this.sdk.get({
        user_id: req.user_id,
      });

      return {
        error: false,
        list: result.length > 0 ? result : [],
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllManagementEmployee(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("management_employee");

      const result = await this.sdk.get({
        user_id: req.user_id,
      });

      return {
        error: false,
        list: result.length > 0 ? result : [],
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("employee");

      let where = "";
      if (req.role !== "admin") {
        where = `WHERE e.id = ${req.params.id} AND e.user_id = ${req.user_id}`;
      } else {
        where = `WHERE e.id = ${req.params.id}`;
      }

      let rawSql = `
        SELECT e.*
        FROM equalityrecord_employee e
        ${where}
        ;
      `;

      const result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          error: false,
          model: result[0],
        };
      } else {
        return {
          error: true,
          model: {},
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getEmployeeByGroup(req) {
    try {
      this.sdk = this.getSDK(req);

      // expected result
      // [
      //  writers: [{}, {}, {}],
      //  artists: [{}, {}, {}],
      //  engineers: [{}, {}, {}],
      // ]

      let filterQuery = "";
      if (req.role === "admin" || req.role === "manager") {
        filterQuery =
          "WHERE e.is_writer = 1 OR e.is_artist = 1 OR e.is_engineer = 1 OR e.is_producer = 1";
      } else {
        filterQuery += `WHERE e.user_id = ${req.user_id} AND (e.is_writer = 1 OR e.is_artist = 1 OR e.is_engineer = 1 OR e.is_producer = 1)`;
      }

      let fullQuery = `
        SELECT
          e.*
        FROM
          equalityrecord_employee e
        ${filterQuery}
      `;

      let result = await this.sdk.rawQuery(fullQuery);

      const writers = [];
      const artists = [];
      const engineers = [];
      const producers = [];

      result.forEach((employee) => {
        if (employee.is_writer) {
          writers.push(employee);
        }
        if (employee.is_artist) {
          artists.push(employee);
        }
        if (employee.is_engineer) {
          engineers.push(employee);
        }
        if (employee.is_producer) {
          producers.push(employee);
        }
      });

      return {
        writers,
        artists,
        engineers,
        producers,
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("employee");

      const payload = {
        ...req.body,
        user_id: req.user_id,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      };

      const result = await this.sdk.insert(payload);

      if (result) {
        return {
          error: false,
          message: "Employee created successfully",
          id: result,
        };
      } else {
        return {
          error: true,
          message: "Employee creation failed",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOneManagementEmployee(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("management_employee");

      const payload = {
        user_id: req.user_id,
        employee_id: req.body.employee_id,
        type: req.body.type,
        value: req.body.value,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date()),
      };

      const result = await this.sdk.insert(payload);

      if (result) {
        return {
          error: false,
          message: "Management employee created successfully",
        };
      } else {
        return {
          error: true,
          message: "Management employee creation failed",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async editOneManagementEmployee(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("management_employee");

      await this.sdk.update(
        filterEmptyFields({
          employee_id: req.body.employee_id,
          type: req.body.type,
          value: req.body.value,
          update_at: sqlDateTimeFormat(new Date()),
        }),
        req.params.id
      );

      if (result) {
        return {
          error: false,
          message: "Management employee updated successfully",
        };
      } else {
        return {
          error: true,
          message: "Management employee update failed",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("employee");

      await this.sdk.update(
        filterEmptyFields({
          ...req.body,
          update_at: sqlDateTimeFormat(new Date()),
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Employee updated successfully",
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("subproject_employee");

      const existEmployee = await this.sdk.get({
        employee_id: req.params.id,
      });

      if (existEmployee.length === 0) {
        this.sdk.setTable("employee");
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Employee deleted successfully",
        };
      } else {
        return {
          error: true,
          message:
            "Employee cannot be deleted because is already assigned to a subproject",
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
