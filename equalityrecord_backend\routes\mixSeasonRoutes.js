const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  getAll,
  retrieveAll,
  getOne,
  addOne,
  updateOne,
  deleteOne,
  retrieveAllForManager,
} = require("../controllers/mixSeasonController");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];

// Key: mix_season
router.post("/retrieve", middlewares, retrieveAll);
router.post("/manager/retrieve", middlewares, retrieveAllForManager);
router.get("/get_all", middlewares, getAll);
router.get("/:id", middlewares, getOne);
router.post("/add", middlewares, addOne);
router.put("/:id", middlewares, updateOne);
router.delete("/:id", middlewares, deleteOne);

module.exports = router;
