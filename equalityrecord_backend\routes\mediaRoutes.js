const router = require('express').Router();
const ProjectMiddleware = require('../../../middleware/ProjectMiddleware');
const UrlMiddleware = require('../../../middleware/UrlMiddleware');
const HostMiddleware = require('../../../middleware/HostMiddleware');
const TokenMiddleware = require('../../../middleware/TokenMiddleware');
const {
  retrieveAll,
  addOne,
  editOne,
  viewOne,
  deleteOne,
} = require('../controllers/mediaController');

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];

// Key: media
router.post('/retrieve-all', middlewares, retrieveAll);
router.post('/', middlewares, addOne);
router.put('/:id', middlewares, editOne);
router.get('/view/:id', middlewares, viewOne);
router.delete('/:id', middlewares, deleteOne);

module.exports = router;
