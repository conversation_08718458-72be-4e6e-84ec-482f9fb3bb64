const { validateObject } = require("../../../services/ValidationService");
const MixTypeService = require("../services/MixTypeService");

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new MixTypeService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.retrieveAllMultiFilter = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new MixTypeService();
    const result = await service.retrieveAllMultiFilter(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.retrieveAllMultiFilterForManager = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new MixTypeService();
    const result = await service.retrieveAllMultiFilterForManager(
      req,
      page,
      limit
    );
    return res.status(200).json({
      error: result.error,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
      message: result.message,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.getAll = async (req, res) => {
  try {
    const service = new MixTypeService();
    const result = await service.getAll(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.getAllMixTypesForClient = async (req, res) => {
  try {
    const service = new MixTypeService();
    const result = await service.getAllMixTypesForClient(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.getOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new MixTypeService();
    const result = await service.getOne(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        name: "required",
        // is_voiceover: "required",
        // voiceover: "required",
        // is_song: "required",
        // song: "required",
        color: "required",
        price: "required",
        sub_projects: "required",
      },
      req.body
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new MixTypeService();
    const result = await service.addOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new MixTypeService();
    const result = await service.updateOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      error: true,
      message: err.message,
    });
  }
};

exports.deleteOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: "required",
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new MixTypeService();
    const result = await service.deleteOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};
