const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const UploadService = require("../../../services/UploadService");
const upload = UploadService.local_upload();

const {
  retrieveAll,
  addOne,
  updateOne,
  view,
  remove,
  updateStatus,
  addNote,
  getWorkOrderDetails,
  uploadFiles,
  updateLyrics,
  uploadFilesData,
  deleteFilesData,
  deleteOneS3File,
  getAllFilesByWorkOrderId,
  getAllWriterFilesByWorkOrderId,
  getAllArtistFilesByWorkOrderId,
  getAllEngineerFilesByWorkOrderId,
  getAllProducerFilesByWorkOrderId,
  deleteMultipleS3Files,
  updateWriter,
  updateArtist,
  updateEngineer,
  deleteWorkOrdersWithNoSubProjects
} = require("../controllers/workOrderController");

const middlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, TokenMiddleware()];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];
const filesPublicMiddlewares = [ProjectMiddleware, UrlMiddleware, HostMiddleware, upload.array("files")];

// Key: work_order
router.post("/retrieve", middlewares, retrieveAll);
router.post("/add", middlewares, addOne);
router.put("/:id", public, updateOne);
router.get("/view/:id", middlewares, view);
router.delete("/:id", middlewares, remove);
router.delete("/cleanup/no-subprojects", middlewares, deleteWorkOrdersWithNoSubProjects);

router.put("/status", middlewares, updateStatus);
router.post("/note/add", middlewares, addNote);

router.put("/update/writer", middlewares, updateWriter);
router.put("/update/artist", middlewares, updateArtist);
router.put("/update/engineer", middlewares, updateEngineer);

router.post("/public/details", public, getWorkOrderDetails);
router.put("/public/lyrics", public, updateLyrics);
router.post("/public/upload_files", filesPublicMiddlewares, uploadFiles);
router.post("/public/upload_files_data", filesPublicMiddlewares, uploadFilesData);
router.delete("/public/upload_files_data", public, deleteFilesData);

router.delete("/public/file/s3/delete", public, deleteOneS3File);
router.delete("/public/file/s3/multi/delete", public, deleteMultipleS3Files);

router.get("/public/files/:id", public, getAllFilesByWorkOrderId);
router.get("/public/writer/files/:id", public, getAllWriterFilesByWorkOrderId);
router.get("/public/artist/files/:id", public, getAllArtistFilesByWorkOrderId);
router.get("/public/engineer/files/:id", public, getAllEngineerFilesByWorkOrderId);
router.get("/public/producer/files/:id", public, getAllProducerFilesByWorkOrderId);

module.exports = router;
