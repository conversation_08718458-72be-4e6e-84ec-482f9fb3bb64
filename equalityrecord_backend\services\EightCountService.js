const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
module.exports = class EightCountService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};

      let filterQuery = "";
      // if (req.role !== "admin") {
      //   filterQuery += `WHERE ec.user_id = ${req.user_id}`;
      // }

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` ec.user_id = ${filter.user_id} `);
        }
        // if (filter.name) {
        //   filterArr.push(
        //     ` LOWER(ec.name) LIKE '%${filter.name.toLowerCase()}%' `
        //   );
        // }
        if (filter.project_id) {
          filterArr.push(` ec.project_id = ${filter.project_id} `);
        }
        if (filter.is_paid) {
          filterArr.push(` ec.is_paid = ${filter.is_paid} `);
        }
        if (filter.version) {
          filterArr.push(` ec.version = ${filter.version} `);
        }
        if (filter.status) {
          filterArr.push(` ec.status = ${filter.status} `);
        }
        if (filter.is_duplicate) {
          filterArr.push(` ec.is_duplicate = ${filter.is_duplicate} `);
        }
        if (filter.duplicate_id) {
          filterArr.push(` ec.duplicate_id = ${filter.duplicate_id} `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_eight_count ec
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      const rawSql = `SELECT ec.*
        FROM equalityrecord_eight_count ec
        ${filterQuery}
        ORDER BY ec.id DESC
      LIMIT ${limit} OFFSET ${offset};`;

      let result = await this.sdk.rawQuery(rawSql);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("eight_count");

      const id = await this.sdk.insert(
        filterEmptyFields({
          project_id: req.body.project_id,
          json_data: req.body.json_data,
          user_id: req.user_id,
          user_role: req.user_role,
          is_paid: req.is_paid,
          version: req.body.version,
          status: req.body.status,
          is_duplicate: req.body.is_duplicate,
          storage: req.body.storage || null,
          duplicate_id: req.body.duplicate_id,
          real_create_at: req.body.real_create_at,
          uses_modified_system: req.body.uses_modified_system,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        })
      );

      if (id) {
        return {
          error: false,
          message: "Eight count added successfully.",
          id
        };
      } else {
        return {
          error: true,
          message: "Eight count add failed.",
          id: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async editOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("eight_count");

      await this.sdk.update(
        filterEmptyFields({
          json_data: req.body.json_data,
          user_id: req.user_id,
          user_role: req.body.user_role,
          is_paid: req.body.is_paid,
          version: req.body.version,
          status: req.body.status,
          is_duplicate: req.body.is_duplicate,
          storage: req.body.storage || null,
          duplicate_id: req.body.duplicate_id,
          real_create_at: req.body.real_create_at,
          uses_modified_system: req.body.uses_modified_system,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Eight count updated successfully."
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async viewOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("eight_count");

      const result = await this.sdk.get({
        project_id: req.params.id
      });

      return {
        error: false,
        model: result.length > 0 ? result[0] : {}
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("eight_count");

      const result = await this.sdk.get({ id: req.params.id });

      if (result.length > 0) {
        await this.sdk.delete({}, req.params.id);
        return {
          error: false,
          message: "Eight count deleted successfully."
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};
