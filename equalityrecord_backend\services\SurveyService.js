const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

module.exports = class ProjectService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  async getAll(req) {
    try {
      this.sdk = this.getSDK(req);

      // Fetch the total number of records
      const countResult = await this.sdk.rawQuery(`
        SELECT COUNT(*) AS total
        FROM equalityrecord_survey;
      `);
      const total = countResult[0].total;

      // Calculate the number of pages based on the total and the page size
      const pageSize = 10; // Change this to the desired page size
      const numPages = Math.ceil(total / pageSize);

      // Get the current page from the request (assuming it's passed in as a query parameter)
      const page = req.query.page || 1;

      // Calculate the offset for the current page
      const offset = (page - 1) * pageSize;

      const result = await this.sdk.rawQuery(`
        SELECT s.*,
        p.id AS project_id, p.team_name, p.content_status
        FROM equalityrecord_survey s
        LEFT JOIN equalityrecord_project p ON s.project_id = p.id
        LIMIT ${pageSize} OFFSET ${offset};
      `);

      return {
        list: result,
        num_pages: numPages,
        page: parseInt(page),
        total: total
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async retrieveSurvey(projectId) {
    try {
      const result = await this.sdk.rawQuery(`
        SELECT project_id
        FROM equalityrecord_survey 
        WHERE uuidv4 = '${projectId}';
      `);

      return result[0];
    } catch (error) {
      throw new Error(error);
    }
  }

  async getDetails(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      let result = await this.sdk.rawQuery(`
         SELECT
            es.*,
            epf.company_name,
            ep.team_name,
            c.program AS program_name,
            td.song_list as songlist,
            td.colors as color
        FROM equalityrecord_survey es
            INNER JOIN equalityrecord_project ep on es.project_id = ep.id
            LEFT JOIN equalityrecord_client c ON ep.client_id = c.id
            LEFT JOIN equalityrecord_profile epf on ep.user_id = epf.user_id
            LEFT JOIN equalityrecord_team_details td on ep.id = td.project_id
        WHERE es.uuidv4 = '${req.body.uuidv4}';
        `);

      let ideas = [];
      if (result.length > 0) {
        ideas = await this.sdk.rawQuery(`
          SELECT *
          FROM equalityrecord_idea
          WHERE project_id = '${result[0].project_id}';
        `);

        result[0].ideas = ideas.length > 0 ? ideas : [];

        return {
          error: false,
          message: "Project exists",
          model: result[0]
        };
      } else {
        return {
          error: true,
          message: "Project does not exist",
          model: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async add(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const exists = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (exists.length > 0) {
        return {
          error: true,
          message: "Survey already exists for this project"
        };
      }

      await this.sdk.insert({
        project_id: req.body.project_id,
        uuidv4: req.body.uuidv4,
        theme_of_the_routine: null,
        overall_description: null,
        email_status: 0, // todo send email, then set to 1
        email_retry: 0,
        status: 0,
        lock_date: req.body.lock_date,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return {
        error: false,
        message: "Survey created successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const survey = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (survey.length === 0) {
        return {
          error: true,
          message: "Survey does not exist"
        };
      }

      await this.sdk.update(
        filterEmptyFields({
          theme_of_the_routine: req.body.theme_of_the_routine,
          overall_description: req.body.overall_description,
          status: req.body.status,
          lock_date: req.body.lock_date,
          update_at: sqlDateTimeFormat(new Date())
        }),
        survey[0].id
      );

      this.sdk.setTable("team_details");

      const team_details = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (team_details.length > 0) {
        await this.sdk.update(
          filterEmptyFields({
            colors: req.body.color,
            song_list: req.body.songlist,
            update_at: sqlDateTimeFormat(new Date())
          }),
          team_details[0].id
        );
      }

      const existingIdeas = await this.sdk.rawQuery(`
        SELECT *
        FROM equalityrecord_idea
        WHERE project_id = '${req.body.project_id}';
      `);

      let ideas = req.body.ideas;
      // ideas = [{id: 1, value: "test"}, {id: 2, value: "test2"}]
      let existingIdeasCount = 0;
      if (existingIdeas.length > 0) {
        existingIdeasCount = existingIdeas.length;
        // if existingIdeasCount = 2
        // then modify ideas id
        // [{id: 1, value: "test"}, {id: 2, value: "test"}, {id: 3, value: "test"}, {id: 4, value: "test2"}]

        ideas = ideas.map((idea, index) => {
          return {
            id: existingIdeasCount + index + 1,
            value: idea.value
          };
        });
      } else {
        ideas = ideas.map((idea, index) => {
          return {
            id: index + 1,
            value: idea.value
          };
        });
      }

      if (ideas.length > 0) {
        // idea.value string may contain "some text" "some text or some text", remove any double quotes or single quotes
        // ideas = ideas.map((idea) => {
        //   return {
        //     id: idea.id,
        //     value: idea.value.replace(/["']/g, "")
        //   };
        // });

        // replace any next line characters to <br>
        ideas = ideas.map((idea) => {
          return {
            id: idea.id,
            value: idea.value.replace(/\n/g, "<br>")
          };
        });

        let insertValues = ideas
          .map((idea) => {
            return `('${survey[0].id}', '${req.body.project_id}', 'idea_${idea.id}', '${idea.value}', '${sqlDateFormat(new Date())}', '${sqlDateTimeFormat(
              new Date()
            )}')`;
          })
          .join(", ");

        await this.sdk.rawQuery(`INSERT INTO equalityrecord_idea (survey_id, project_id, idea_key, idea_value, create_at, update_at) VALUES ${insertValues};`);
      }

      // create a row at survey_notification table
      this.sdk.setTable("survey_notification");
      await this.sdk.insert({
        project_id: Number(req.body.project_id),
        survey_id: Number(survey[0].id),
        is_seen: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      return {
        error: false,
        message: "Survey updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOneFromClient(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const survey = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (survey.length === 0) {
        return {
          error: true,
          message: "Survey does not exist"
        };
      }

      await this.sdk.update(
        filterEmptyFields({
          theme_of_the_routine: req.body.theme_of_the_routine,
          overall_description: req.body.overall_description,
          status: req.body.status,
          lock_date: req.body.lock_date,
          color: req.body.color,
          songlist: req.body.songlist,
          update_at: sqlDateTimeFormat(new Date())
        }),
        survey[0].id
      );

      this.sdk.setTable("team_details");

      const team_details = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (team_details.length > 0) {
        await this.sdk.update(
          filterEmptyFields({
            colors: req.body.color,
            song_list: req.body.songlist,
            update_at: sqlDateTimeFormat(new Date())
          }),
          team_details[0].id
        );
      }

      const existingIdeas = await this.sdk.rawQuery(`
        SELECT *
        FROM equalityrecord_idea
        WHERE project_id = '${req.body.project_id}';
      `);

      let ideas = req.body.ideas;
      // ideas = [{id: 1, value: "test", dbId: 1}, {id: 2, value: "test2", dbId: null}]
      let userGivenExistingIdeas = []; // when dbId has value
      let userGivenNewIdeas = []; // when dbId is null

      userGivenExistingIdeas = ideas.filter((idea) => idea.dbId !== null);

      console.log("userGivenExistingIdeas ==>", userGivenExistingIdeas);
      userGivenNewIdeas = ideas.filter((idea) => idea.dbId === null);

      let existingIdeasCount = 0;
      console.log("existingIdeas ==>", existingIdeas);
      if (existingIdeas.length > 0) {
        existingIdeasCount = existingIdeas.length;

        if (userGivenExistingIdeas.length > 0) {
          userGivenExistingIdeas.forEach((idea) => {
            existingIdeas.forEach((existingIdea) => {
              if (idea.dbId === existingIdea.id) {
                this.sdk.setTable("idea");
                this.sdk.update(
                  filterEmptyFields({
                    idea_value: idea.value.replace(/\n/g, "<br>"),
                    update_at: sqlDateTimeFormat(new Date())
                  }),
                  existingIdea.id
                );
              }
            });
          });
        }

        if (userGivenNewIdeas.length > 0) {
          userGivenNewIdeas = userGivenNewIdeas.map((idea, index) => {
            return {
              id: existingIdeasCount + index + 1,
              value: idea.value
            };
          });
        }
      } else {
        if (userGivenNewIdeas.length > 0) {
          userGivenNewIdeas = userGivenNewIdeas.map((idea, index) => {
            return {
              id: index + 1,
              value: idea.value
            };
          });
        }
      }

      // for userGivenNewIdeas, insert the new ideas
      if (userGivenNewIdeas.length > 0) {
        let insertValues = userGivenNewIdeas
          .map((idea) => {
            return `('${survey[0].id}', '${req.body.project_id}', 'idea_${idea.id}', '${idea.value.replace(/\n/g, "<br>")}', '${sqlDateFormat(
              new Date()
            )}', '${sqlDateTimeFormat(new Date())}')`;
          })
          .join(", ");

        await this.sdk.rawQuery(`INSERT INTO equalityrecord_idea (survey_id, project_id, idea_key, idea_value, create_at, update_at) VALUES ${insertValues};`);
      }

      // create a row at survey_notification table
      this.sdk.setTable("survey_notification");
      const existingSurveyNotification = await this.sdk.get({
        project_id: req.body.project_id
      });

      if (existingSurveyNotification.length > 0) {
        await this.sdk.update(
          filterEmptyFields({
            is_seen: 0,
            update_at: sqlDateTimeFormat(new Date())
          }),
          existingSurveyNotification[0].id
        );
      } else {
        await this.sdk.insert({
          project_id: Number(req.body.project_id),
          survey_id: Number(survey[0].id),
          is_seen: 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
      }

      return {
        error: false,
        message: "Survey updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOneLockDate(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const survey = await this.sdk.get({
        id: req.params.id
      });

      if (survey.length === 0) {
        return {
          error: true,
          message: "Survey does not exist"
        };
      }

      await this.sdk.update(
        filterEmptyFields({
          status: req.body.status,
          lock_date: req.body.lock_date,
          update_at: sqlDateTimeFormat(new Date())
        }),
        survey[0].id
      );

      return {
        error: false,
        message: "Survey updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOneThemeOfTheRoutine(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      await this.sdk.update(
        filterEmptyFields({
          theme_of_the_routine: req.body.theme_of_the_routine,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Theme of the Routine updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async view(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey");

      const survey = await this.sdk.get({
        id: req.params.id
      });

      if (survey.length === 0) {
        return {
          survey: null,
          ideas: []
        };
      }

      const result = await this.sdk.rawQuery(`
        SELECT *
        FROM equalityrecord_idea 
        WHERE survey_id = '${survey.id}';
      `);

      return {
        survey: survey,
        ideas: result
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getSurveyEmailTemplate(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("email");

      const result = await this.sdk.get({
        slug: "survey"
      });

      if (result.length === 0) {
        return {
          error: true,
          message: "Email template does not exist"
        };
      }

      return {
        error: false,
        model: result[0]
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSurveyEmailTemplate(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("email");

      await this.sdk.update(
        filterEmptyFields({
          subject: req.body.subject,
          tag: req.body.tag,
          html: req.body.html,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Email template updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllSurveyNotificationsByProjectId(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey_notification");

      const result = await this.sdk.get({
        project_id: req.params.project_id
      });

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateSurveyNotification(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("survey_notification");

      await this.sdk.update(
        filterEmptyFields({
          is_seen: req.body.is_seen,
          update_at: sqlDateTimeFormat(new Date())
        }),
        req.params.id
      );

      return {
        error: false,
        message: "Email template updated successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }
};
