const router = require('express').Router();
const ProjectMiddleware = require('../../../middleware/ProjectMiddleware');
const UrlMiddleware = require('../../../middleware/UrlMiddleware');
const HostMiddleware = require('../../../middleware/HostMiddleware');
const TokenMiddleware = require('../../../middleware/TokenMiddleware');
const {
  getAll,
  retrieveAll,
  retrieveAllMultiFilter,
  getOne,
  addOne,
  updateOne,
  getEmployeeByGroup,
  deleteOne,
  getAllManagementEmployee,
  addOneManagementEmployee,
  editOneManagementEmployee,
} = require('../controllers/employeeController');

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];

// Key: employee
router.get('/get_all', middlewares, getAll);
router.post('/retrieve', middlewares, retrieveAll);
router.post('/retrieve_all_multi_filter', middlewares, retrieveAllMultiFilter);
router.get('/:id', middlewares, getOne);
router.post('/add', middlewares, addOne);
router.put('/:id', middlewares, updateOne);
router.get('/retrieve/group', middlewares, getEmployeeByGroup);
router.delete('/:id', middlewares, deleteOne);

router.get('/management/get_all', middlewares, getAllManagementEmployee);
router.post('/management/add', middlewares, addOneManagementEmployee);
router.put('/management/:id', middlewares, editOneManagementEmployee);

module.exports = router;
