const { sqlDateFormat, sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");
const PasswordService = require("../../../services/PasswordService");
const config = require("../../../config");
const postmark = require("postmark");
const stripe = require("stripe")(config.stripe.secret_key);
module.exports = class UserService {
  constructor() {
    this.sdk = null;
  }

  getSDK(req) {
    const sdk = req.sdk;
    sdk.getDatabase();
    sdk.setProjectId(req.projectId);
    return sdk;
  }

  jsonContainsBuilder(memberIds) {
    let memberIdsStr = "";
    if (memberIds.length === 1) {
      memberIdsStr = memberIds[0];
      return ` JSON_CONTAINS(cm.member_ids, '${memberIdsStr}')`;
    } else if (memberIds.length > 1) {
      let memberIdsArr = [];
      memberIds.forEach((m) => {
        memberIdsArr.push(` JSON_CONTAINS(cm.member_ids, '${m}')`);
      });
      return memberIdsArr.join(" OR ");
    }
  }

  async retrieveAll(req, page, limit) {
    try {
      this.sdk = this.getSDK(req);

      let filter = req.body.filter ?? {};
      // let filter = {
      //   id: 1,
      //   first_name: "John",
      //   last_name: "Doe",
      //   email: "<EMAIL>",
      //   role: 'admin',
      //   status: 1,
      //   company_name: "John Doe Company",
      // };
      let filterQuery = "";
      if (req.role !== "admin") {
        filterQuery += `WHERE u.user_id = ${req.user_id}`;
      }

      let countRawSql = `
        SELECT COUNT(*) AS total
        FROM equalityrecord_user u
        ${filterQuery}
      `;
      const countResult = await this.sdk.rawQuery(countRawSql);

      const total = countResult[0].total;
      const numPages = Math.ceil(total / limit);
      const offset = (page - 1) * limit;

      let filterArr = [];
      if (filterQuery && Object.keys(filter).length > 0) {
        filterQuery += " AND ";
      } else if (Object.keys(filter).length > 0) {
        filterQuery += " WHERE ";
      }

      if (Object.keys(filter).length > 0) {
        if (filter.user_id && req.role === "admin") {
          filterArr.push(` u.user_id = ${filter.id} `);
        }
        if (filter.first_name) {
          filterArr.push(` LOWER(u.first_name) LIKE '%${filter.first_name.toLowerCase()}%' `);
        }
        if (filter.last_name) {
          filterArr.push(` LOWER(u.last_name) LIKE '%${filter.last_name.toLowerCase()}%' `);
        }
        if (filter.email) {
          filterArr.push(` LOWER(u.email) LIKE '%${filter.email.toLowerCase()}%' `);
        }
        if (filter.status) {
          filterArr.push(` u.status = ${filter.status} `);
        }
        if (filter.company_name) {
          filterArr.push(`LOWER(p.company_name) LIKE '%${filter.company_name.toLowerCase()}%'`);
        }
        if (filter.subscription) {
          filterArr.push(` u.subscription = ${filter.subscription} `);
        }
        if (filter.role) {
          filterArr.push(` u.role = '${filter.role}' `);
        }
        if (filterArr.length > 0) {
          filterQuery += filterArr.join(" AND ");
        }
      }

      const result = await this.sdk.rawQuery(`
        SELECT u.*,
          p.company_name,
          p.subscription_id,
          p.plan_id
        FROM equalityrecord_user u
        LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
        ${filterQuery}
        ORDER BY u.id DESC
        LIMIT ${limit} OFFSET ${offset};
      `);

      if (result.length > 0) {
        return {
          list: result,
          num_pages: numPages,
          page: parseInt(page),
          total: total
        };
      } else {
        return {
          list: [],
          num_pages: 0,
          page: parseInt(page),
          total: 0
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getUserDetailsById(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT
          u.id, u.first_name, u.last_name, u.email, u.phone,
          u.photo, u.type, u.verify, u.status, u.role, u.subscription, u.steps, u.company_address, u.has_stripe, u.stripe_account_id,
          p.company_name, p.office_email, p.company_logo, p.license_company_logo,p.edit_policy_link ,
          p.deposit_percent, p.estimated_delivery, p.routine_submission_date, p.survey, p.contract_agreement, u.has_invoice_subscription
        FROM equalityrecord_user u
        LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
        LEFT JOIN equalityrecord_stripe_subscription s ON s.id = p.subscription_id
        WHERE u.id = ${req.params.id};
      `);

      // if role is memeber or manager, check stripe account

      let hasStripe = result[0].has_stripe == 1 ? true : false;
      let accountLink = null;

      if (!result[0].has_stripe && (result[0].role == "manager" || result[0].role == "member")) {
        if (result[0].stripe_account_id) {
          const account = await stripe.accounts.retrieve(result[0].stripe_account_id);

          if (account.details_submitted) {
            hasStripe = true;

            // update user with has_stripe
            this.sdk.setTable("user");
            await this.sdk.update(
              {
                has_stripe: true
              },
              result[0].id
            );
          } else {
            accountLink = await stripe.accountLinks.create({
              account: result[0].stripe_account_id,
              refresh_url: `https://equalitydev.manaknightdigital.com/stripe/refresh-onboarding`,
              return_url: `https://equalitydev.manaknightdigital.com/stripe/onboarding-complete`,
              type: "account_onboarding"
            });
          }
        } else {
          console.log("HEREEEEEE 4");

          const account = await stripe.accounts.create({
            type: "express",
            email: result[0].email,
            capabilities: {
              card_payments: { requested: true },
              transfers: { requested: true }
            },
            business_type: "individual",
            metadata: {
              userId: result[0].id,
              type: "user"
            }
          });

          accountLink = await stripe.accountLinks.create({
            account: account.id,
            refresh_url: `https://equalitydev.manaknightdigital.com/stripe/refresh-onboarding`,
            return_url: `https://equalitydev.manaknightdigital.com/stripe/onboarding-complete`,
            type: "account_onboarding"
          });

          // update user with stripe account id
          this.sdk.setTable("user");
          await this.sdk.update(
            {
              stripe_account_id: account.id
            },
            result[0].id
          );
        }
      }

      const customer = await this.sdk.rawQuery(`
        SELECT u.*, s.id as subId, p.id AS planId
        FROM equalityrecord_user AS u LEFT JOIN equalityrecord_stripe_subscription AS s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing') LEFT JOIN equalityrecord_stripe_price AS p ON s.price_id = p.id WHERE u.id = ${req.params.id} ;
      `);

      // Get main member details - the person who originally created the manager/company relationship
      let mainMemberDetails = await this.getMainMemberDetails(req.params.id);

      if (result.length > 0) {
        return {
          error: false,
          model: {
            ...result[0],
            subscription_id: customer[0].subId,
            plan_id: customer[0].planId,
            has_stripe: hasStripe,
            account_link: accountLink,
            main_user_details: mainMemberDetails
          }
        };
      } else {
        return {
          error: true,
          model: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getUserSubscription(req) {
    try {
      this.sdk = this.getSDK(req);

      const result = await this.sdk.rawQuery(`
        SELECT
          id, role, subscription, status
        FROM equalityrecord_user
        WHERE id = ${req.user_id};
      `);

      if (result.length > 0) {
        return {
          error: false,
          model: result[0]
        };
      } else {
        return {
          error: true,
          model: null
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async addOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("user");

      if (req.role !== "admin") {
        return {
          error: true,
          message: "You are not authorized to create user"
        };
      }

      let role = req.body.role;
      if (role === "admin" || role === "client") {
        return {
          error: true,
          message: "Role " + role + " cannot be created."
        };
      }

      const exists = await this.sdk.get({
        email: req.body.email
      });

      if (exists.length > 0) {
        return {
          error: true,
          message: "User already exists"
        };
      } else {
        const hashPassword = await PasswordService.hash(req.body.password);
        const result = await this.sdk.insert({
          email: req.body.email,
          password: hashPassword,
          role: role,
          verify: 1,
          status: 1,
          type: 0,
          first_name: req.body.first_name,
          last_name: req.body.last_name,
          subscription: role === "member" ? 1 : null,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        this.sdk.setTable("profile");
        await this.sdk.insert({
          user_id: result,
          company_name: req.body.company_name ?? null,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        return {
          error: false,
          message: "User created successfully",
          user_id: result
        };
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateOne(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("user");

      const exists = await this.sdk.get({
        id: req.params.id
      });

      if (exists.length > 0) {
        await this.sdk.update(
          filterEmptyFields({
            first_name: req.body.first_name,
            last_name: req.body.last_name,
            phone: req.body.phone,
            photo: req.body.photo,
            status: req.body.status,
            role: req.body.role,
            verify: req.body.verify,
            steps: req.body.steps,
            company_address: req.body.company_address,
            subscription: req.body.subscription,
            update_at: sqlDateTimeFormat(new Date())
          }),
          req.params.id
        );

        let company_name = req.body.company_name ?? null;
        let office_email = req.body.office_email ?? null;
        let company_logo = req.body.company_logo ?? null;
        let edit_policy_link = req.body.edit_policy_link ?? null;
        let license_company_logo = req.body.license_company_logo ?? null;
        let subscription_id = req.body.subscription_id ?? null;
        let plan_id = req.body.plan_id ?? null;
        let deposit_percent = req.body.deposit_percent ?? null;
        let estimated_delivery = req.body.estimated_delivery ?? null;
        let routine_submission_date = req.body.routine_submission_date ?? null;
        let survey = req.body.survey ?? null;
        let contract_agreement = req.body.contract_agreement ?? null;

        await this.sdk.rawQuery(`
          UPDATE equalityrecord_profile
          SET
            company_name = '${company_name}',
            office_email = '${office_email}',
            company_logo = '${company_logo}',
            edit_policy_link = '${edit_policy_link}',
            license_company_logo = '${license_company_logo}',
            subscription_id = '${subscription_id}',
            plan_id = '${plan_id}',
            deposit_percent = '${deposit_percent}',
            estimated_delivery = '${estimated_delivery}',
            routine_submission_date = '${routine_submission_date}',
            survey = '${survey}',
            contract_agreement = '${contract_agreement}'
          WHERE user_id = ${req.params.id};
        `);

        return {
          error: false,
          message: "User updated successfully"
        };
      } else {
        throw new Error("User not found");
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async deleteOne(req) {
    try {
      this.sdk = this.getSDK(req);

      let { email } = req.body;

      this.sdk.setTable("client");
      const existsClient = await this.sdk.get({
        email: email
      });

      if (existsClient.length > 0) {
        for (let i = 0; i < existsClient.length; i++) {
          await this.sdk.delete({}, existsClient[i].id);

          this.sdk.setTable("client_member");
          const existsClientMember = await this.sdk.get({
            client_id: existsClient[i].id
          });

          if (existsClientMember.length > 0) {
            await this.sdk.delete({}, existsClientMember[0].id);
          }
        }
      }

      this.sdk.setTable("user");
      const exists = await this.sdk.get({
        email: email
      });
      if (exists.length > 0) {
        await this.sdk.delete({}, exists[0].id);

        this.sdk.setTable("profile");
        const existsProfile = await this.sdk.get({
          user_id: exists[0].id
        });

        if (existsProfile.length > 0) {
          await this.sdk.delete({}, existsProfile[0].id);
        }

        return {
          error: false,
          message: "User deleted successfully"
        };
      } else {
        throw new Error("User not found");
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async uploadPhoto(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("user");

      const exists = await this.sdk.get({
        id: req.params.id
      });

      if (exists.length > 0) {
        await this.sdk.update(
          {
            photo: req.body.photo,
            update_at: sqlDateTimeFormat(new Date())
          },
          req.params.id
        );

        return {
          error: false,
          message: "Photo uploaded successfully"
        };
      } else {
        throw new Error("User not found");
      }
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllClientByMemberId(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the member is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.params.id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Query modification to include company shared clients
      let sqlQuery = "";

      if (isPartOfCompany) {
        // If member is part of a company, get both personal clients and company shared clients
        sqlQuery = `SELECT
          cm.*,
          c.name AS client_full_name, c.program AS client_program,
          c.position AS client_position, c.email AS client_email,
          c.phone AS client_phone
        FROM equalityrecord_client_member cm
          LEFT JOIN equalityrecord_client c ON c.id = cm.client_id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.params.id}')
          OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1)`;
      } else {
        // If member is not part of a company, get only personal clients
        sqlQuery = `SELECT
          cm.*,
          c.name AS client_full_name, c.program AS client_program,
          c.position AS client_position, c.email AS client_email,
          c.phone AS client_phone
        FROM equalityrecord_client_member cm
          LEFT JOIN equalityrecord_client c ON c.id = cm.client_id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.params.id}')`;
      }

      const result = await this.sdk.rawQuery(sqlQuery);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          // Add a flag to indicate if this is a company shared client
          result[i].is_company_shared = result[i].is_company_shared === 1;

          let memberIds = JSON.parse(result[i].member_ids);
          // console.log(memberIds);

          if (memberIds.length > 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id IN (${memberIds.join(",")});`);

            result[i].members = members;
          } else if (memberIds.length === 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id = ${memberIds[0]};`);

            result[i].members = members;
          }
        }
      }

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllClientsProject(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Query modification to include company shared clients
      let sqlQuery = "";

      if (isPartOfCompany) {
        // If user is part of a company, get both personal clients and company shared clients with projects
        sqlQuery = `SELECT
          cm.*,
          c.name AS client_full_name,
          c.program AS client_program,
          c.position AS client_position,
          c.email AS client_email,
          c.phone AS client_phone
        FROM 
          equalityrecord_client_member cm
        LEFT JOIN 
          equalityrecord_client c ON c.id = cm.client_id
        WHERE 
          (JSON_CONTAINS(cm.member_ids, '${req.user_id}') OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1))
          AND EXISTS (
            SELECT 1 
            FROM equalityrecord_project 
            WHERE equalityrecord_project.client_id = cm.client_id
          )`;
      } else {
        // If user is not part of a company, get only personal clients with projects
        sqlQuery = `SELECT
          cm.*,
          c.name AS client_full_name,
          c.program AS client_program,
          c.position AS client_position,
          c.email AS client_email,
          c.phone AS client_phone
        FROM 
          equalityrecord_client_member cm
        LEFT JOIN 
          equalityrecord_client c ON c.id = cm.client_id
        WHERE 
          JSON_CONTAINS(cm.member_ids, '${req.user_id}')
          AND EXISTS (
            SELECT 1 
            FROM equalityrecord_project 
            WHERE equalityrecord_project.client_id = cm.client_id
          )`;
      }

      const result = await this.sdk.rawQuery(sqlQuery);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          // Add a flag to indicate if this is a company shared client
          result[i].is_company_shared = result[i].is_company_shared === 1;

          let memberIds = JSON.parse(result[i].member_ids);
          // console.log(memberIds);

          if (memberIds.length > 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id IN (${memberIds.join(",")});`);

            result[i].members = members;
          } else if (memberIds.length === 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id = ${memberIds[0]};`);

            result[i].members = members;
          }
        }
      }

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllClients(req) {
    try {
      this.sdk = this.getSDK(req);

      // Return all clients in the system without filtering by user
      // This is to support the old system that expects all clients
      const result = await this.sdk.rawQuery(`
        SELECT
          cm.*,
          c.name AS client_full_name, c.program AS client_program,
          c.position AS client_position, c.email AS client_email,
          c.phone AS client_phone
        FROM equalityrecord_client_member cm
        LEFT JOIN equalityrecord_client c ON c.id = cm.client_id
      `);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          result[i].is_company_shared = result[i].is_company_shared === 1;

          let memberIds = JSON.parse(result[i].member_ids);

          if (memberIds.length > 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id IN (${memberIds.join(",")});`);

            result[i].members = members;
          } else if (memberIds.length === 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id = ${memberIds[0]};`);

            result[i].members = members;
          }
        }
      }

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async assignClientToMembersByClientUserId(req) {
    try {
      this.sdk = this.getSDK(req);

      const clientExists = await this.sdk.rawQuery(`
        SELECT id FROM equalityrecord_client
        WHERE id = ${req.params.id};`);

      if (clientExists.length === 0) {
        return {
          error: true,
          message: "Client not found"
        };
      }

      // Check if this client is already a company shared client
      const clientMemberExists = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_client_member
        WHERE client_id = ${req.params.id}`);

      const isCompanySharedClient = clientMemberExists.length > 0 && clientMemberExists[0].is_company_shared === 1;

      // If it's a company shared client, we need to determine if the request is from the company manager
      if (isCompanySharedClient) {
        const companyId = clientMemberExists[0].company_id;

        // If the request is not from a manager or admin, reject it
        // if (req.role !== "admin" && req.role !== "manager") {
        //   return {
        //     error: true,
        //     message: "Only managers or admins can modify company shared clients"
        //   };
        // }

        // // If the requester is a manager but not the company manager, reject it
        // if (req.role === "manager" && req.user_id != companyId) {
        //   return {
        //     error: true,
        //     message: "Only the company manager who created the shared client can modify it"
        //   };
        // }
      }

      let memberIds = req.body.member_ids;
      if (memberIds.length === 0) {
        this.sdk.setTable("client_member");
        const exists = await this.sdk.get({
          client_id: req.params.id
        });
        if (exists.length > 0) {
          await this.sdk.delete({}, exists[0].id);
          return {
            error: false,
            message: "Client unassigned from all members successfully."
          };
        } else {
          return {
            error: false,
            message: "Client was not assigned to any members."
          };
        }
      }

      // Check if the user is adding client to company members
      let isCompanyOperation = false;
      let companyManagerId = null;

      if (req.role === "manager") {
        // Get the company members this manager manages
        const companyInfo = await this.sdk.rawQuery(`
          SELECT mp.manager_id, mp.member_ids
          FROM equalityrecord_manager_permission mp
          WHERE mp.manager_id = ${req.user_id}
        `);

        if (companyInfo.length > 0) {
          isCompanyOperation = true;
          companyManagerId = req.user_id;

          // If this is a company operation, all company members should be included
          const companyMembers = JSON.parse(companyInfo[0].member_ids);

          // Add the manager as well
          memberIds = [...new Set([...memberIds, ...companyMembers, companyManagerId])];
        }
      }

      let memberIdsStr = JSON.stringify(memberIds);

      this.sdk.setTable("client_member");
      const exists = await this.sdk.get({
        client_id: req.params.id
      });

      if (exists.length > 0) {
        await this.sdk.update(
          {
            member_ids: memberIdsStr,
            company_id: isCompanyOperation ? companyManagerId : null,
            is_company_shared: isCompanyOperation ? 1 : 0,
            update_at: sqlDateTimeFormat(new Date())
          },
          exists[0].id
        );
      } else {
        await this.sdk.insert({
          client_id: req.params.id,
          member_ids: memberIdsStr,
          company_id: isCompanyOperation ? companyManagerId : null,
          is_company_shared: isCompanyOperation ? 1 : 0,
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
      }

      return {
        error: false,
        message: isCompanyOperation ? "Client assigned to company successfully" : "Client assigned to members successfully"
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getAllMembersByManagerId(req) {
    try {
      this.sdk = this.getSDK(req);

      if (req.role === "member" || req.role === "client") {
        return {
          error: true,
          message: "You are not authorized to access this resource"
        };
      }

      if (req.role === "manager" && Number(req.user_id) !== Number(req.params.id)) {
        return {
          error: true,
          message: "You are not authorized to access this resource"
        };
      }

      const managerMembers = await this.sdk.rawQuery(`
        SELECT
          mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE mp.manager_id = ${req.params.id};
      `);

      if (managerMembers.length === 0) {
        return {
          error: true,
          message: "Manager members not found"
        };
      }

      // member_ids will be in stringified json [1, 2, 4]

      let memberIds = managerMembers[0].member_ids;
      memberIds = JSON.parse(memberIds);

      let members = [];
      if (memberIds.length === 0) {
        return {
          error: false,
          list: []
        };
      }

      if (memberIds.length > 1) {
        members = await this.sdk.rawQuery(`
          SELECT
            u.id, u.first_name, u.last_name, u.email, u.phone,
            u.photo, u.type, u.verify, u.status, u.role, u.subscription,
            p.company_name, p.office_email, p.company_logo, p.license_company_logo,p.edit_policy_link 
          FROM equalityrecord_user u
          LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
          WHERE u.id IN (${memberIds.join(",")});
        `);
      } else if (memberIds.length === 1) {
        members = await this.sdk.rawQuery(`
          SELECT
            u.id, u.first_name, u.last_name, u.email, u.phone,
            u.photo, u.type, u.verify, u.status, u.role, u.subscription,
            p.company_name, p.office_email, p.company_logo, p.license_company_logo,p.edit_policy_link 
          FROM equalityrecord_user u
          LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
          WHERE u.id = ${memberIds[0]};
        `);
      }

      return {
        error: false,
        list: members.length > 0 ? members : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async createManagerWithPermissions(req) {
    try {
      this.sdk = this.getSDK(req);

      // First check if the member already has a manager
      const existingManagerPermissions = await this.sdk.rawQuery(`
        SELECT mp.*, u.email as manager_email, u.first_name, u.last_name
        FROM equalityrecord_manager_permission mp
        JOIN equalityrecord_user u ON u.id = mp.manager_id
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      if (existingManagerPermissions.length > 0) {
        const manager = existingManagerPermissions[0];
        return {
          error: true,
          message: `You already have a manager (${manager.first_name} ${manager.last_name} - ${manager.manager_email})`
        };
      }

      this.sdk.setTable("user");
      const { email } = req.body;

      // Check if user already exists
      const exists = await this.sdk.get({
        email: email
      });

      if (exists.length > 0) {
        // If user exists, use their ID instead of creating new user
        const existingUserId = exists[0].id;

        // Update the existing user's role to manager if needed
        if (exists[0].role !== "manager") {
          await this.sdk.update(
            {
              role: "manager",
              update_at: sqlDateTimeFormat(new Date())
            },
            {
              id: existingUserId
            }
          );
        }

        // Skip user creation and profile creation
        // Continue with creating manager permission
        this.sdk.setTable("manager_permission");
        await this.sdk.insert({
          manager_id: existingUserId,
          member_ids: JSON.stringify([req.user_id]),
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });

        // Transfer member's clients to the company shared clients
        await this.transferClientsToCompany(req.user_id);

        return {
          error: false,
          message: "Existing user assigned as manager successfully"
        };
      }
      // Generate a random password
      const password = Math.random().toString(36).substring(2, 15);
      const hashPassword = await PasswordService.hash(password);

      // Create the manager user
      const result = await this.sdk.insert({
        email: email,
        password: hashPassword,
        first_name: req.body.first_name || "",
        last_name: req.body.last_name || "",
        role: "manager",
        verify: 1,
        status: 1,
        type: 0,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Create profile
      this.sdk.setTable("profile");
      await this.sdk.insert({
        user_id: result,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Create manager permission
      this.sdk.setTable("manager_permission");
      await this.sdk.insert({
        manager_id: result,
        member_ids: JSON.stringify([req.user_id]),
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Transfer member's clients to the company shared clients
      await this.transferClientsToCompany(req.user_id);

      // Send email with credentials
      try {
        const client = new postmark.ServerClient(config.mail_pass);

        let emailResponse = await client.sendEmail({
          From: config.Email_From,
          To: email,
          Subject: "Your Manager Account Credentials",
          HtmlBody: `
            <h1>Welcome to Your Manager Account</h1>
            <p>Your account has been created successfully.</p>
            <p>Here are your login credentials:</p>
            <p>Email: ${email}</p>
            <p>Password: ${password}</p>
            <p>Please change your password after your first login.</p>
          `
        });
        console.log(emailResponse);
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
        // Continue execution even if email fails
      }

      return {
        error: false,
        message: "Manager account created successfully",
        user_id: result
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async transferClientsToCompany(userId) {
    try {
      // Get all the clients associated with this user
      this.sdk.setTable("client_member");
      const clientMembers = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_client_member
        WHERE JSON_CONTAINS(member_ids, '${userId}')
      `);

      if (clientMembers.length === 0) {
        // No clients to transfer
        return;
      }

      // Get the company info (managers and members)
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${userId}')
      `);

      if (companyInfo.length === 0) {
        // No company relationship found
        return;
      }

      const company = companyInfo[0];
      const managerId = company.manager_id;
      const memberIds = JSON.parse(company.member_ids);

      // For each client, update the client_member record to include all company members
      for (let clientMember of clientMembers) {
        const currentMemberIds = JSON.parse(clientMember.member_ids);

        // Create a set of unique member IDs to avoid duplicates
        const allMemberIds = new Set([...currentMemberIds, ...memberIds]);

        // Update the client_member record with all company members
        await this.sdk.update(
          {
            member_ids: JSON.stringify(Array.from(allMemberIds)),
            company_id: managerId, // Store the manager's ID as company_id
            is_company_shared: 1, // Flag to indicate this is a shared company client
            update_at: sqlDateTimeFormat(new Date())
          },
          clientMember.id
        );
      }
    } catch (error) {
      console.error("Error transferring clients to company:", error);
      throw new Error(error);
    }
  }

  async addMemberToCompany(req) {
    try {
      this.sdk = this.getSDK(req);

      // Validate that the requester is a manager
      if (req.role !== "manager" && req.role !== "admin") {
        return {
          error: true,
          message: "Only managers or admins can add members to a company"
        };
      }

      const { member_id } = req.body;

      // Verify the member exists
      this.sdk.setTable("user");
      const memberExists = await this.sdk.get({
        id: member_id
      });

      if (memberExists.length === 0) {
        return {
          error: true,
          message: "Member not found"
        };
      }

      // Check if the member is already part of a company
      const existingMembership = await this.sdk.rawQuery(`
        SELECT mp.manager_id, u.first_name, u.last_name, u.email
        FROM equalityrecord_manager_permission mp
        JOIN equalityrecord_user u ON u.id = mp.manager_id
        WHERE JSON_CONTAINS(mp.member_ids, '${member_id}')
      `);

      if (existingMembership.length > 0) {
        const manager = existingMembership[0];
        return {
          error: true,
          message: `This member is already part of a company managed by ${manager.first_name} ${manager.last_name} (${manager.email})`
        };
      }

      // Add the member to the manager's company
      this.sdk.setTable("manager_permission");
      const managerPermission = await this.sdk.get({
        manager_id: req.user_id
      });

      if (managerPermission.length === 0) {
        // Create a new manager permission entry
        await this.sdk.insert({
          manager_id: req.user_id,
          member_ids: JSON.stringify([member_id]),
          create_at: sqlDateFormat(new Date()),
          update_at: sqlDateTimeFormat(new Date())
        });
      } else {
        // Update existing manager permission
        const existingMemberIds = JSON.parse(managerPermission[0].member_ids);
        const updatedMemberIds = [...new Set([...existingMemberIds, member_id])];

        await this.sdk.update(
          {
            member_ids: JSON.stringify(updatedMemberIds),
            update_at: sqlDateTimeFormat(new Date())
          },
          managerPermission[0].id
        );
      }

      // Grant access to all company shared clients
      const companyClients = await this.sdk.rawQuery(`
        SELECT * FROM equalityrecord_client_member
        WHERE company_id = ${req.user_id} AND is_company_shared = 1
      `);

      for (let client of companyClients) {
        const currentMemberIds = JSON.parse(client.member_ids);
        if (!currentMemberIds.includes(member_id)) {
          const updatedMemberIds = [...currentMemberIds, member_id];

          await this.sdk.update(
            {
              member_ids: JSON.stringify(updatedMemberIds),
              update_at: sqlDateTimeFormat(new Date())
            },
            client.id
          );
        }
      }

      return {
        error: false,
        message: "Member added to company successfully and granted access to company clients"
      };
    } catch (error) {
      console.error("Error adding member to company:", error);
      throw new Error(error);
    }
  }

  async getCompanyInfo(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids, mp.create_at
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}') OR mp.manager_id = ${req.user_id}
      `);

      let company = null;
      let managerId = null;
      let memberIds = null;

      if (companyInfo.length === 0) {
        // return {
        //   error: false,
        //   is_company: false,
        //   message: "User is not part of a company",
        //   company: null
        // };
        managerId = req.user_id;
        memberIds = [req.user_id];
      } else {
        company = companyInfo[0];
        managerId = company.manager_id;
        memberIds = JSON.parse(company.member_ids);
      }

      // Determine main member (first member added to the company)
      const mainMemberId = memberIds.length > 0 ? memberIds[0] : null;

      // Get company manager details
      const managerDetails = await this.sdk.rawQuery(`
        SELECT 
          u.id, u.first_name, u.last_name, u.email, u.phone, u.photo,u.company_address,
          p.company_name, p.office_email, p.company_logo, p.license_company_logo, 
          p.edit_policy_link, p.number_of_lines, p.subscription_id, p.plan_id,
          p.deposit_percent, p.estimated_delivery, p.routine_submission_date, 
          p.survey, p.contract_agreement, u.has_invoice_subscription
        FROM equalityrecord_user u
        LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
        WHERE u.id = ${managerId}
      `);

      // Get all company members details
      let members = [];
      if (memberIds.length > 0) {
        members = await this.sdk.rawQuery(`
          SELECT 
            u.id, u.first_name, u.last_name, u.email, u.phone, u.photo,u.company_address,
            p.company_name, p.office_email, p.company_logo, p.license_company_logo, 
            p.edit_policy_link, p.number_of_lines, p.subscription_id, p.plan_id,
            p.deposit_percent, p.estimated_delivery, p.routine_submission_date, 
            p.survey, p.contract_agreement, u.has_invoice_subscription
          FROM equalityrecord_user u
          LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
          WHERE u.id IN (${memberIds.join(",")})
        `);
      }

      // Count shared clients
      const clientCount = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count
        FROM equalityrecord_client_member
        WHERE company_id = ${managerId} AND is_company_shared = 1
      `);

      const isManager = req.user_id == managerId;
      const isMainMember = req.user_id == mainMemberId;

      // Get main member details if exists
      let mainMember = null;
      if (mainMemberId) {
        const mainMemberDetails = await this.sdk.rawQuery(`
          SELECT 
            u.id, u.first_name, u.last_name, u.email, u.phone, u.photo,u.company_address,
            p.company_name, p.office_email, p.company_logo, p.license_company_logo, 
            p.edit_policy_link, p.number_of_lines, p.subscription_id, p.plan_id,
            p.deposit_percent, p.estimated_delivery, p.routine_submission_date, 
            p.survey, p.contract_agreement, u.has_invoice_subscription
          FROM equalityrecord_user u
          LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
          WHERE u.id = ${mainMemberId}
        `);

        if (mainMemberDetails.length > 0) {
          mainMember = mainMemberDetails[0];
        }
      }

      return {
        error: false,
        is_company: true,
        is_manager: isManager,
        is_main_member: isMainMember,
        company: {
          manager: managerDetails[0],
          members: members,
          main_member: mainMember,
          main_member_id: mainMemberId,
          shared_clients_count: clientCount[0].count,
          created_at: company ? company.create_at : managerDetails[0].create_at
        }
      };
    } catch (error) {
      console.error("Error getting company info:", error);
      throw new Error(error);
    }
  }

  async createCompanyMemberWithPayment(req) {
    try {
      this.sdk = this.getSDK(req);

      // Validate that the requester is a manager or main member
      const isMainMember = await this.isUserMainMember(req.user_id);

      // if (req.role !== "manager" && req.role !== "admin" && !isMainMember) {
      //   return {
      //     error: true,
      //     message: "Only main member can create company members"
      //   };
      // }

      // Check payment information
      const { payment_info, email, first_name, last_name, phone } = req.body;

      if (!payment_info || !payment_info.amount || payment_info.amount < 100) {
        return {
          error: true,
          message: "Payment of $100 is required to create a new company member"
        };
      }

      // Check if user already exists
      this.sdk.setTable("user");
      const exists = await this.sdk.get({
        email: email
      });

      if (exists.length > 0) {
        return {
          error: true,
          message: "User with this email already exists"
        };
      }

      const stripeService = new (require("../../../services/StripeService"))();

      // Create checkout session first to get the invoice ID for URLs
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.insert({
        user_id: req.user_id,
        total: payment_info.amount,
        payment_method: payment_info.method || "credit_card",
        status: "pending",
        member_data: JSON.stringify({
          email,
          first_name,
          last_name,
          phone
        }),
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      const checkoutSession = await stripeService.createCheckoutSession({
        line_items: [
          {
            price_data: {
              currency: "usd",
              product_data: {
                name: "Company Member Subscription"
              },
              unit_amount: payment_info.amount
            },
            quantity: 1
          }
        ],
        payment_method_types: ["card"],
        mode: "payment",
        success_url: `https://equalitydev.manaknightdigital.com/invoice/member-add-success?invoice_id=${invoice}`,
        cancel_url: `https://equalitydev.manaknightdigital.com/invoice/member-add-cancel?invoice_id=${invoice}`,
        metadata: {
          email,
          first_name,
          last_name,
          phone,
          user_id: req.user_id,
          handle_webhook_with: "confirmCompanyMemberPayment"
        }
      });

      // Update invoice with checkout session ID
      await this.sdk.update(
        {
          payment_intent_id: checkoutSession.id,
          update_at: sqlDateTimeFormat(new Date())
        },
        invoice
      );

      return {
        error: false,
        message: "Checkout session created successfully. Please confirm payment to create company member.",
        checkout_session_id: checkoutSession.id,
        stripe_checkout_url: checkoutSession.url
      };
    } catch (error) {
      console.error("Error creating payment intent for company member:", error);
      throw new Error(error);
    }
  }

  async confirmCompanyMemberPayment(req) {
    try {
      this.sdk = this.getSDK(req);

      // Validate checkout session
      // this.sdk.setTable("invoice");
      const invoice = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_invoice WHERE payment_intent_id = "${req.params.id}" AND user_id = ${req.user_id}`);

      if (invoice.length === 0) {
        return {
          error: true,
          message: "Checkout session not found or unauthorized"
        };
      }

      const intent = invoice[0];
      if (intent.status != "pending") {
        return {
          error: true,
          message: "Checkout session has already been processed"
        };
      }

      // Verify payment status with Stripe
      const stripeService = new (require("../../../services/StripeService"))();
      const checkoutSession = await stripeService.retrieveCheckoutSession(intent.payment_intent_id);

      if (!checkoutSession || checkoutSession.payment_status !== "paid") {
        return {
          error: true,
          message: "Payment has not been completed successfully"
        };
      }

      // Update invoice status
      await this.sdk.rawQuery(`UPDATE equalityrecord_invoice SET status = "paid", update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${intent.id}`);

      // Extract member data
      const memberData = JSON.parse(intent.member_data);
      const { email, first_name, last_name, phone } = memberData;

      // Generate a random password
      const password = Math.random().toString(36).substring(2, 15);
      const hashPassword = await PasswordService.hash(password);

      // this.sdk.setTable("user");
      const result = await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_user (email, password, first_name, last_name, phone, role, verify, status, type, subscription, create_at, update_at) VALUES ("${email}", "${hashPassword}", "${
          first_name || ""
        }", "${last_name || ""}", "${phone || ""}", "member", 1, 1, 0, 1, "${sqlDateFormat(new Date())}", "${sqlDateTimeFormat(new Date())}")`
      );

      // Get company info for the manager
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.*, p.company_name
        FROM equalityrecord_manager_permission mp
        JOIN equalityrecord_user u ON u.id = mp.manager_id
        JOIN equalityrecord_profile p ON p.user_id = mp.manager_id
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}') OR mp.manager_id = ${req.user_id}
      `);

      // Create profile with company details
      // this.sdk.setTable("profile");
      await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_profile (user_id, company_name, create_at, update_at) VALUES (${result.insertId}, "${
          companyInfo.length > 0 ? companyInfo[0].company_name : null
        }", "${sqlDateFormat(new Date())}", "${sqlDateTimeFormat(new Date())}")`
      );
      //   user_id: result,
      //   company_name: companyInfo.length > 0 ? companyInfo[0].company_name : null,
      //   create_at: sqlDateFormat(new Date()),
      //   update_at: sqlDateTimeFormat(new Date())
      // });

      // Add member to the company
      if (companyInfo.length > 0) {
        const memberIds = JSON.parse(companyInfo[0].member_ids || "[]");
        const updatedMemberIds = [...new Set([...memberIds, result.insertId])];

        await this.sdk.rawQuery(
          `UPDATE equalityrecord_manager_permission SET member_ids = "${JSON.stringify(updatedMemberIds)}", update_at = "${sqlDateTimeFormat(
            new Date()
          )}" WHERE id = ${companyInfo[0].id}`
        );

        // Grant access to all company shared clients
        const companyClients = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_client_member
          WHERE company_id = ${req.user_id} AND is_company_shared = 1
        `);

        for (let client of companyClients) {
          const currentMemberIds = JSON.parse(client.member_ids);
          const updatedClientMemberIds = [...currentMemberIds, result];

          await this.sdk.rawQuery(
            `UPDATE equalityrecord_client_member SET member_ids = "${JSON.stringify(updatedClientMemberIds)}", update_at = "${sqlDateTimeFormat(
              new Date()
            )}" WHERE id = ${client.id}`
          );
        }
      } else {
        // Create a new company
        await this.sdk.rawQuery(
          `INSERT INTO equalityrecord_manager_permission (manager_id, member_ids, create_at, update_at) VALUES (${req.user_id}, "${JSON.stringify([
            result.insertId
          ])}", "${sqlDateFormat(new Date())}", "${sqlDateTimeFormat(new Date())}")`
        );

        // Grant access to all company shared clients
        const companyClients = await this.sdk.rawQuery(`
          SELECT * FROM equalityrecord_client_member
          WHERE company_id = ${req.user_id} AND is_company_shared = 1
        `);

        for (let client of companyClients) {
          const currentMemberIds = JSON.parse(client.member_ids);
          const updatedClientMemberIds = [...currentMemberIds, result.insertId];

          await this.sdk.rawQuery(
            `UPDATE equalityrecord_client_member SET member_ids = "${JSON.stringify(updatedClientMemberIds)}", update_at = "${sqlDateTimeFormat(
              new Date()
            )}" WHERE id = ${client.id}`
          );
        }
      }

      // Send email with credentials
      try {
        const client = new postmark.ServerClient(config.mail_pass);

        let emailResponse = await client.sendEmail({
          From: config.Email_From,
          To: email,
          Subject: "Your Company Member Account Credentials",
          HtmlBody: `
            <h1>Welcome to Your Company Member Account</h1>
            <p>Your account has been created successfully as part of the company.</p>
            <p>Here are your login credentials:</p>
            <p>Email: ${email}</p>
            <p>Password: ${password}</p>
            <p>Please change your password after your first login.</p>
          `
        });
        console.log(emailResponse);
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
        // Continue execution even if email fails
      }

      // Log the payment
      await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_payment_log (user_id, amount, payment_method, payment_status, payment_description, payment_intent_id, create_at, update_at) VALUES (${
          req.user_id
        }, ${intent.total}, "${intent.payment_method}", "completed", "Invoice subscription activation payment", "${intent.payment_intent_id}", "${sqlDateFormat(
          new Date()
        )}", "${sqlDateTimeFormat(new Date())}")`
      );
      //   user_id: req.user_id,
      //   amount: intent.total,
      //   payment_method: intent.payment_method,
      //   payment_status: "completed",
      //   payment_description: "Invoice subscription activation payment",
      //   payment_intent_id: intent.payment_intent_id,
      //   create_at: sqlDateFormat(new Date()),
      //   update_

      return {
        error: false,
        message: "Invoice subscription activated successfully",
        has_invoice_subscription: true
      };
    } catch (error) {
      console.error("Error confirming invoice subscription payment:", error);
      throw new Error(error);
    }
  }

  async isUserMainMember(userId) {
    try {
      // Check if user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.* 
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${userId}')
      `);

      if (companyInfo.length > 0) {
        // User is part of a company, check if they are the first member
        const memberIds = JSON.parse(companyInfo[0].member_ids);
        // Main member is typically the first member in the company
        return memberIds[0] == userId;
      }

      return false;
    } catch (error) {
      console.error("Error checking if user is main member:", error);
      return false;
    }
  }

  async getMainMemberDetails(userId) {
    try {
      return await module.exports.getMainMemberDetails(this.sdk, userId);
    } catch (error) {
      console.error("Error getting main member details:", error);
      return null;
    }
  }

  async createInvoiceSubscriptionPayment(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if user already has invoice subscription
      this.sdk.setTable("user");
      const user = await this.sdk.get({
        id: req.user_id
      });

      if (user.length === 0) {
        return {
          error: true,
          message: "User not found"
        };
      }

      if (user[0].has_invoice_subscription === 1) {
        return {
          error: true,
          message: "User already has an invoice subscription"
        };
      }

      // Fixed amount for invoice subscription
      const amount = 40000; // $400 in cents

      // Create checkout session first to get the invoice ID for URLs
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.insert({
        user_id: req.user_id,
        total: amount,
        payment_method: "credit_card",
        status: "pending",
        payment_type: "invoice_subscription",
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });

      // Create checkout session
      const stripeService = new (require("../../../services/StripeService"))();
      const checkoutSession = await stripeService.createCheckoutSession({
        line_items: [
          {
            price_data: {
              currency: "usd",
              product_data: {
                name: "Invoice Subscription"
              },
              unit_amount: amount
            },
            quantity: 1
          }
        ],
        payment_method_types: ["card"],
        mode: "payment",
        success_url: `https://equalitydev.manaknightdigital.com/invoice/invoice-success?invoice_id=${invoice}`,
        cancel_url: `https://equalitydev.manaknightdigital.com/invoice/invoice-cancel?invoice_id=${invoice}`,
        metadata: {
          user_id: req.user_id,
          payment_type: "invoice_subscription",
          handle_webhook_with: "confirmInvoiceSubscriptionPayment"
        }
      });

      // Update invoice with checkout session ID
      await this.sdk.update(
        {
          payment_intent_id: checkoutSession.id,
          update_at: sqlDateTimeFormat(new Date())
        },
        invoice
      );

      return {
        error: false,
        message: "Checkout session created successfully. Please confirm payment to activate invoice subscription.",
        checkout_session_id: checkoutSession.id,
        stripe_checkout_url: checkoutSession.url
      };
    } catch (error) {
      console.error("Error creating invoice subscription payment:", error);
      throw new Error(error);
    }
  }

  async confirmInvoiceSubscriptionPayment(req) {
    try {
      this.sdk = this.getSDK(req);

      // Validate checkout session
      this.sdk.setTable("invoice");
      const invoice = await this.sdk.rawQuery(`SELECT * FROM equalityrecord_invoice WHERE payment_intent_id = "${req.params.id}" AND user_id = ${req.user_id}`);

      if (invoice.length === 0) {
        return {
          error: true,
          message: "Checkout session not found or unauthorized"
        };
      }

      const intent = invoice[0];
      if (intent.status === "paid" || intent.status === "completed") {
        return {
          error: true,
          message: "Checkout session has already been processed"
        };
      }

      // Verify payment status with Stripe
      const stripeService = new (require("../../../services/StripeService"))();
      const checkoutSession = await stripeService.retrieveCheckoutSession(intent.payment_intent_id);

      if (!checkoutSession || checkoutSession.payment_status !== "paid") {
        return {
          error: true,
          message: "Payment has not been completed successfully"
        };
      }

      // Update invoice status
      await this.sdk.rawQuery(`UPDATE equalityrecord_invoice SET status = "completed", update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${intent.id}`);

      // Update user has_invoice_subscription field
      await this.sdk.rawQuery(
        `UPDATE equalityrecord_user SET has_invoice_subscription = 1, update_at = "${sqlDateTimeFormat(new Date())}" WHERE id = ${req.user_id}`
      );

      // Log the payment
      await this.sdk.rawQuery(
        `INSERT INTO equalityrecord_payment_log (user_id, amount, payment_method, payment_status, payment_description, payment_intent_id, create_at, update_at) VALUES (${
          req.user_id
        }, ${intent.total}, "${intent.payment_method}", "completed", "Invoice subscription activation payment", "${intent.payment_intent_id}", "${sqlDateFormat(
          new Date()
        )}", "${sqlDateTimeFormat(new Date())}")`
      );

      return {
        error: false,
        message: "Invoice subscription activated successfully",
        has_invoice_subscription: true
      };
    } catch (error) {
      console.error("Error confirming invoice subscription payment:", error);
      throw new Error(error);
    }
  }

  // fetch all legal documents
  async fetchAllLegalDocuments(req) {
    try {
      this.sdk = this.getSDK(req);
      this.sdk.setTable("legal_document");
      const legalDocuments = await this.sdk.get();
      return {
        error: false,
        message: "Legal documents fetched successfully",
        data: legalDocuments
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  async getUserClients(req) {
    try {
      this.sdk = this.getSDK(req);

      // Check if the user is part of a company
      const companyInfo = await this.sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${req.user_id}')
      `);

      let isPartOfCompany = companyInfo.length > 0;
      let companyManagerId = isPartOfCompany ? companyInfo[0].manager_id : null;

      // Query modification to include company shared clients
      let sqlQuery = "";

      if (isPartOfCompany) {
        // If user is part of a company, get both personal clients and company shared clients
        sqlQuery = `SELECT
          cm.*,
          c.name AS client_full_name, c.program AS client_program,
          c.position AS client_position, c.email AS client_email,
          c.phone AS client_phone
        FROM equalityrecord_client_member cm
          LEFT JOIN equalityrecord_client c ON c.id = cm.client_id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}') 
          OR (cm.company_id = ${companyManagerId} AND cm.is_company_shared = 1)`;
      } else {
        // If user is not part of a company, get only personal clients
        sqlQuery = `SELECT
          cm.*,
          c.name AS client_full_name, c.program AS client_program,
          c.position AS client_position, c.email AS client_email,
          c.phone AS client_phone
        FROM equalityrecord_client_member cm
          LEFT JOIN equalityrecord_client c ON c.id = cm.client_id
        WHERE JSON_CONTAINS(cm.member_ids, '${req.user_id}')`;
      }

      const result = await this.sdk.rawQuery(sqlQuery);

      if (result.length > 0) {
        for (let i = 0; i < result.length; i++) {
          // Add a flag to indicate if this is a company shared client
          result[i].is_company_shared = result[i].is_company_shared === 1;

          let memberIds = JSON.parse(result[i].member_ids);
          // console.log(memberIds);

          if (memberIds.length > 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id IN (${memberIds.join(",")});`);

            result[i].members = members;
          } else if (memberIds.length === 1) {
            let members = await this.sdk.rawQuery(`SELECT
              u.id,
              CONCAT(u.first_name, ' ', u.last_name) AS full_name
            FROM equalityrecord_user u
              LEFT JOIN equalityrecord_profile p ON p.user_id = u.id
            WHERE u.id = ${memberIds[0]};`);

            result[i].members = members;
          }
        }
      }

      return {
        error: false,
        list: result.length > 0 ? result : []
      };
    } catch (error) {
      throw new Error(error);
    }
  }

  /**
   * Static utility method to check if a user is a main member or manager
   * This can be used across different services to avoid code duplication
   * @param {Object} sdk - Database SDK instance
   * @param {number} userId - User ID to check
   * @param {string} userRole - User role ('admin', 'manager', 'member', etc.)
   * @returns {Object} - {isMainMember: boolean, isManager: boolean, canPerformAction: boolean}
   */
  static async checkMainMemberOrManagerPermissions(sdk, userId, userRole) {
    try {
      // Admin can always perform actions
      if (userRole === "admin") {
        return {
          isMainMember: false,
          isManager: false,
          canPerformAction: true,
          reason: "Admin privileges"
        };
      }

      // Check if the user is a manager
      const isManager = userRole === "manager";

      // If not a manager, check if they are a main member (part of a company but the first member)
      let isMainMember = false;

      if (!isManager && userRole === "member") {
        // Check if user is part of a company
        const companyInfo = await sdk.rawQuery(`
          SELECT mp.* 
          FROM equalityrecord_manager_permission mp
          WHERE JSON_CONTAINS(mp.member_ids, '${userId}')
        `);

        if (companyInfo.length > 0) {
          // User is part of a company, check if they are the first member
          const memberIds = JSON.parse(companyInfo[0].member_ids);
          // Main member is typically the first member in the company
          isMainMember = memberIds[0] == userId;
        }
      }

      const canPerformAction = isManager || isMainMember;
      const reason = isManager ? "Manager privileges" : isMainMember ? "Main member privileges" : "No permissions";

      return {
        isMainMember,
        isManager,
        canPerformAction,
        reason
      };
    } catch (error) {
      console.error("Error checking main member or manager permissions:", error);
      return {
        isMainMember: false,
        isManager: false,
        canPerformAction: false,
        reason: "Error checking permissions"
      };
    }
  }

  /**
   * Static utility method to get main member details for any user
   * @param {Object} sdk - Database SDK instance
   * @param {number} userId - User ID to get main member details for
   * @returns {Object|null} - Main member details or null
   */
  static async getMainMemberDetails(sdk, userId) {
    try {
      // Check if the user is part of a company
      const companyInfo = await sdk.rawQuery(`
        SELECT mp.manager_id, mp.member_ids
        FROM equalityrecord_manager_permission mp
        WHERE JSON_CONTAINS(mp.member_ids, '${userId}') OR mp.manager_id = ${userId}
      `);

      if (companyInfo.length === 0) {
        // User is not part of a company, they are the main member themselves
        const userDetails = await sdk.rawQuery(`
          SELECT u.has_invoice_subscription, u.subscription, s.id as subId, p.id AS planId
          FROM equalityrecord_user u
          LEFT JOIN equalityrecord_stripe_subscription s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing')
          LEFT JOIN equalityrecord_stripe_price p ON s.price_id = p.id
          WHERE u.id = ${userId}
        `);

        if (userDetails.length > 0) {
          return {
            has_invoice_subscription: userDetails[0].has_invoice_subscription,
            plan_id: userDetails[0].planId,
            subscription_id: userDetails[0].subId,
            is_self: true
          };
        }
        return null;
      }

      const company = companyInfo[0];
      const memberIds = JSON.parse(company.member_ids);

      // Determine main member (first member added to the company)
      const mainMemberId = memberIds.length > 0 ? memberIds[0] : null;

      if (!mainMemberId) {
        return null;
      }

      // Get main member details with subscription information using the same approach as lines 210-212
      const mainMemberDetails = await sdk.rawQuery(`
        SELECT 
          u.id, u.has_invoice_subscription, u.subscription, s.id as subId, p.id AS planId
        FROM equalityrecord_user u
        LEFT JOIN equalityrecord_stripe_subscription s ON s.user_id = u.id AND (s.status = 'active' OR s.status = 'trialing')
        LEFT JOIN equalityrecord_stripe_price p ON s.price_id = p.id
        WHERE u.id = ${mainMemberId}
      `);

      if (mainMemberDetails.length > 0) {
        return {
          id: mainMemberDetails[0].id,
          has_invoice_subscription: mainMemberDetails[0].has_invoice_subscription,
          plan_id: mainMemberDetails[0].planId,
          subscription_id: mainMemberDetails[0].subId,
          is_self: mainMemberId == userId
        };
      }

      return null;
    } catch (error) {
      console.error("Error getting main member details:", error);
      return null;
    }
  }
};
