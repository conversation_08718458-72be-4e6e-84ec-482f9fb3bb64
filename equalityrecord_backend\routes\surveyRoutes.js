const router = require("express").Router();
const ProjectMiddleware = require("../../../middleware/ProjectMiddleware");
const UrlMiddleware = require("../../../middleware/UrlMiddleware");
const HostMiddleware = require("../../../middleware/HostMiddleware");
const TokenMiddleware = require("../../../middleware/TokenMiddleware");
const {
  getAll,
  add,
  updateOne,
  view,
  getDetails,
  getSurveyEmailTemplate,
  updateSurveyEmailTemplate,
  getAllSurveyNotificationsByProjectId,
  updateSurveyNotification,
  updateOneThemeOfTheRoutine,
  updateOneLockDate,
  updateOneFromClient,
} = require("../controllers/surveyController");

const middlewares = [
  ProjectMiddleware,
  UrlMiddleware,
  HostMiddleware,
  TokenMiddleware(),
];
const public = [ProjectMiddleware, UrlMiddleware, HostMiddleware];

// Key: survey
router.get("/", middlewares, getAll);
router.post("/", middlewares, add);
router.put("/update", public, updateOne);
router.put("/update_from_client", public, updateOneFromClient);
router.put("/update/lock_date/:id", public, updateOneLockDate);
router.put("/update/theme/:id", public, updateOneThemeOfTheRoutine);
router.get("/view/:id", middlewares, view);
router.post("/details", public, getDetails);

router.get("/email_template", middlewares, getSurveyEmailTemplate);
router.put("/email_template/:id", middlewares, updateSurveyEmailTemplate);

router.get(
  "/notification/:project_id",
  middlewares,
  getAllSurveyNotificationsByProjectId
);
router.put("/notification/:id", middlewares, updateSurveyNotification);

module.exports = router;
