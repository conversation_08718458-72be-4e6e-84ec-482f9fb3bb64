const { validateObject } = require('../../../services/ValidationService');
const EmployeeService = require('../services/EmployeeService');

exports.retrieveAll = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new EmployeeService();
    const result = await service.retrieveAll(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.retrieveAllMultiFilter = async (req, res) => {
  try {
    const page = req.body.page ?? 1;
    const limit = req.body.limit ?? 10;
    const service = new EmployeeService();
    const result = await service.retrieveAllMultiFilter(req, page, limit);
    return res.status(200).json({
      error: false,
      list: result.list,
      num_pages: result.num_pages,
      page: result.page,
      total: result.total,
    });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getAll = async (req, res) => {
  try {
    const service = new EmployeeService();
    const result = await service.getAll(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getAllManagementEmployee = async (req, res) => {
  try {
    const service = new EmployeeService();
    const result = await service.getAllManagementEmployee(req);
    return res.status(200).json({ error: false, list: result.list });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: 'required',
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new EmployeeService();
    const result = await service.getOne(req);
    return res.status(200).json({ error: result.error, model: result.model });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.addOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        name: 'required',
        email: 'required',
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new EmployeeService();
    const result = await service.addOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message, id: result.id });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.addOneManagementEmployee = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        employee_id: 'required',
        type: 'required',
        value: 'required',
      },
      req.body
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new EmployeeService();
    const result = await service.addOneManagementEmployee(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.editOneManagementEmployee = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: 'required',
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new EmployeeService();
    const result = await service.editOneManagementEmployee(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.updateOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: 'required',
      },
      req.params
    );
    if (validation.error) return res.status(403).json(validation);
    const service = new EmployeeService();
    const result = await service.updateOne(req);
    return res
      .status(200)
      .json({ error: result.error, message: result.message });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.getEmployeeByGroup = async (req, res) => {
  try {
    const service = new EmployeeService();
    const result = await service.getEmployeeByGroup(req);
    return res.status(200).json({ error: false, list: result });
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};

exports.deleteOne = async (req, res) => {
  try {
    const validation = await validateObject(
      {
        id: 'required',
      },
      req.params
    );

    if (validation.error) return res.status(403).json(validation);

    const service = new EmployeeService();
    const result = await service.deleteOne(req);

    if (!result.error) {
      return res.status(200).json({ error: false, message: result.message });
    } else {
      return res.status(200).json({ error: true, message: result.message });
    }
  } catch (err) {
    console.error(err);
    res.status(404);
    res.json({
      message: err.message,
    });
  }
};
