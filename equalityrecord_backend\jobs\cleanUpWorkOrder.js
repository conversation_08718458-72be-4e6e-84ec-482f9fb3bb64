const WorkOrderService = require("../services/WorkOrderService");
const { sqlDateTimeFormat, filterEmptyFields } = require("../../../services/UtilService");

async function cleanUpWorkOrder(sdk, options) {
  try {
    console.log("Starting work order cleanup job...");

    // Create a request object with SDK for the service
    const req = {
      sdk: sdk,
      projectId: options.projectId,
      role: "admin" // Job runs with admin privileges
    };

    // Create service instance
    const workOrderService = new WorkOrderService();

    // Find and delete work orders with no subprojects
    const result = await workOrderService.deleteWorkOrdersWithNoSubProjects(req);

    // Log results
    if (result.count > 0) {
      console.log(`Cleanup job completed: Deleted ${result.count} work orders with no sub-projects`);
      console.log(`Deleted work order IDs: ${result.deleted_ids.join(", ")}`);
    } else {
      console.log("Cleanup job completed: No work orders without sub-projects found");
    }

    return {
      success: true,
      message: result.message,
      count: result.count,
      deleted_ids: result.deleted_ids || []
    };
  } catch (error) {
    console.error("Error in work order cleanup job:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = cleanUpWorkOrder;
